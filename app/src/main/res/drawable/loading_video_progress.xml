<?xml version="1.0" encoding="utf-8"?>
<animated-rotate
        xmlns:android="http://schemas.android.com/apk/res/android"
    android:pivotX="50%" android:pivotY="50%"
    android:fromDegrees="0"
    android:toDegrees="360">
        <bitmap android:src="@drawable/loading1"/>
        <!--<shape
        android:shape="ring"
        android:innerRadiusRatio="3"
        android:thicknessRatio="8"
        android:useLevel="false">
        <gradient
            android:type="sweep"
            android:useLevel="false"
            android:startColor="#6BD3FF"
            android:centerColor="#FF7121"
            android:centerY="0.50"
            android:endColor="#FFFF00" />
        </shape>-->
</animated-rotate>
