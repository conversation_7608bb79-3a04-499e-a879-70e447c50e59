package com.videomontage.freeeditingapps.application;

import android.content.Context;
import android.content.pm.PackageManager;

import androidx.multidex.MultiDexApplication;
import android.util.DisplayMetrics;
import android.util.Log;

import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.gson.Gson;
import com.videomontage.freeeditingapps.BuildConfig;
import timber.log.Timber;
import com.videomontage.freeeditingapps.analytics.AnalyticsSharedPref;
import com.videomontage.freeeditingapps.analytics.AnalyticsVolley;
import com.videomontage.freeeditingapps.analytics.FragmentAnalytics;
import com.videomontage.freeeditingapps.analytics.UserSessionHelper;
//import com.videomontage.freeeditingapps.data.usecase.GetStickers;

import java.util.HashMap;



public class MyApplication extends MultiDexApplication {
    public static HashMap<Integer, FragmentAnalytics> ANALYTICS = new HashMap<>();
    private static int counterEvent;
    private static MyApplication mySelf;

    public static int screenWidth;
    public static int screenHeight;

    public static MyApplication self() {
        return mySelf;
    }


    public void onCreate() {
        super.onCreate();
        mySelf = this;

        // Initialize Timber for debug builds
        if (BuildConfig.DEBUG) {
            Timber.plant(new Timber.DebugTree());
        }

//        GetStickers getStickers = new GetStickers();
//        getStickers.getStickers(this);

        DisplayMetrics mDisplayMetrics = getApplicationContext().getResources()
                .getDisplayMetrics();
        screenWidth = mDisplayMetrics.widthPixels;
        screenHeight = mDisplayMetrics.heightPixels;

//        Fabric.with(this, new Crashlytics());
        FirebaseAnalytics.getInstance(this);
//        Log.d("proba", "onCreate: " + isInstallFromUpdate());

    }

    public static void addEvent(FragmentAnalytics fragmentAnalytics){
        ANALYTICS.put(++counterEvent, fragmentAnalytics);
        Gson gson = new Gson();
        String json = gson.toJson(MyApplication.ANALYTICS);
        AnalyticsSharedPref.setPreference(self(), json);

    }

    public static Context getAppContext() {
        return mySelf;
    }

    public  boolean isInstallFromUpdate() {
        try {
            long firstInstallTime =   mySelf.getPackageManager().getPackageInfo(getPackageName(), 0).firstInstallTime;
            long lastUpdateTime = mySelf.getPackageManager().getPackageInfo(getPackageName(), 0).lastUpdateTime;
            return firstInstallTime != lastUpdateTime;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            return false;
        }
    }


    protected static UserSessionHelper.OnUserSessionEnd mUserSessionEnded = new UserSessionHelper.OnUserSessionEnd() {

        @Override
        public void onSessionEnd(long sessionDurationinMillis) {

            AnalyticsVolley.sendAnalytics(self());

            if (BuildConfig.DEBUG) {
                Log.d("dddd", "Session ended in " + (sessionDurationinMillis / 1000) + " seconds.");
            }
        }
    };



    public static void onActivityResumed() {
        sessionHelper.onActivityResumed();
    }

    public static void onActivityPaused() {
        sessionHelper.onActivityPaused();
    }

    protected static UserSessionHelper sessionHelper = new UserSessionHelper(mUserSessionEnded);
}