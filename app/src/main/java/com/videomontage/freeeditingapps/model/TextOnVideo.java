package com.videomontage.freeeditingapps.model;

public class TextOnVideo {

    private String text;
    private long startTime;
    private long endTime;
    private int xPosition;
    private int yPosition;
    private int color;


    public TextOnVideo() {
    }

    public TextOnVideo(String text, long startTime, long endTime, int xPosition, int yPosition, int color) {
        this.text = text;
        this.startTime = startTime;
        this.endTime = endTime;
        this.xPosition = xPosition;
        this.yPosition = yPosition;
        this.color = color;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public int getxPosition() {
        return xPosition;
    }

    public void setxPosition(int xPosition) {
        this.xPosition = xPosition;
    }

    public int getyPosition() {
        return yPosition;
    }

    public void setyPosition(int yPosition) {
        this.yPosition = yPosition;
    }

    public int getColor() {
        return color;
    }

    public void setColor(int color) {
        this.color = color;
    }
}
