package com.videomontage.freeeditingapps.model;

public class CommandModel {

    private String[] command;
    private String fileNamePreview, previewImagePath, effectName;

    public CommandModel(String[] command, String fileNamePreview, String previewImagePath, String effectName) {
        this.command = command;
        this.fileNamePreview = fileNamePreview;
        this.previewImagePath = previewImagePath;
        this.effectName = effectName;
    }


    public String[] getCommand() {
        return command;
    }

    public void setCommand(String[] command) {
        this.command = command;
    }

    public String getFileNamePreview() {
        return fileNamePreview;
    }

    public void setFileNamePreview(String fileNamePreview) {
        this.fileNamePreview = fileNamePreview;
    }

    public String getPreviewImagePath() {
        return previewImagePath;
    }

    public void setPreviewImagePath(String previewImagePath) {
        this.previewImagePath = previewImagePath;
    }

    public String getEffectName() {
        return effectName;
    }

    public void setEffectName(String effectName) {
        this.effectName = effectName;
    }
}
