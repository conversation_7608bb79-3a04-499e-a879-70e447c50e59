package com.videomontage.freeeditingapps.model;

import android.media.MediaMetadataRetriever;
import android.os.Build;

import com.videomontage.freeeditingapps.R;
import com.videomontage.freeeditingapps.utils.VideoUtil;

import java.io.IOException;
import java.util.ArrayList;

public class ComModel {

    private String effectName;
    private int drawable;
    private boolean previewAvailable;

    public ComModel(String effectName, int drawable) {
        this.effectName = effectName;
        this.drawable = drawable;
    }

    public boolean isPreviewAvailable() {
        return previewAvailable;
    }

    public void setPreviewAvailable(boolean previewAvailable) {
        this.previewAvailable = previewAvailable;
    }

    public int getDrawable() {
        return drawable;
    }

    public void setDrawable(int drawable) {
        this.drawable = drawable;
    }

    public String getEffectName() {
        return effectName;
    }

    public void setEffectName(String effectName) {
        this.effectName = effectName;
    }

    public static <T extends Object> T getThresholdCommand(String inputPath, String outputPath, String thresholdColor, String minThresholdColor, String maxThresholdColor) {
//        ffmpeg -i ss_cut.mp4 -f lavfi -i color=gray:s=1920x1080 -f lavfi -i color=white:s=1920x1080 -f lavfi -i color=black:s=1920x1080 -filter_complex threshold output.mp4

        if (isNewApiRequired())
            return (T) ("-i " + inputPath + " -f lavfi -i color=" + thresholdColor + ":s=1920x1080 -f lavfi -i color=" + minThresholdColor + ":s=1920x1080 -f lavfi -i color=" + maxThresholdColor + ":s=1920x1080 -filter_complex \"threshold\" " + outputPath);

        else
            return (T) new String[]{"-i", inputPath, "-f", "lavfi", "-i", "color=" + thresholdColor + ":s=1920x1080", "-f", "lavfi", "-i", "color=" + minThresholdColor + ":s=1920x1080", "-f", "lavfi", "-i", "color=" + maxThresholdColor + ":s=1920x1080", "-filter_complex", "threshold", outputPath};

    }

    public static <T extends Object> T getFadeOutCommand(String inputPath, String outputPath, long videoDuration, int videoFadeInDuration, int audioFadeInDuration) {
        String[] command = null;
        String commandNewApi = null;

        if (isNewApiRequired()) {
            if (VideoUtil.isVideoHaveAudioTrack(inputPath))
                commandNewApi = "-i " + inputPath + " -preset ultrafast -filter_complex \"fade=out:st=" + (videoDuration - videoFadeInDuration) + ":d=" + videoFadeInDuration + "; afade=out:st=" + (videoDuration - audioFadeInDuration) + ":d=" + audioFadeInDuration + "\" -c:v libx264 -c:a aac " + outputPath;
            else
                commandNewApi = "-i " + inputPath + " -preset ultrafast -filter_complex \"fade=out:st=" + (videoDuration - videoFadeInDuration) + ":d=" + videoFadeInDuration + "\" -c:v libx264 " + outputPath;

        } else {
            if (VideoUtil.isVideoHaveAudioTrack(inputPath))
                command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-filter_complex", "fade=out:st=" + (videoDuration - videoFadeInDuration) + ":d=" + videoFadeInDuration + "; afade=out:st=" + (videoDuration - audioFadeInDuration) + ":d=" + audioFadeInDuration, "-c:v", "libx264", "-c:a", "aac", outputPath};
            else
                command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-filter_complex", "fade=out:st=" + (videoDuration - videoFadeInDuration) + ":d=" + videoFadeInDuration, "-c:v", "libx264", outputPath};

        }

        return isNewApiRequired() ? (T) commandNewApi : (T) command;
    }

    public static <T extends Object> T getFadeInCommand(String inputPath, String outputPath, int videoFadeInDuration, int audioFadeInDuration) {
        String[] command = null;
        String commandNewApi = null;

        if (isNewApiRequired()) {
            if (VideoUtil.isVideoHaveAudioTrack(inputPath))
                commandNewApi = "-i " + inputPath + " -preset ultrafast -filter_complex \"fade=in:st=0:d=" + videoFadeInDuration + "; afade=in:st=0:d=" + audioFadeInDuration + "\" -c:v libx264 -c:a aac " + outputPath;
            else
                commandNewApi = "-i " + inputPath + " -preset ultrafast -filter_complex \"fade=in:st=0:d=" + videoFadeInDuration + "\" -c:v libx264 " + outputPath;

        } else {
            if (VideoUtil.isVideoHaveAudioTrack(inputPath))
                command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-filter_complex", "fade=in:st=0:d=" + videoFadeInDuration + "; afade=in:st=0:d=" + audioFadeInDuration, "-c:v", "libx264", "-c:a", "aac", outputPath};
            else
                command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-filter_complex", "fade=in:st=0:d=" + videoFadeInDuration, "-c:v", "libx264", outputPath};

        }

        return isNewApiRequired() ? (T) commandNewApi : (T) command;
    }

    public static <T extends Object> T getImagesToVideoCommand(String inputPath, String outputPath, int fps, String originalVideoPath) {
        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        retriever.setDataSource(originalVideoPath);
        int width = Integer.valueOf(retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH));
        int height = Integer.valueOf(retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT));
        try {
            retriever.release();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
//        return new String[]{"-y", "-r", +fps + "/1", "-i", inputPath, "-c:v", "libx264", "-vf", "scale="+width+":"+height+","+"fps=" + fps, "-pix_fmt", "yuv420p", outputPath};
        if (isNewApiRequired())
            return (T) ("-y -r " +fps + "/1 -i " + inputPath + " -c:v libx264 -vf \"fps=" + fps +"\" -pix_fmt yuv420p " + outputPath);
        else
            return (T) new String[]{"-y", "-r", +fps + "/1", "-i", inputPath, "-c:v", "libx264", "-vf", "fps=" + fps, "-pix_fmt", "yuv420p", outputPath};

    }

    public static <T extends Object> T getImagesToVideoCommand(String inputPath, String outputPath, int fps) {
        String[] command;
        if (isNewApiRequired())
            return (T) ("-y -r " +fps + "/1 -i " + inputPath + " -c:v libx264 -vf  \"fps=" + fps +"\" -pix_fmt yuv420p -preset ultrafast " + outputPath);

        else
            return (T) new String[]{"-y", "-r", +fps + "/1", "-i", inputPath, "-c:v", "libx264", "-vf", "fps=" + fps, "-pix_fmt", "yuv420p", "-preset", "ultrafast", outputPath};

//        return command;
    }

    public static <T extends Object> T getExtractFramesCommand(String inputPath, String outputPath, int fps) {
        String[] command;
        if (isNewApiRequired())
            return (T) ("-y -i " + inputPath + " -r " +fps + "/1 -preset ultrafast " + outputPath);
        else
            return (T) new String[]{"-y", "-i", inputPath, "-r", +fps + "/1", "-preset", "ultrafast", outputPath};

    }

    public static <T extends Object> T getCommand(String effectName, String inPath, String outputPath) {

        String[] command = null;
        String commandNewApi = null;
        String inputPath = getCharEscaptedPath(inPath);

        switch (effectName) {

//            case IMAGES_TO_VIDEO:
//                command = new String[]{"-y", "-r", "4/1", "-i", inputPath, "-c:v", "libx264", "-vf", "fps=4", "-pix_fmt", "yuv420p", outputPath};
//                break;
            case SPLIT_VIDEO:
                if (isNewApiRequired()) {
                    if (!VideoUtil.isVideoHaveAudioTrack(inPath))
                        commandNewApi = "-i " + inputPath + " -preset ultrafast -c:v libx264 -crf 22 -map 0 -segment_time 6 -g 9 -sc_threshold 0 -force_key_frames expr:gte(t,n_forced*6) -f segment -an " + outputPath;
                    else
                        commandNewApi = "-i " + inputPath + " -preset ultrafast -c:v libx264 -crf 22 -map 0 -segment_time 6 -g 9 -sc_threshold 0 -force_key_frames expr:gte(t,n_forced*6) -f segment " + outputPath;

                } else {
                    if (!VideoUtil.isVideoHaveAudioTrack(inPath))
                        command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-c:v", "libx264", "-crf", "22", "-map", "0", "-segment_time", "6", "-g", "9", "-sc_threshold", "0", "-force_key_frames", "expr:gte(t,n_forced*6)", "-f", "segment", "-an", outputPath};
                    else
                        command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-c:v", "libx264", "-crf", "22", "-map", "0", "-segment_time", "6", "-g", "9", "-sc_threshold", "0", "-force_key_frames", "expr:gte(t,n_forced*6)", "-f", "segment", outputPath};
                }

                break;
            case CROSS_FADE:
                command = new String[]{"-i", inputPath, "-i", inputPath, "-filter_complex",
                        "    [0:v]trim=start=0:end=9,setpts=PTS-STARTPTS[firstclip];" +
                                "    [1:v]trim=start=1,setpts=PTS-STARTPTS[secondclip];" +
                                "    [0:v]trim=start=9:end=10,setpts=PTS-STARTPTS[fadeoutsrc];" +
                                "    [1:v]trim=start=0:end=1,setpts=PTS-STARTPTS[fadeinsrc];" +
                                "    [fadeinsrc]format=pix_fmts=yuva420p," +
                                "                fade=t=in:st=0:d=1:alpha=1[fadein];" +
                                "    [fadeoutsrc]format=pix_fmts=yuva420p," +
                                "                fade=t=out:st=0:d=1:alpha=1[fadeout];" +
                                "    [fadein]fifo[fadeinfifo];" +
                                "    [fadeout]fifo[fadeoutfifo];" +
                                "    [fadeoutfifo][fadeinfifo]overlay[crossfade];" +
                                "    [firstclip][crossfade][secondclip]concat=n=3[output];" +
                                "    [0:a][1:a] acrossfade=d=1 [audio]",
                        "-map", "[output]", "-map", "[audio]", "-preset", "ultrafast", outputPath};
                break;
            case MIRROR_VERTICAL:
                if (isNewApiRequired())
                    commandNewApi = "-y -i " + inputPath + " -filter_complex \"crop=iw/2:ih:0:0,split[left][tmp];[tmp]hflip[right];[left][right] hstack\" -preset ultrafast " + outputPath;
                else
                    command = new String[]{"-y", "-i", inputPath, "-filter_complex", "crop=iw/2:ih:0:0,split[left][tmp];[tmp]hflip[right];[left][right] hstack", "-preset", "ultrafast", outputPath};
                break;
            case PENCIL_SKETCH_4:
//                command = new String[]{"-y", "-i", inputPath, "-r", "30/1", outputPath};
                if (isNewApiRequired())
                    commandNewApi = "-y -i " + inputPath + " -r 4/1 " + outputPath;
                else
                    command = new String[]{"-y", "-i", inputPath, "-r", "4/1", outputPath};
                break;
            case REVERSE_VIDEO:
                if (isNewApiRequired()) {
                    if (!VideoUtil.isVideoHaveAudioTrack(inPath))
                        commandNewApi = "-y -i " + inputPath + " -preset ultrafast -c:v libx264 -crf 22 -filter_complex \"reverse\" -an " + outputPath;
                    else
                        commandNewApi = "-y -i " + inputPath + " -preset ultrafast -c:v libx264 -crf 22 -vf \"reverse\" -af areverse " + outputPath;

                } else {
                    if (!VideoUtil.isVideoHaveAudioTrack(inPath))
                        command = new String[]{"-y", "-i", inputPath, "-preset", "ultrafast", "-c:v", "libx264", "-crf", "22", "-filter_complex", "reverse", "-an", outputPath};
                    else
                        command = new String[]{"-y", "-i", inputPath, "-preset", "ultrafast", "-c:v", "libx264", "-crf", "22", "-vf", "reverse", "-af", "areverse", outputPath};
                }

                break;
            case BOOMERANG:
                if (isNewApiRequired())
                    commandNewApi = "-y -i " + inputPath + " -filter_complex \"[0]reverse[r];[0][r][0]concat=n=3,setpts=0.5*PTS\" -preset ultrafast -crf 22 " + outputPath;
                else
                    command = new String[]{"-y", "-i", inputPath, "-filter_complex", "[0]reverse[r];[0][r][0]concat=n=3,setpts=0.5*PTS", "-preset", "ultrafast", "-crf", "22", outputPath};
                break;
            case BLACK_AND_WHITE:
                if (isNewApiRequired()) {
                    commandNewApi = "-y -i " + inputPath + " -vf \"hue=s=0\" -preset ultrafast -c:a copy " + outputPath;
                } else
                    command = new String[]{"-y", "-i", inputPath, "-vf", "hue=s=0", "-preset", "ultrafast", "-c:a", "copy", outputPath};
                break;
            case SEPIA:
                if (isNewApiRequired()) {
                    if (VideoUtil.isVideoHaveAudioTrack(inPath))
                        commandNewApi = "-i " + inputPath + " -strict experimental -preset ultrafast -filter_complex  \"[0:v]colorchannelmixer=.393:.769:.189:0:.349:.686:.168:0:.272:.534:.131[colorchannelmixed];[colorchannelmixed]eq=1.0:0:1.3:2.4:1.0:1.0:1.0:1.0[color_effect]\" -map [color_effect] -map 0:a -vcodec mpeg4 -crf 23 -b:v 1000K -maxrate 1500K -bufsize 500K " + outputPath;
                    else
                        commandNewApi = "-i " + inputPath + " -strict experimental -preset ultrafast -filter_complex  \"[0:v]colorchannelmixer=.393:.769:.189:0:.349:.686:.168:0:.272:.534:.131[colorchannelmixed];[colorchannelmixed]eq=1.0:0:1.3:2.4:1.0:1.0:1.0:1.0[color_effect]\" -map [color_effect] -vcodec mpeg4 -crf 23 -b:v 1000K -maxrate 1500K -bufsize 500K " + outputPath;

                } else {
                    if (VideoUtil.isVideoHaveAudioTrack(inPath))
                        command = new String[]{"-i", inputPath, "-strict", "experimental", "-preset", "ultrafast", "-filter_complex", "[0:v]colorchannelmixer=.393:.769:.189:0:.349:.686:.168:0:.272:.534:.131[colorchannelmixed];[colorchannelmixed]eq=1.0:0:1.3:2.4:1.0:1.0:1.0:1.0[color_effect]", "-map", "[color_effect]", "-map", "0:a", "-vcodec", "mpeg4", "-crf", "23", "-b:v", "1000K", "-maxrate", "1500K", "-bufsize", "500K", outputPath};
                    else
                        command = new String[]{"-i", inputPath, "-strict", "experimental", "-preset", "ultrafast", "-filter_complex", "[0:v]colorchannelmixer=.393:.769:.189:0:.349:.686:.168:0:.272:.534:.131[colorchannelmixed];[colorchannelmixed]eq=1.0:0:1.3:2.4:1.0:1.0:1.0:1.0[color_effect]", "-map", "[color_effect]", "-vcodec", "mpeg4", "-crf", "23", "-b:v", "1000K", "-maxrate", "1500K", "-bufsize", "500K", outputPath};

                }
                break;
            case GAMMA_CORRECTION:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"eq=gamma=7.0:saturation=1.3\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "eq=gamma=7.0:saturation=1.3", "-c:a", "copy", outputPath};
                break;
            case STABILIZATION:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf  \"deshake\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "deshake", "-c:a", "copy", outputPath};
                break;
            case SHARPER:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"unsharp\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "unsharp", "-c:a", "copy", outputPath};
                break;
            case ZOOM_IN_PROGRESSIVELY:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"zoompan=z='1+(1.4*in/300)':x='70*in/300':y='190*in/300':d=1\" " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "zoompan=z='1+(1.4*in/300)':x='70*in/300':y='190*in/300':d=1", outputPath};
                break;
            case FLIP_VERTICAL:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf  \"vflip\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "vflip", "-c:a", "copy", outputPath};
                break;
            case FLIP_HORIZONTAL:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"hflip\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "hflip", "-c:a", "copy", outputPath};
                break;
            case ROTATE_90_DEGREE_CLOCKWISE:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf  \"transpose=1\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "transpose=1", "-c:a", "copy", outputPath};
                break;
            case ROTATE_90_DEGERE_COUNTERCLOCKWISE:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"transpose=2\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "transpose=2", "-c:a", "copy", outputPath};
                break;
            case BLUR:
//                command = new String[]{"-i", inputPath, "-filter_complex", "[0:v]boxblur=10[bg];[0:v]crop=100:100:60:30[fg];[bg][fg]overlay=60:30", "-map", "0:a", "-c:v", "libx264", "-c:a", "copy", "-movflags", "+faststart", outputPath};
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -filter_complex \"boxblur=10\" -map 0:a? -c:v libx264 -c:a copy -movflags +faststart " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-filter_complex", "boxblur=10", "-map", "0:a", "-c:v", "libx264", "-c:a", "copy", "-movflags", "+faststart", outputPath};
                break;
            case CONVOLUTION:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"convolution=-2 -1 0 -1 1 1 0 1 2:-2 -1 0 -1 1 1 0 1 2:-2 -1 0 -1 1 1 0 1 2:-2 -1 0 -1 1 1 0 1 2\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "convolution=\"-2 -1 0 -1 1 1 0 1 2:-2 -1 0 -1 1 1 0 1 2:-2 -1 0 -1 1 1 0 1 2:-2 -1 0 -1 1 1 0 1 2\"", "-c:a", "copy", outputPath};
                break;
            case CONVOLUTION_2:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"convolution=1 1 1 1 -8 1 1 1 1:1 1 1 1 -8 1 1 1 1:1 1 1 1 -8 1 1 1 1:1 1 1 1 -8 1 1 1 1:5:5:5:1:0:128:128:0\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "convolution=1 1 1 1 -8 1 1 1 1:1 1 1 1 -8 1 1 1 1:1 1 1 1 -8 1 1 1 1:1 1 1 1 -8 1 1 1 1:5:5:5:1:0:128:128:0", "-c:a", "copy", outputPath};
                break;
            case STEREOSCOPIC:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"stereo3d=sbs2l:arcc\" -c:a copy -pix_fmt yuv420p -y " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "stereo3d=sbs2l:arcc", "-c:a", "copy", "-pix_fmt", "yuv420p", "-y", outputPath};
                break;
            case PIXELIZE:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"scale=iw/10:ih/10,scale=10*iw:10*ih:flags=neighbor\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "scale=iw/10:ih/10,scale=10*iw:10*ih:flags=neighbor", "-c:a", "copy", outputPath};
                break;
            case BRIGHTNESS_PLUS:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"eq=brightness=0.3\" " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "eq=brightness=0.3", outputPath};
                break;
            case BRIGHTNESS_MINUS:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"eq=brightness=-0.3\" " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "eq=brightness=-0.3", outputPath};
                break;
            case SATURATION_PLUS:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"eq=saturation=1.5\" " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "eq=saturation=1.5", outputPath};
                break;
            case SATURATION_MINUS:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"eq=saturation=0.5\" " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "eq=saturation=0.5", outputPath};
                break;
            case GAMMA_R_PLUS:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"eq=gamma_r=1.5\" " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "eq=gamma_r=1.5", outputPath};
                break;
            case GAMMA_R_MINUS:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"eq=gamma_r=-0.5\" " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "eq=gamma_r=-0.5", outputPath};
                break;
            case GAMMA_G_PLUS:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"eq=gamma_g=1.5\" " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "eq=gamma_g=1.5", outputPath};
                break;
            case GAMMA_G_MINUS:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"eq=gamma_g=-0.5\" " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "eq=gamma_g=-0.5", outputPath};
                break;
            case GAMMA_B_PLUS:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"eq=gamma_b=1.5\" " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "eq=gamma_b=1.5", outputPath};
                break;
            case GAMMA_B_MINUS:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"eq=gamma_b=-0.5\" " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "eq=gamma_b=-0.5", outputPath};
                break;
            case COLOR_BALANCE:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"colorbalance=rs=-0.3:bs=0.3:rh=0.1:bh=-0.1\" -pix_fmt yuv420p -y " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "colorbalance=rs=-0.3:bs=0.3:rh=0.1:bh=-0.1", "-pix_fmt", "yuv420p", "-y", outputPath};
                break;
            case COLOR_BALANCE_TWO:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"colorbalance=gs=0.3:rh=0.1:bh=0.1\" -pix_fmt yuv420p -y " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "colorbalance=gs=0.3:rh=0.1:bh=0.1", "-pix_fmt", "yuv420p", "-y", outputPath};
                break;
            case COLOR_NEGATIVE:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"curves=color_negative\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "curves=color_negative", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case CROSS_PROCESS:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"curves=cross_process\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "curves=cross_process", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case DARKER:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"curves=darker\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "curves=darker", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case LIGHTER:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"curves=lighter\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "curves=lighter", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case INCREASE_CONTRAST:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"curves=increase_contrast\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "curves=increase_contrast", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case LINEAR_CONTRAST:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"curves=linear_contrast\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "curves=linear_contrast", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case MEDIUM_CONTRAST:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"curves=medium_contrast\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "curves=medium_contrast", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case STRONG_CONTRAST:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"curves=strong_contrast\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "curves=strong_contrast", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case NEGATIVE:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"curves=negative\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "curves=negative", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case CURVES_BLUE:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"curves=blue='0/0 0.5/0.58 1/1'\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "curves=blue='0/0 0.5/0.58 1/1'", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case ENHANCED_LBG_2:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"elbg=2:n=1\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "elbg=2:n=1", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case ENHANCED_LBG_4:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"elbg=4:n=1\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "elbg=4:n=1", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case ENHANCED_LBG_8:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"elbg=8:n=1\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "elbg=8:n=1", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case ENHANCED_LBG_16:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"elbg=16:n=1\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "elbg=16:n=1", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case DEBAND:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"deband=1thr=0.5:2thr=0.5:3thr=0.5\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "deband=1thr=0.5:2thr=0.5:3thr=0.5", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case SWAP_RECT_ONE:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"swaprect=w/6:h/6:0:5*h/6,swaprect=w/6:h/6:5*w/6:h/2,swaprect=w/6:h/6:w/3:5*h/6,swaprect=w/6:h/6:0:h/3,swaprect=w/6:h/6:w/6:0,swaprect=w/6:h/6:w/2:h/6,swaprect=w/6:h/6:5*w/6:5*h/6,swaprect=w/6:h/6:2*w/3:0,swaprect=w/6:h/6:w/3:h/6,swaprect=w/6:h/6:2*w/3:h/3,swaprect=w/6:h/6:0:h/2,swaprect=w/6:h/6:2*w/3:2*h/3,swaprect=w/6:h/6:w/2:5*h/6,swaprect=w/6:h/6:w/2:0,swaprect=w/6:h/6:5*w/6:0,swaprect=w/6:h/6:w/2:h/3,swaprect=w/6:h/6:2*w/3:h/2,swaprect=w/6:h/6:0:h/6,swaprect=w/6:h/6:w/6:h/3,swaprect=w/6:h/6:5*w/6:2*h/3,swaprect=w/6:h/6:0:2*h/3,swaprect=w/6:h/6:w/3:h/3,swaprect=w/6:h/6:2*w/3:5*h/6,swaprect=w/6:h/6:w/6:2*h/3,swaprect=w/6:h/6:w/6:h/6,swaprect=w/6:h/6:2*w/3:h/6,swaprect=w/6:h/6:w/3:2*h/3,swaprect=w/6:h/6:w/2:h/2,swaprect=w/6:h/6:w/2:2*h/3,swaprect=w/6:h/6:w/3:h/2,swaprect=w/6:h/6:w/6:h/2,swaprect=w/6:h/6:5*w/6:h/3,swaprect=w/6:h/6:w/6:5*h/6,swaprect=w/6:h/6:5*w/6:h/6,swaprect=w/6:h/6:0:0,swaprect=w/6:h/6:w/3:0\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "swaprect=w/6:h/6:0:5*h/6,swaprect=w/6:h/6:5*w/6:h/2,swaprect=w/6:h/6:w/3:5*h/6,swaprect=w/6:h/6:0:h/3,swaprect=w/6:h/6:w/6:0,swaprect=w/6:h/6:w/2:h/6,swaprect=w/6:h/6:5*w/6:5*h/6,swaprect=w/6:h/6:2*w/3:0,swaprect=w/6:h/6:w/3:h/6,swaprect=w/6:h/6:2*w/3:h/3,swaprect=w/6:h/6:0:h/2,swaprect=w/6:h/6:2*w/3:2*h/3,swaprect=w/6:h/6:w/2:5*h/6,swaprect=w/6:h/6:w/2:0,swaprect=w/6:h/6:5*w/6:0,swaprect=w/6:h/6:w/2:h/3,swaprect=w/6:h/6:2*w/3:h/2,swaprect=w/6:h/6:0:h/6,swaprect=w/6:h/6:w/6:h/3,swaprect=w/6:h/6:5*w/6:2*h/3,swaprect=w/6:h/6:0:2*h/3,swaprect=w/6:h/6:w/3:h/3,swaprect=w/6:h/6:2*w/3:5*h/6,swaprect=w/6:h/6:w/6:2*h/3,swaprect=w/6:h/6:w/6:h/6,swaprect=w/6:h/6:2*w/3:h/6,swaprect=w/6:h/6:w/3:2*h/3,swaprect=w/6:h/6:w/2:h/2,swaprect=w/6:h/6:w/2:2*h/3,swaprect=w/6:h/6:w/3:h/2,swaprect=w/6:h/6:w/6:h/2,swaprect=w/6:h/6:5*w/6:h/3,swaprect=w/6:h/6:w/6:5*h/6,swaprect=w/6:h/6:5*w/6:h/6,swaprect=w/6:h/6:0:0,swaprect=w/6:h/6:w/3:0", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case SWAP_RECT_TWO:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"swaprect=w/6:h/6:w/3:0,swaprect=w/6:h/6:0:0,swaprect=w/6:h/6:5*w/6:h/6,swaprect=w/6:h/6:w/6:5*h/6,swaprect=w/6:h/6:5*w/6:h/3,swaprect=w/6:h/6:w/6:h/2,swaprect=w/6:h/6:w/3:h/2,swaprect=w/6:h/6:w/2:2*h/3,swaprect=w/6:h/6:w/2:h/2,swaprect=w/6:h/6:w/3:2*h/3,swaprect=w/6:h/6:2*w/3:h/6,swaprect=w/6:h/6:w/6:h/6,swaprect=w/6:h/6:w/6:2*h/3,swaprect=w/6:h/6:2*w/3:5*h/6,swaprect=w/6:h/6:w/3:h/3,swaprect=w/6:h/6:0:2*h/3,swaprect=w/6:h/6:5*w/6:2*h/3,swaprect=w/6:h/6:w/6:h/3,swaprect=w/6:h/6:0:h/6,swaprect=w/6:h/6:2*w/3:h/2,swaprect=w/6:h/6:w/2:h/3,swaprect=w/6:h/6:5*w/6:0,swaprect=w/6:h/6:w/2:0,swaprect=w/6:h/6:w/2:5*h/6,swaprect=w/6:h/6:2*w/3:2*h/3,swaprect=w/6:h/6:0:h/2,swaprect=w/6:h/6:2*w/3:h/3,swaprect=w/6:h/6:w/3:h/6,swaprect=w/6:h/6:2*w/3:0,swaprect=w/6:h/6:5*w/6:5*h/6,swaprect=w/6:h/6:w/2:h/6,swaprect=w/6:h/6:w/6:0,swaprect=w/6:h/6:0:h/3,swaprect=w/6:h/6:w/3:5*h/6,swaprect=w/6:h/6:5*w/6:h/2,swaprect=w/6:h/6:0:5*h/6\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "swaprect=w/6:h/6:w/3:0,swaprect=w/6:h/6:0:0,swaprect=w/6:h/6:5*w/6:h/6,swaprect=w/6:h/6:w/6:5*h/6,swaprect=w/6:h/6:5*w/6:h/3,swaprect=w/6:h/6:w/6:h/2,swaprect=w/6:h/6:w/3:h/2,swaprect=w/6:h/6:w/2:2*h/3,swaprect=w/6:h/6:w/2:h/2,swaprect=w/6:h/6:w/3:2*h/3,swaprect=w/6:h/6:2*w/3:h/6,swaprect=w/6:h/6:w/6:h/6,swaprect=w/6:h/6:w/6:2*h/3,swaprect=w/6:h/6:2*w/3:5*h/6,swaprect=w/6:h/6:w/3:h/3,swaprect=w/6:h/6:0:2*h/3,swaprect=w/6:h/6:5*w/6:2*h/3,swaprect=w/6:h/6:w/6:h/3,swaprect=w/6:h/6:0:h/6,swaprect=w/6:h/6:2*w/3:h/2,swaprect=w/6:h/6:w/2:h/3,swaprect=w/6:h/6:5*w/6:0,swaprect=w/6:h/6:w/2:0,swaprect=w/6:h/6:w/2:5*h/6,swaprect=w/6:h/6:2*w/3:2*h/3,swaprect=w/6:h/6:0:h/2,swaprect=w/6:h/6:2*w/3:h/3,swaprect=w/6:h/6:w/3:h/6,swaprect=w/6:h/6:2*w/3:0,swaprect=w/6:h/6:5*w/6:5*h/6,swaprect=w/6:h/6:w/2:h/6,swaprect=w/6:h/6:w/6:0,swaprect=w/6:h/6:0:h/3,swaprect=w/6:h/6:w/3:5*h/6,swaprect=w/6:h/6:5*w/6:h/2,swaprect=w/6:h/6:0:5*h/6", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case SWAP_RECT_THREE:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"swaprect=w/10:h/6:w/10:5*h/6,swaprect=w/10:h/6:w/10:h/3,swaprect=w/10:h/6:0:h/3,swaprect=w/10:h/6:2*w/5:5*h/6,swaprect=w/10:h/6:0:h/2,swaprect=w/10:h/6:3*w/5:2*h/3,swaprect=w/10:h/6:7*w/10:0,swaprect=w/10:h/6:3*w/10:h/6,swaprect=w/10:h/6:9*w/10:h/3,swaprect=w/10:h/6:4*w/5:2*h/3,swaprect=w/10:h/6:3*w/5:h/6,swaprect=w/10:h/6:2*w/5:2*h/3,swaprect=w/10:h/6:4*w/5:5*h/6,swaprect=w/10:h/6:2*w/5:h/6,swaprect=w/10:h/6:4*w/5:h/2,swaprect=w/10:h/6:9*w/10:h/6,swaprect=w/10:h/6:w/5:0,swaprect=w/10:h/6:w/2:h/3,swaprect=w/10:h/6:3*w/10:h/2,swaprect=w/10:h/6:7*w/10:h/6,swaprect=w/10:h/6:2*w/5:h/2,swaprect=w/10:h/6:3*w/5:h/3,swaprect=w/10:h/6:2*w/5:h/3,swaprect=w/10:h/6:4*w/5:h/6,swaprect=w/10:h/6:3*w/10:5*h/6,swaprect=w/10:h/6:7*w/10:5*h/6,swaprect=w/10:h/6:w/10:0,swaprect=w/10:h/6:w/5:h/3,swaprect=w/10:h/6:w/5:5*h/6,swaprect=w/10:h/6:4*w/5:h/3,swaprect=w/10:h/6:w/2:h/2,swaprect=w/10:h/6:3*w/5:5*h/6,swaprect=w/10:h/6:0:5*h/6,swaprect=w/10:h/6:0:2*h/3,swaprect=w/10:h/6:w/10:2*h/3,swaprect=w/10:h/6:w/2:2*h/3,swaprect=w/10:h/6:w/5:h/2,swaprect=w/10:h/6:4*w/5:0,swaprect=w/10:h/6:w/10:h/2,swaprect=w/10:h/6:7*w/10:h/2,swaprect=w/10:h/6:9*w/10:5*h/6,swaprect=w/10:h/6:9*w/10:h/2,swaprect=w/10:h/6:w/5:h/6,swaprect=w/10:h/6:3*w/10:2*h/3,swaprect=w/10:h/6:w/2:5*h/6,swaprect=w/10:h/6:2*w/5:0,swaprect=w/10:h/6:9*w/10:2*h/3,swaprect=w/10:h/6:3*w/10:h/3,swaprect=w/10:h/6:0:h/6,swaprect=w/10:h/6:9*w/10:0,swaprect=w/10:h/6:3*w/10:0,swaprect=w/10:h/6:3*w/5:h/2,swaprect=w/10:h/6:w/2:0,swaprect=w/10:h/6:7*w/10:2*h/3,swaprect=w/10:h/6:7*w/10:h/3,swaprect=w/10:h/6:w/10:h/6,swaprect=w/10:h/6:w/2:h/6,swaprect=w/10:h/6:w/5:2*h/3,swaprect=w/10:h/6:3*w/5:0,swaprect=w/10:h/6:0:0\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "swaprect=w/10:h/6:w/10:5*h/6,swaprect=w/10:h/6:w/10:h/3,swaprect=w/10:h/6:0:h/3,swaprect=w/10:h/6:2*w/5:5*h/6,swaprect=w/10:h/6:0:h/2,swaprect=w/10:h/6:3*w/5:2*h/3,swaprect=w/10:h/6:7*w/10:0,swaprect=w/10:h/6:3*w/10:h/6,swaprect=w/10:h/6:9*w/10:h/3,swaprect=w/10:h/6:4*w/5:2*h/3,swaprect=w/10:h/6:3*w/5:h/6,swaprect=w/10:h/6:2*w/5:2*h/3,swaprect=w/10:h/6:4*w/5:5*h/6,swaprect=w/10:h/6:2*w/5:h/6,swaprect=w/10:h/6:4*w/5:h/2,swaprect=w/10:h/6:9*w/10:h/6,swaprect=w/10:h/6:w/5:0,swaprect=w/10:h/6:w/2:h/3,swaprect=w/10:h/6:3*w/10:h/2,swaprect=w/10:h/6:7*w/10:h/6,swaprect=w/10:h/6:2*w/5:h/2,swaprect=w/10:h/6:3*w/5:h/3,swaprect=w/10:h/6:2*w/5:h/3,swaprect=w/10:h/6:4*w/5:h/6,swaprect=w/10:h/6:3*w/10:5*h/6,swaprect=w/10:h/6:7*w/10:5*h/6,swaprect=w/10:h/6:w/10:0,swaprect=w/10:h/6:w/5:h/3,swaprect=w/10:h/6:w/5:5*h/6,swaprect=w/10:h/6:4*w/5:h/3,swaprect=w/10:h/6:w/2:h/2,swaprect=w/10:h/6:3*w/5:5*h/6,swaprect=w/10:h/6:0:5*h/6,swaprect=w/10:h/6:0:2*h/3,swaprect=w/10:h/6:w/10:2*h/3,swaprect=w/10:h/6:w/2:2*h/3,swaprect=w/10:h/6:w/5:h/2,swaprect=w/10:h/6:4*w/5:0,swaprect=w/10:h/6:w/10:h/2,swaprect=w/10:h/6:7*w/10:h/2,swaprect=w/10:h/6:9*w/10:5*h/6,swaprect=w/10:h/6:9*w/10:h/2,swaprect=w/10:h/6:w/5:h/6,swaprect=w/10:h/6:3*w/10:2*h/3,swaprect=w/10:h/6:w/2:5*h/6,swaprect=w/10:h/6:2*w/5:0,swaprect=w/10:h/6:9*w/10:2*h/3,swaprect=w/10:h/6:3*w/10:h/3,swaprect=w/10:h/6:0:h/6,swaprect=w/10:h/6:9*w/10:0,swaprect=w/10:h/6:3*w/10:0,swaprect=w/10:h/6:3*w/5:h/2,swaprect=w/10:h/6:w/2:0,swaprect=w/10:h/6:7*w/10:2*h/3,swaprect=w/10:h/6:7*w/10:h/3,swaprect=w/10:h/6:w/10:h/6,swaprect=w/10:h/6:w/2:h/6,swaprect=w/10:h/6:w/5:2*h/3,swaprect=w/10:h/6:3*w/5:0,swaprect=w/10:h/6:0:0,", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case SWAP_RECT_FOUR:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"swaprect=w/10:h/6:0:0,swaprect=w/10:h/6:3*w/5:0,swaprect=w/10:h/6:w/5:2*h/3,swaprect=w/10:h/6:w/2:h/6,swaprect=w/10:h/6:w/10:h/6,swaprect=w/10:h/6:7*w/10:h/3,swaprect=w/10:h/6:7*w/10:2*h/3,swaprect=w/10:h/6:w/2:0,swaprect=w/10:h/6:3*w/5:h/2,swaprect=w/10:h/6:3*w/10:0,swaprect=w/10:h/6:9*w/10:0,swaprect=w/10:h/6:0:h/6,swaprect=w/10:h/6:3*w/10:h/3,swaprect=w/10:h/6:9*w/10:2*h/3,swaprect=w/10:h/6:2*w/5:0,swaprect=w/10:h/6:w/2:5*h/6,swaprect=w/10:h/6:3*w/10:2*h/3,swaprect=w/10:h/6:w/5:h/6,swaprect=w/10:h/6:9*w/10:h/2,swaprect=w/10:h/6:9*w/10:5*h/6,swaprect=w/10:h/6:7*w/10:h/2,swaprect=w/10:h/6:w/10:h/2,swaprect=w/10:h/6:4*w/5:0,swaprect=w/10:h/6:w/5:h/2,swaprect=w/10:h/6:w/2:2*h/3,swaprect=w/10:h/6:w/10:2*h/3,swaprect=w/10:h/6:0:2*h/3,swaprect=w/10:h/6:0:5*h/6,swaprect=w/10:h/6:3*w/5:5*h/6,swaprect=w/10:h/6:w/2:h/2,swaprect=w/10:h/6:4*w/5:h/3,swaprect=w/10:h/6:w/5:5*h/6,swaprect=w/10:h/6:w/5:h/3,swaprect=w/10:h/6:w/10:0,swaprect=w/10:h/6:7*w/10:5*h/6,swaprect=w/10:h/6:3*w/10:5*h/6,swaprect=w/10:h/6:4*w/5:h/6,swaprect=w/10:h/6:2*w/5:h/3,swaprect=w/10:h/6:3*w/5:h/3,swaprect=w/10:h/6:2*w/5:h/2,swaprect=w/10:h/6:7*w/10:h/6,swaprect=w/10:h/6:3*w/10:h/2,swaprect=w/10:h/6:w/2:h/3,swaprect=w/10:h/6:w/5:0,swaprect=w/10:h/6:9*w/10:h/6,swaprect=w/10:h/6:4*w/5:h/2,swaprect=w/10:h/6:2*w/5:h/6,swaprect=w/10:h/6:4*w/5:5*h/6,swaprect=w/10:h/6:2*w/5:2*h/3,swaprect=w/10:h/6:3*w/5:h/6,swaprect=w/10:h/6:4*w/5:2*h/3,swaprect=w/10:h/6:9*w/10:h/3,swaprect=w/10:h/6:3*w/10:h/6,swaprect=w/10:h/6:7*w/10:0,swaprect=w/10:h/6:3*w/5:2*h/3,swaprect=w/10:h/6:0:h/2,swaprect=w/10:h/6:2*w/5:5*h/6,swaprect=w/10:h/6:0:h/3,swaprect=w/10:h/6:w/10:h/3,swaprect=w/10:h/6:w/10:5*h/6\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "swaprect=w/10:h/6:0:0,swaprect=w/10:h/6:3*w/5:0,swaprect=w/10:h/6:w/5:2*h/3,swaprect=w/10:h/6:w/2:h/6,swaprect=w/10:h/6:w/10:h/6,swaprect=w/10:h/6:7*w/10:h/3,swaprect=w/10:h/6:7*w/10:2*h/3,swaprect=w/10:h/6:w/2:0,swaprect=w/10:h/6:3*w/5:h/2,swaprect=w/10:h/6:3*w/10:0,swaprect=w/10:h/6:9*w/10:0,swaprect=w/10:h/6:0:h/6,swaprect=w/10:h/6:3*w/10:h/3,swaprect=w/10:h/6:9*w/10:2*h/3,swaprect=w/10:h/6:2*w/5:0,swaprect=w/10:h/6:w/2:5*h/6,swaprect=w/10:h/6:3*w/10:2*h/3,swaprect=w/10:h/6:w/5:h/6,swaprect=w/10:h/6:9*w/10:h/2,swaprect=w/10:h/6:9*w/10:5*h/6,swaprect=w/10:h/6:7*w/10:h/2,swaprect=w/10:h/6:w/10:h/2,swaprect=w/10:h/6:4*w/5:0,swaprect=w/10:h/6:w/5:h/2,swaprect=w/10:h/6:w/2:2*h/3,swaprect=w/10:h/6:w/10:2*h/3,swaprect=w/10:h/6:0:2*h/3,swaprect=w/10:h/6:0:5*h/6,swaprect=w/10:h/6:3*w/5:5*h/6,swaprect=w/10:h/6:w/2:h/2,swaprect=w/10:h/6:4*w/5:h/3,swaprect=w/10:h/6:w/5:5*h/6,swaprect=w/10:h/6:w/5:h/3,swaprect=w/10:h/6:w/10:0,swaprect=w/10:h/6:7*w/10:5*h/6,swaprect=w/10:h/6:3*w/10:5*h/6,swaprect=w/10:h/6:4*w/5:h/6,swaprect=w/10:h/6:2*w/5:h/3,swaprect=w/10:h/6:3*w/5:h/3,swaprect=w/10:h/6:2*w/5:h/2,swaprect=w/10:h/6:7*w/10:h/6,swaprect=w/10:h/6:3*w/10:h/2,swaprect=w/10:h/6:w/2:h/3,swaprect=w/10:h/6:w/5:0,swaprect=w/10:h/6:9*w/10:h/6,swaprect=w/10:h/6:4*w/5:h/2,swaprect=w/10:h/6:2*w/5:h/6,swaprect=w/10:h/6:4*w/5:5*h/6,swaprect=w/10:h/6:2*w/5:2*h/3,swaprect=w/10:h/6:3*w/5:h/6,swaprect=w/10:h/6:4*w/5:2*h/3,swaprect=w/10:h/6:9*w/10:h/3,swaprect=w/10:h/6:3*w/10:h/6,swaprect=w/10:h/6:7*w/10:0,swaprect=w/10:h/6:3*w/5:2*h/3,swaprect=w/10:h/6:0:h/2,swaprect=w/10:h/6:2*w/5:5*h/6,swaprect=w/10:h/6:0:h/3,swaprect=w/10:h/6:w/10:h/3,swaprect=w/10:h/6:w/10:5*h/6", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case SWAP_RECT_FIVE:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"swaprect=w/16:h/9:w/16:5*h/9,swaprect=w/16:h/9:7*w/16:2*h/9,swaprect=w/16:h/9:w/2:8*h/9,swaprect=w/16:h/9:7*w/8:h/3,swaprect=w/16:h/9:13*w/16:8*h/9,swaprect=w/16:h/9:3*w/4:h/9,swaprect=w/16:h/9:5*w/8:h/3,swaprect=w/16:h/9:9*w/16:5*h/9,swaprect=w/16:h/9:w/2:h/3,swaprect=w/16:h/9:0:8*h/9,swaprect=w/16:h/9:3*w/8:2*h/9,swaprect=w/16:h/9:5*w/8:5*h/9,swaprect=w/16:h/9:11*w/16:8*h/9,swaprect=w/16:h/9:0:4*h/9,swaprect=w/16:h/9:11*w/16:h/3,swaprect=w/16:h/9:7*w/8:7*h/9,swaprect=w/16:h/9:15*w/16:4*h/9,swaprect=w/16:h/9:3*w/16:7*h/9,swaprect=w/16:h/9:5*w/16:h/9,swaprect=w/16:h/9:13*w/16:h/9,swaprect=w/16:h/9:9*w/16:4*h/9,swaprect=w/16:h/9:w/4:2*h/9,swaprect=w/16:h/9:w/8:4*h/9,swaprect=w/16:h/9:9*w/16:2*h/3,swaprect=w/16:h/9:5*w/16:h/3,swaprect=w/16:h/9:w/16:8*h/9,swaprect=w/16:h/9:3*w/4:2*h/3,swaprect=w/16:h/9:3*w/4:0,swaprect=w/16:h/9:5*w/16:7*h/9,swaprect=w/16:h/9:13*w/16:2*h/9,swaprect=w/16:h/9:5*w/16:2*h/3,swaprect=w/16:h/9:5*w/8:4*h/9,swaprect=w/16:h/9:w/4:2*h/3,swaprect=w/16:h/9:3*w/8:4*h/9,swaprect=w/16:h/9:w/4:5*h/9,swaprect=w/16:h/9:7*w/8:5*h/9,swaprect=w/16:h/9:w/2:2*h/3,swaprect=w/16:h/9:13*w/16:0,swaprect=w/16:h/9:w/16:2*h/9,swaprect=w/16:h/9:11*w/16:4*h/9,swaprect=w/16:h/9:7*w/8:4*h/9,swaprect=w/16:h/9:3*w/16:4*h/9,swaprect=w/16:h/9:0:5*h/9,swaprect=w/16:h/9:3*w/8:8*h/9,swaprect=w/16:h/9:0:h/3,swaprect=w/16:h/9:w/16:7*h/9,swaprect=w/16:h/9:9*w/16:7*h/9,swaprect=w/16:h/9:3*w/8:7*h/9,swaprect=w/16:h/9:w/4:7*h/9,swaprect=w/16:h/9:w/8:2*h/3,swaprect=w/16:h/9:w/2:0,swaprect=w/16:h/9:0:2*h/9,swaprect=w/16:h/9:3*w/4:2*h/9,swaprect=w/16:h/9:5*w/8:0,swaprect=w/16:h/9:3*w/8:h/3,swaprect=w/16:h/9:7*w/16:h/3,swaprect=w/16:h/9:9*w/16:h/3,swaprect=w/16:h/9:w/8:h/3,swaprect=w/16:h/9:3*w/8:5*h/9,swaprect=w/16:h/9:3*w/16:5*h/9,swaprect=w/16:h/9:5*w/16:0,swaprect=w/16:h/9:11*w/16:2*h/9,swaprect=w/16:h/9:w/8:2*h/9,swaprect=w/16:h/9:w/16:h/3,swaprect=w/16:h/9:3*w/16:8*h/9,swaprect=w/16:h/9:w/4:h/9,swaprect=w/16:h/9:7*w/8:2*h/3,swaprect=w/16:h/9:13*w/16:5*h/9,swaprect=w/16:h/9:7*w/8:0,swaprect=w/16:h/9:3*w/16:h/9,swaprect=w/16:h/9:3*w/4:h/3,swaprect=w/16:h/9:7*w/16:h/9,swaprect=w/16:h/9:15*w/16:7*h/9,swaprect=w/16:h/9:7*w/16:5*h/9,swaprect=w/16:h/9:15*w/16:5*h/9,swaprect=w/16:h/9:15*w/16:0,swaprect=w/16:h/9:0:h/9,swaprect=w/16:h/9:11*w/16:2*h/3,swaprect=w/16:h/9:w/2:2*h/9,swaprect=w/16:h/9:7*w/8:h/9,swaprect=w/16:h/9:9*w/16:8*h/9,swaprect=w/16:h/9:3*w/4:8*h/9,swaprect=w/16:h/9:w/2:4*h/9,swaprect=w/16:h/9:0:7*h/9,swaprect=w/16:h/9:w/8:0,swaprect=w/16:h/9:13*w/16:2*h/3,swaprect=w/16:h/9:11*w/16:0,swaprect=w/16:h/9:w/2:5*h/9,swaprect=w/16:h/9:15*w/16:8*h/9,swaprect=w/16:h/9:w/4:h/3,swaprect=w/16:h/9:13*w/16:h/3,swaprect=w/16:h/9:3*w/16:2*h/9,swaprect=w/16:h/9:7*w/16:8*h/9,swaprect=w/16:h/9:3*w/8:2*h/3,swaprect=w/16:h/9:15*w/16:h/3,swaprect=w/16:h/9:w/16:2*h/3,swaprect=w/16:h/9:0:0,swaprect=w/16:h/9:w/2:7*h/9,swaprect=w/16:h/9:5*w/8:7*h/9,swaprect=w/16:h/9:5*w/8:8*h/9,swaprect=w/16:h/9:w/16:0,swaprect=w/16:h/9:w/8:h/9,swaprect=w/16:h/9:5*w/16:5*h/9,swaprect=w/16:h/9:w/8:5*h/9,swaprect=w/16:h/9:3*w/16:2*h/3,swaprect=w/16:h/9:w/4:8*h/9,swaprect=w/16:h/9:9*w/16:0,swaprect=w/16:h/9:5*w/8:2*h/3,swaprect=w/16:h/9:7*w/8:2*h/9,swaprect=w/16:h/9:3*w/4:4*h/9,swaprect=w/16:h/9:7*w/16:7*h/9,swaprect=w/16:h/9:w/16:4*h/9,swaprect=w/16:h/9:5*w/16:8*h/9,swaprect=w/16:h/9:5*w/16:4*h/9,swaprect=w/16:h/9:13*w/16:4*h/9,swaprect=w/16:h/9:7*w/16:0,swaprect=w/16:h/9:w/16:h/9,swaprect=w/16:h/9:15*w/16:h/9,swaprect=w/16:h/9:0:2*h/3,swaprect=w/16:h/9:9*w/16:h/9,swaprect=w/16:h/9:13*w/16:7*h/9,swaprect=w/16:h/9:15*w/16:2*h/3,swaprect=w/16:h/9:3*w/16:0,swaprect=w/16:h/9:3*w/8:h/9,swaprect=w/16:h/9:3*w/4:5*h/9,swaprect=w/16:h/9:3*w/4:7*h/9,swaprect=w/16:h/9:7*w/8:8*h/9,swaprect=w/16:h/9:7*w/16:2*h/3,swaprect=w/16:h/9:w/4:4*h/9,swaprect=w/16:h/9:3*w/8:0,swaprect=w/16:h/9:3*w/16:h/3,swaprect=w/16:h/9:w/8:7*h/9,swaprect=w/16:h/9:11*w/16:h/9,swaprect=w/16:h/9:5*w/8:2*h/9,swaprect=w/16:h/9:15*w/16:2*h/9,swaprect=w/16:h/9:11*w/16:5*h/9,swaprect=w/16:h/9:w/2:h/9,swaprect=w/16:h/9:5*w/16:2*h/9,swaprect=w/16:h/9:w/8:8*h/9,swaprect=w/16:h/9:7*w/16:4*h/9,swaprect=w/16:h/9:5*w/8:h/9,swaprect=w/16:h/9:9*w/16:2*h/9,swaprect=w/16:h/9:11*w/16:7*h/9,swaprect=w/16:h/9:w/4:0\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "swaprect=w/16:h/9:w/16:5*h/9,swaprect=w/16:h/9:7*w/16:2*h/9,swaprect=w/16:h/9:w/2:8*h/9,swaprect=w/16:h/9:7*w/8:h/3,swaprect=w/16:h/9:13*w/16:8*h/9,swaprect=w/16:h/9:3*w/4:h/9,swaprect=w/16:h/9:5*w/8:h/3,swaprect=w/16:h/9:9*w/16:5*h/9,swaprect=w/16:h/9:w/2:h/3,swaprect=w/16:h/9:0:8*h/9,swaprect=w/16:h/9:3*w/8:2*h/9,swaprect=w/16:h/9:5*w/8:5*h/9,swaprect=w/16:h/9:11*w/16:8*h/9,swaprect=w/16:h/9:0:4*h/9,swaprect=w/16:h/9:11*w/16:h/3,swaprect=w/16:h/9:7*w/8:7*h/9,swaprect=w/16:h/9:15*w/16:4*h/9,swaprect=w/16:h/9:3*w/16:7*h/9,swaprect=w/16:h/9:5*w/16:h/9,swaprect=w/16:h/9:13*w/16:h/9,swaprect=w/16:h/9:9*w/16:4*h/9,swaprect=w/16:h/9:w/4:2*h/9,swaprect=w/16:h/9:w/8:4*h/9,swaprect=w/16:h/9:9*w/16:2*h/3,swaprect=w/16:h/9:5*w/16:h/3,swaprect=w/16:h/9:w/16:8*h/9,swaprect=w/16:h/9:3*w/4:2*h/3,swaprect=w/16:h/9:3*w/4:0,swaprect=w/16:h/9:5*w/16:7*h/9,swaprect=w/16:h/9:13*w/16:2*h/9,swaprect=w/16:h/9:5*w/16:2*h/3,swaprect=w/16:h/9:5*w/8:4*h/9,swaprect=w/16:h/9:w/4:2*h/3,swaprect=w/16:h/9:3*w/8:4*h/9,swaprect=w/16:h/9:w/4:5*h/9,swaprect=w/16:h/9:7*w/8:5*h/9,swaprect=w/16:h/9:w/2:2*h/3,swaprect=w/16:h/9:13*w/16:0,swaprect=w/16:h/9:w/16:2*h/9,swaprect=w/16:h/9:11*w/16:4*h/9,swaprect=w/16:h/9:7*w/8:4*h/9,swaprect=w/16:h/9:3*w/16:4*h/9,swaprect=w/16:h/9:0:5*h/9,swaprect=w/16:h/9:3*w/8:8*h/9,swaprect=w/16:h/9:0:h/3,swaprect=w/16:h/9:w/16:7*h/9,swaprect=w/16:h/9:9*w/16:7*h/9,swaprect=w/16:h/9:3*w/8:7*h/9,swaprect=w/16:h/9:w/4:7*h/9,swaprect=w/16:h/9:w/8:2*h/3,swaprect=w/16:h/9:w/2:0,swaprect=w/16:h/9:0:2*h/9,swaprect=w/16:h/9:3*w/4:2*h/9,swaprect=w/16:h/9:5*w/8:0,swaprect=w/16:h/9:3*w/8:h/3,swaprect=w/16:h/9:7*w/16:h/3,swaprect=w/16:h/9:9*w/16:h/3,swaprect=w/16:h/9:w/8:h/3,swaprect=w/16:h/9:3*w/8:5*h/9,swaprect=w/16:h/9:3*w/16:5*h/9,swaprect=w/16:h/9:5*w/16:0,swaprect=w/16:h/9:11*w/16:2*h/9,swaprect=w/16:h/9:w/8:2*h/9,swaprect=w/16:h/9:w/16:h/3,swaprect=w/16:h/9:3*w/16:8*h/9,swaprect=w/16:h/9:w/4:h/9,swaprect=w/16:h/9:7*w/8:2*h/3,swaprect=w/16:h/9:13*w/16:5*h/9,swaprect=w/16:h/9:7*w/8:0,swaprect=w/16:h/9:3*w/16:h/9,swaprect=w/16:h/9:3*w/4:h/3,swaprect=w/16:h/9:7*w/16:h/9,swaprect=w/16:h/9:15*w/16:7*h/9,swaprect=w/16:h/9:7*w/16:5*h/9,swaprect=w/16:h/9:15*w/16:5*h/9,swaprect=w/16:h/9:15*w/16:0,swaprect=w/16:h/9:0:h/9,swaprect=w/16:h/9:11*w/16:2*h/3,swaprect=w/16:h/9:w/2:2*h/9,swaprect=w/16:h/9:7*w/8:h/9,swaprect=w/16:h/9:9*w/16:8*h/9,swaprect=w/16:h/9:3*w/4:8*h/9,swaprect=w/16:h/9:w/2:4*h/9,swaprect=w/16:h/9:0:7*h/9,swaprect=w/16:h/9:w/8:0,swaprect=w/16:h/9:13*w/16:2*h/3,swaprect=w/16:h/9:11*w/16:0,swaprect=w/16:h/9:w/2:5*h/9,swaprect=w/16:h/9:15*w/16:8*h/9,swaprect=w/16:h/9:w/4:h/3,swaprect=w/16:h/9:13*w/16:h/3,swaprect=w/16:h/9:3*w/16:2*h/9,swaprect=w/16:h/9:7*w/16:8*h/9,swaprect=w/16:h/9:3*w/8:2*h/3,swaprect=w/16:h/9:15*w/16:h/3,swaprect=w/16:h/9:w/16:2*h/3,swaprect=w/16:h/9:0:0,swaprect=w/16:h/9:w/2:7*h/9,swaprect=w/16:h/9:5*w/8:7*h/9,swaprect=w/16:h/9:5*w/8:8*h/9,swaprect=w/16:h/9:w/16:0,swaprect=w/16:h/9:w/8:h/9,swaprect=w/16:h/9:5*w/16:5*h/9,swaprect=w/16:h/9:w/8:5*h/9,swaprect=w/16:h/9:3*w/16:2*h/3,swaprect=w/16:h/9:w/4:8*h/9,swaprect=w/16:h/9:9*w/16:0,swaprect=w/16:h/9:5*w/8:2*h/3,swaprect=w/16:h/9:7*w/8:2*h/9,swaprect=w/16:h/9:3*w/4:4*h/9,swaprect=w/16:h/9:7*w/16:7*h/9,swaprect=w/16:h/9:w/16:4*h/9,swaprect=w/16:h/9:5*w/16:8*h/9,swaprect=w/16:h/9:5*w/16:4*h/9,swaprect=w/16:h/9:13*w/16:4*h/9,swaprect=w/16:h/9:7*w/16:0,swaprect=w/16:h/9:w/16:h/9,swaprect=w/16:h/9:15*w/16:h/9,swaprect=w/16:h/9:0:2*h/3,swaprect=w/16:h/9:9*w/16:h/9,swaprect=w/16:h/9:13*w/16:7*h/9,swaprect=w/16:h/9:15*w/16:2*h/3,swaprect=w/16:h/9:3*w/16:0,swaprect=w/16:h/9:3*w/8:h/9,swaprect=w/16:h/9:3*w/4:5*h/9,swaprect=w/16:h/9:3*w/4:7*h/9,swaprect=w/16:h/9:7*w/8:8*h/9,swaprect=w/16:h/9:7*w/16:2*h/3,swaprect=w/16:h/9:w/4:4*h/9,swaprect=w/16:h/9:3*w/8:0,swaprect=w/16:h/9:3*w/16:h/3,swaprect=w/16:h/9:w/8:7*h/9,swaprect=w/16:h/9:11*w/16:h/9,swaprect=w/16:h/9:5*w/8:2*h/9,swaprect=w/16:h/9:15*w/16:2*h/9,swaprect=w/16:h/9:11*w/16:5*h/9,swaprect=w/16:h/9:w/2:h/9,swaprect=w/16:h/9:5*w/16:2*h/9,swaprect=w/16:h/9:w/8:8*h/9,swaprect=w/16:h/9:7*w/16:4*h/9,swaprect=w/16:h/9:5*w/8:h/9,swaprect=w/16:h/9:9*w/16:2*h/9,swaprect=w/16:h/9:11*w/16:7*h/9,swaprect=w/16:h/9:w/4:0", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case SWAP_RECT_SIX:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"swaprect=w/16:h/9:w/4:0,swaprect=w/16:h/9:11*w/16:7*h/9,swaprect=w/16:h/9:9*w/16:2*h/9,swaprect=w/16:h/9:5*w/8:h/9,swaprect=w/16:h/9:7*w/16:4*h/9,swaprect=w/16:h/9:w/8:8*h/9,swaprect=w/16:h/9:5*w/16:2*h/9,swaprect=w/16:h/9:w/2:h/9,swaprect=w/16:h/9:11*w/16:5*h/9,swaprect=w/16:h/9:15*w/16:2*h/9,swaprect=w/16:h/9:5*w/8:2*h/9,swaprect=w/16:h/9:11*w/16:h/9,swaprect=w/16:h/9:w/8:7*h/9,swaprect=w/16:h/9:3*w/16:h/3,swaprect=w/16:h/9:3*w/8:0,swaprect=w/16:h/9:w/4:4*h/9,swaprect=w/16:h/9:7*w/16:2*h/3,swaprect=w/16:h/9:7*w/8:8*h/9,swaprect=w/16:h/9:3*w/4:7*h/9,swaprect=w/16:h/9:3*w/4:5*h/9,swaprect=w/16:h/9:3*w/8:h/9,swaprect=w/16:h/9:3*w/16:0,swaprect=w/16:h/9:15*w/16:2*h/3,swaprect=w/16:h/9:13*w/16:7*h/9,swaprect=w/16:h/9:9*w/16:h/9,swaprect=w/16:h/9:0:2*h/3,swaprect=w/16:h/9:15*w/16:h/9,swaprect=w/16:h/9:w/16:h/9,swaprect=w/16:h/9:7*w/16:0,swaprect=w/16:h/9:13*w/16:4*h/9,swaprect=w/16:h/9:5*w/16:4*h/9,swaprect=w/16:h/9:5*w/16:8*h/9,swaprect=w/16:h/9:w/16:4*h/9,swaprect=w/16:h/9:7*w/16:7*h/9,swaprect=w/16:h/9:3*w/4:4*h/9,swaprect=w/16:h/9:7*w/8:2*h/9,swaprect=w/16:h/9:5*w/8:2*h/3,swaprect=w/16:h/9:9*w/16:0,swaprect=w/16:h/9:w/4:8*h/9,swaprect=w/16:h/9:3*w/16:2*h/3,swaprect=w/16:h/9:w/8:5*h/9,swaprect=w/16:h/9:5*w/16:5*h/9,swaprect=w/16:h/9:w/8:h/9,swaprect=w/16:h/9:w/16:0,swaprect=w/16:h/9:5*w/8:8*h/9,swaprect=w/16:h/9:5*w/8:7*h/9,swaprect=w/16:h/9:w/2:7*h/9,swaprect=w/16:h/9:0:0,swaprect=w/16:h/9:w/16:2*h/3,swaprect=w/16:h/9:15*w/16:h/3,swaprect=w/16:h/9:3*w/8:2*h/3,swaprect=w/16:h/9:7*w/16:8*h/9,swaprect=w/16:h/9:3*w/16:2*h/9,swaprect=w/16:h/9:13*w/16:h/3,swaprect=w/16:h/9:w/4:h/3,swaprect=w/16:h/9:15*w/16:8*h/9,swaprect=w/16:h/9:w/2:5*h/9,swaprect=w/16:h/9:11*w/16:0,swaprect=w/16:h/9:13*w/16:2*h/3,swaprect=w/16:h/9:w/8:0,swaprect=w/16:h/9:0:7*h/9,swaprect=w/16:h/9:w/2:4*h/9,swaprect=w/16:h/9:3*w/4:8*h/9,swaprect=w/16:h/9:9*w/16:8*h/9,swaprect=w/16:h/9:7*w/8:h/9,swaprect=w/16:h/9:w/2:2*h/9,swaprect=w/16:h/9:11*w/16:2*h/3,swaprect=w/16:h/9:0:h/9,swaprect=w/16:h/9:15*w/16:0,swaprect=w/16:h/9:15*w/16:5*h/9,swaprect=w/16:h/9:7*w/16:5*h/9,swaprect=w/16:h/9:15*w/16:7*h/9,swaprect=w/16:h/9:7*w/16:h/9,swaprect=w/16:h/9:3*w/4:h/3,swaprect=w/16:h/9:3*w/16:h/9,swaprect=w/16:h/9:7*w/8:0,swaprect=w/16:h/9:13*w/16:5*h/9,swaprect=w/16:h/9:7*w/8:2*h/3,swaprect=w/16:h/9:w/4:h/9,swaprect=w/16:h/9:3*w/16:8*h/9,swaprect=w/16:h/9:w/16:h/3,swaprect=w/16:h/9:w/8:2*h/9,swaprect=w/16:h/9:11*w/16:2*h/9,swaprect=w/16:h/9:5*w/16:0,swaprect=w/16:h/9:3*w/16:5*h/9,swaprect=w/16:h/9:3*w/8:5*h/9,swaprect=w/16:h/9:w/8:h/3,swaprect=w/16:h/9:9*w/16:h/3,swaprect=w/16:h/9:7*w/16:h/3,swaprect=w/16:h/9:3*w/8:h/3,swaprect=w/16:h/9:5*w/8:0,swaprect=w/16:h/9:3*w/4:2*h/9,swaprect=w/16:h/9:0:2*h/9,swaprect=w/16:h/9:w/2:0,swaprect=w/16:h/9:w/8:2*h/3,swaprect=w/16:h/9:w/4:7*h/9,swaprect=w/16:h/9:3*w/8:7*h/9,swaprect=w/16:h/9:9*w/16:7*h/9,swaprect=w/16:h/9:w/16:7*h/9,swaprect=w/16:h/9:0:h/3,swaprect=w/16:h/9:3*w/8:8*h/9,swaprect=w/16:h/9:0:5*h/9,swaprect=w/16:h/9:3*w/16:4*h/9,swaprect=w/16:h/9:7*w/8:4*h/9,swaprect=w/16:h/9:11*w/16:4*h/9,swaprect=w/16:h/9:w/16:2*h/9,swaprect=w/16:h/9:13*w/16:0,swaprect=w/16:h/9:w/2:2*h/3,swaprect=w/16:h/9:7*w/8:5*h/9,swaprect=w/16:h/9:w/4:5*h/9,swaprect=w/16:h/9:3*w/8:4*h/9,swaprect=w/16:h/9:w/4:2*h/3,swaprect=w/16:h/9:5*w/8:4*h/9,swaprect=w/16:h/9:5*w/16:2*h/3,swaprect=w/16:h/9:13*w/16:2*h/9,swaprect=w/16:h/9:5*w/16:7*h/9,swaprect=w/16:h/9:3*w/4:0,swaprect=w/16:h/9:3*w/4:2*h/3,swaprect=w/16:h/9:w/16:8*h/9,swaprect=w/16:h/9:5*w/16:h/3,swaprect=w/16:h/9:9*w/16:2*h/3,swaprect=w/16:h/9:w/8:4*h/9,swaprect=w/16:h/9:w/4:2*h/9,swaprect=w/16:h/9:9*w/16:4*h/9,swaprect=w/16:h/9:13*w/16:h/9,swaprect=w/16:h/9:5*w/16:h/9,swaprect=w/16:h/9:3*w/16:7*h/9,swaprect=w/16:h/9:15*w/16:4*h/9,swaprect=w/16:h/9:7*w/8:7*h/9,swaprect=w/16:h/9:11*w/16:h/3,swaprect=w/16:h/9:0:4*h/9,swaprect=w/16:h/9:11*w/16:8*h/9,swaprect=w/16:h/9:5*w/8:5*h/9,swaprect=w/16:h/9:3*w/8:2*h/9,swaprect=w/16:h/9:0:8*h/9,swaprect=w/16:h/9:w/2:h/3,swaprect=w/16:h/9:9*w/16:5*h/9,swaprect=w/16:h/9:5*w/8:h/3,swaprect=w/16:h/9:3*w/4:h/9,swaprect=w/16:h/9:13*w/16:8*h/9,swaprect=w/16:h/9:7*w/8:h/3,swaprect=w/16:h/9:w/2:8*h/9,swaprect=w/16:h/9:7*w/16:2*h/9,swaprect=w/16:h/9:w/16:5*h/9\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "swaprect=w/16:h/9:w/4:0,swaprect=w/16:h/9:11*w/16:7*h/9,swaprect=w/16:h/9:9*w/16:2*h/9,swaprect=w/16:h/9:5*w/8:h/9,swaprect=w/16:h/9:7*w/16:4*h/9,swaprect=w/16:h/9:w/8:8*h/9,swaprect=w/16:h/9:5*w/16:2*h/9,swaprect=w/16:h/9:w/2:h/9,swaprect=w/16:h/9:11*w/16:5*h/9,swaprect=w/16:h/9:15*w/16:2*h/9,swaprect=w/16:h/9:5*w/8:2*h/9,swaprect=w/16:h/9:11*w/16:h/9,swaprect=w/16:h/9:w/8:7*h/9,swaprect=w/16:h/9:3*w/16:h/3,swaprect=w/16:h/9:3*w/8:0,swaprect=w/16:h/9:w/4:4*h/9,swaprect=w/16:h/9:7*w/16:2*h/3,swaprect=w/16:h/9:7*w/8:8*h/9,swaprect=w/16:h/9:3*w/4:7*h/9,swaprect=w/16:h/9:3*w/4:5*h/9,swaprect=w/16:h/9:3*w/8:h/9,swaprect=w/16:h/9:3*w/16:0,swaprect=w/16:h/9:15*w/16:2*h/3,swaprect=w/16:h/9:13*w/16:7*h/9,swaprect=w/16:h/9:9*w/16:h/9,swaprect=w/16:h/9:0:2*h/3,swaprect=w/16:h/9:15*w/16:h/9,swaprect=w/16:h/9:w/16:h/9,swaprect=w/16:h/9:7*w/16:0,swaprect=w/16:h/9:13*w/16:4*h/9,swaprect=w/16:h/9:5*w/16:4*h/9,swaprect=w/16:h/9:5*w/16:8*h/9,swaprect=w/16:h/9:w/16:4*h/9,swaprect=w/16:h/9:7*w/16:7*h/9,swaprect=w/16:h/9:3*w/4:4*h/9,swaprect=w/16:h/9:7*w/8:2*h/9,swaprect=w/16:h/9:5*w/8:2*h/3,swaprect=w/16:h/9:9*w/16:0,swaprect=w/16:h/9:w/4:8*h/9,swaprect=w/16:h/9:3*w/16:2*h/3,swaprect=w/16:h/9:w/8:5*h/9,swaprect=w/16:h/9:5*w/16:5*h/9,swaprect=w/16:h/9:w/8:h/9,swaprect=w/16:h/9:w/16:0,swaprect=w/16:h/9:5*w/8:8*h/9,swaprect=w/16:h/9:5*w/8:7*h/9,swaprect=w/16:h/9:w/2:7*h/9,swaprect=w/16:h/9:0:0,swaprect=w/16:h/9:w/16:2*h/3,swaprect=w/16:h/9:15*w/16:h/3,swaprect=w/16:h/9:3*w/8:2*h/3,swaprect=w/16:h/9:7*w/16:8*h/9,swaprect=w/16:h/9:3*w/16:2*h/9,swaprect=w/16:h/9:13*w/16:h/3,swaprect=w/16:h/9:w/4:h/3,swaprect=w/16:h/9:15*w/16:8*h/9,swaprect=w/16:h/9:w/2:5*h/9,swaprect=w/16:h/9:11*w/16:0,swaprect=w/16:h/9:13*w/16:2*h/3,swaprect=w/16:h/9:w/8:0,swaprect=w/16:h/9:0:7*h/9,swaprect=w/16:h/9:w/2:4*h/9,swaprect=w/16:h/9:3*w/4:8*h/9,swaprect=w/16:h/9:9*w/16:8*h/9,swaprect=w/16:h/9:7*w/8:h/9,swaprect=w/16:h/9:w/2:2*h/9,swaprect=w/16:h/9:11*w/16:2*h/3,swaprect=w/16:h/9:0:h/9,swaprect=w/16:h/9:15*w/16:0,swaprect=w/16:h/9:15*w/16:5*h/9,swaprect=w/16:h/9:7*w/16:5*h/9,swaprect=w/16:h/9:15*w/16:7*h/9,swaprect=w/16:h/9:7*w/16:h/9,swaprect=w/16:h/9:3*w/4:h/3,swaprect=w/16:h/9:3*w/16:h/9,swaprect=w/16:h/9:7*w/8:0,swaprect=w/16:h/9:13*w/16:5*h/9,swaprect=w/16:h/9:7*w/8:2*h/3,swaprect=w/16:h/9:w/4:h/9,swaprect=w/16:h/9:3*w/16:8*h/9,swaprect=w/16:h/9:w/16:h/3,swaprect=w/16:h/9:w/8:2*h/9,swaprect=w/16:h/9:11*w/16:2*h/9,swaprect=w/16:h/9:5*w/16:0,swaprect=w/16:h/9:3*w/16:5*h/9,swaprect=w/16:h/9:3*w/8:5*h/9,swaprect=w/16:h/9:w/8:h/3,swaprect=w/16:h/9:9*w/16:h/3,swaprect=w/16:h/9:7*w/16:h/3,swaprect=w/16:h/9:3*w/8:h/3,swaprect=w/16:h/9:5*w/8:0,swaprect=w/16:h/9:3*w/4:2*h/9,swaprect=w/16:h/9:0:2*h/9,swaprect=w/16:h/9:w/2:0,swaprect=w/16:h/9:w/8:2*h/3,swaprect=w/16:h/9:w/4:7*h/9,swaprect=w/16:h/9:3*w/8:7*h/9,swaprect=w/16:h/9:9*w/16:7*h/9,swaprect=w/16:h/9:w/16:7*h/9,swaprect=w/16:h/9:0:h/3,swaprect=w/16:h/9:3*w/8:8*h/9,swaprect=w/16:h/9:0:5*h/9,swaprect=w/16:h/9:3*w/16:4*h/9,swaprect=w/16:h/9:7*w/8:4*h/9,swaprect=w/16:h/9:11*w/16:4*h/9,swaprect=w/16:h/9:w/16:2*h/9,swaprect=w/16:h/9:13*w/16:0,swaprect=w/16:h/9:w/2:2*h/3,swaprect=w/16:h/9:7*w/8:5*h/9,swaprect=w/16:h/9:w/4:5*h/9,swaprect=w/16:h/9:3*w/8:4*h/9,swaprect=w/16:h/9:w/4:2*h/3,swaprect=w/16:h/9:5*w/8:4*h/9,swaprect=w/16:h/9:5*w/16:2*h/3,swaprect=w/16:h/9:13*w/16:2*h/9,swaprect=w/16:h/9:5*w/16:7*h/9,swaprect=w/16:h/9:3*w/4:0,swaprect=w/16:h/9:3*w/4:2*h/3,swaprect=w/16:h/9:w/16:8*h/9,swaprect=w/16:h/9:5*w/16:h/3,swaprect=w/16:h/9:9*w/16:2*h/3,swaprect=w/16:h/9:w/8:4*h/9,swaprect=w/16:h/9:w/4:2*h/9,swaprect=w/16:h/9:9*w/16:4*h/9,swaprect=w/16:h/9:13*w/16:h/9,swaprect=w/16:h/9:5*w/16:h/9,swaprect=w/16:h/9:3*w/16:7*h/9,swaprect=w/16:h/9:15*w/16:4*h/9,swaprect=w/16:h/9:7*w/8:7*h/9,swaprect=w/16:h/9:11*w/16:h/3,swaprect=w/16:h/9:0:4*h/9,swaprect=w/16:h/9:11*w/16:8*h/9,swaprect=w/16:h/9:5*w/8:5*h/9,swaprect=w/16:h/9:3*w/8:2*h/9,swaprect=w/16:h/9:0:8*h/9,swaprect=w/16:h/9:w/2:h/3,swaprect=w/16:h/9:9*w/16:5*h/9,swaprect=w/16:h/9:5*w/8:h/3,swaprect=w/16:h/9:3*w/4:h/9,swaprect=w/16:h/9:13*w/16:8*h/9,swaprect=w/16:h/9:7*w/8:h/3,swaprect=w/16:h/9:w/2:8*h/9,swaprect=w/16:h/9:7*w/16:2*h/9,swaprect=w/16:h/9:w/16:5*h/9", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case NOISE:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -bsf:v noise=100000000 " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-bsf:v", "noise=100000000", outputPath};
                break;
            case CRAZY:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"rotate=PI/6,stereo3d=abl:sbsr,stereo3d=sbsl:aybd,split [main][tmp]; [tmp] crop=iw:ih/2:0:0, vflip [flip]; [main][flip] overlay=0:H/2,crop=iw/2:ih:0:0,split[left][tmp];[tmp]hflip[right];[left][right] hstack\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "rotate=PI/6,stereo3d=abl:sbsr,stereo3d=sbsl:aybd,split [main][tmp]; [tmp] crop=iw:ih/2:0:0, vflip [flip]; [main][flip] overlay=0:H/2,crop=iw/2:ih:0:0,split[left][tmp];[tmp]hflip[right];[left][right] hstack", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case MIRROR:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"split [main][tmp]; [tmp] crop=iw:ih/2:0:0, vflip [flip]; [main][flip] overlay=0:H/2\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "split [main][tmp]; [tmp] crop=iw:ih/2:0:0, vflip [flip]; [main][flip] overlay=0:H/2", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case CARTOON:
                String umetak = "y=val*3";
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"split [main][tmp]; [tmp] lutyuv=" + umetak + " [tmp2]; [main][tmp2] overlay\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "split [main][tmp]; [tmp] lutyuv=" + umetak + " [tmp2]; [main][tmp2] overlay", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
            case ZOOMED:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"scale=2*iw:-1, crop=iw/2:ih/2\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "scale=2*iw:-1, crop=iw/2:ih/2", "-c:a", "copy", outputPath};
                break;
            case VINTAGE:
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"curves=vintage\" -pix_fmt yuv420p -y -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "curves=vintage", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
//                command = new String[]{"-i", inputPath, "-vf", "curves=vintage", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", outputPath};
                break;
//            case FADE_IN:
//                break;
//            case FADE_OUT:
//                break;
//
            case GLITCH:
            case GLITCH_FILTER_0:
                // Digital glitch effect using datamosh-style corruption
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"rgbashift=rh=5:rv=10:gh=-5:gv=-10:bh=0:bv=5\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "rgbashift=rh=5:rv=10:gh=-5:gv=-10:bh=0:bv=5", "-c:a", "copy", outputPath};
                break;
            case GLITCH_FILTER_1:
                // Chromatic aberration effect
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"split=3[red][green][blue];[red]lutrgb=g=0:b=0,geq='r(X+5,Y)':g=0:b=0[red1];[green]lutrgb=r=0:b=0,geq=r=0:'g(X-5,Y)':b=0[green1];[blue]lutrgb=r=0:g=0,geq=r=0:g=0:'b(X,Y+5)'[blue1];[red1][green1]blend=all_mode=addition[rg];[rg][blue1]blend=all_mode=addition\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "split=3[red][green][blue];[red]lutrgb=g=0:b=0,geq='r(X+5,Y)':g=0:b=0[red1];[green]lutrgb=r=0:b=0,geq=r=0:'g(X-5,Y)':b=0[green1];[blue]lutrgb=r=0:g=0,geq=r=0:g=0:'b(X,Y+5)'[blue1];[red1][green1]blend=all_mode=addition[rg];[rg][blue1]blend=all_mode=addition", "-c:a", "copy", outputPath};
                break;
            case GLITCH_FILTER_2:
                // Scanline effect
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"split[a][b];[a]field=top[a1];[b]field=bottom[b1];[a1][b1]blend=all_expr='if(mod(Y,4),A,B)'\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "split[a][b];[a]field=top[a1];[b]field=bottom[b1];[a1][b1]blend=all_expr='if(mod(Y,4),A,B)'", "-c:a", "copy", outputPath};
                break;
            case GLITCH_FILTER_3:
                // Pixelation glitch
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"scale=iw/8:ih/8,scale=8*iw:8*ih:flags=neighbor\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "scale=iw/8:ih/8,scale=8*iw:8*ih:flags=neighbor", "-c:a", "copy", outputPath};
                break;
            case GLITCH_FILTER_4:
                // VHS tracking error effect
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"hue=s=0.7,curves=preset=darker,rgbashift=rh=2:bv=-2\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "hue=s=0.7,curves=preset=darker,rgbashift=rh=2:bv=-2", "-c:a", "copy", outputPath};
                break;
            case GLITCH_FILTER_5:
                // Displacement map glitch
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"split[main][disp];[disp]scale=iw:ih,format=gray,geq='lum=mod(X+Y,10)*25'[disp2];[main][disp2]displace=edge=wrap\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "split[main][disp];[disp]scale=iw:ih,format=gray,geq='lum=mod(X+Y,10)*25'[disp2];[main][disp2]displace=edge=wrap", "-c:a", "copy", outputPath};
                break;
            case GLITCH_FILTER_6:
                // Color channel shift
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"rgbashift=rh=-10:gv=10:bh=5:bv=-5,eq=contrast=1.2\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "rgbashift=rh=-10:gv=10:bh=5:bv=-5,eq=contrast=1.2", "-c:a", "copy", outputPath};
                break;
            case GLITCH_FILTER_7:
                // Datamosh effect simulation
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"minterpolate=fps=25:mi_mode=mci:me_mode=bidir:scd=none,select='not(mod(n,5))',setpts=N/FRAME_RATE/TB\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "minterpolate=fps=25:mi_mode=mci:me_mode=bidir:scd=none,select='not(mod(n,5))',setpts=N/FRAME_RATE/TB", "-c:a", "copy", outputPath};
                break;
            case GLITCH_FILTER_8:
                // Block distortion
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"crop=iw:ih/4:0:0,split=4[a][b][c][d];[a]crop=iw/4:ih:0:0[a1];[b]crop=iw/4:ih:iw/4:0,hflip[b1];[c]crop=iw/4:ih:iw/2:0,negate[c1];[d]crop=iw/4:ih:3*iw/4:0[d1];[a1][b1]hstack[ab];[c1][d1]hstack[cd];[ab][cd]hstack\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "crop=iw:ih/4:0:0,split=4[a][b][c][d];[a]crop=iw/4:ih:0:0[a1];[b]crop=iw/4:ih:iw/4:0,hflip[b1];[c]crop=iw/4:ih:iw/2:0,negate[c1];[d]crop=iw/4:ih:3*iw/4:0[d1];[a1][b1]hstack[ab];[c1][d1]hstack[cd];[ab][cd]hstack", "-c:a", "copy", outputPath};
                break;
            case GLITCH_FILTER_9:
                // Interlacing artifact
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"tinterlace=mode=interlacex2,curves=preset=vintage\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "tinterlace=mode=interlacex2,curves=preset=vintage", "-c:a", "copy", outputPath};
                break;
            case GLITCH_FILTER_10:
                // RGB noise glitch
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"noise=alls=20:allf=t+u,rgbashift=rh=3:gh=-3\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "noise=alls=20:allf=t+u,rgbashift=rh=3:gh=-3", "-c:a", "copy", outputPath};
                break;
            case GLITCH_FILTER_11:
                // Horizontal displacement
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"geq='p(X+sin(Y/10)*10,Y)'\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "geq='p(X+sin(Y/10)*10,Y)'", "-c:a", "copy", outputPath};
                break;
            case GLITCH_FILTER_12:
                // Color posterization
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"format=rgb24,geq='r=floor(r(X,Y)/64)*64:g=floor(g(X,Y)/64)*64:b=floor(b(X,Y)/64)*64',format=yuv420p\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "format=rgb24,geq='r=floor(r(X,Y)/64)*64:g=floor(g(X,Y)/64)*64:b=floor(b(X,Y)/64)*64',format=yuv420p", "-c:a", "copy", outputPath};
                break;
            case GLITCH_FILTER_13:
                // Mosaic glitch
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"scale=iw/16:ih/16,scale=16*iw:16*ih:flags=neighbor,rgbashift=rv=5:gv=-5\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "scale=iw/16:ih/16,scale=16*iw:16*ih:flags=neighbor,rgbashift=rv=5:gv=-5", "-c:a", "copy", outputPath};
                break;
            case GLITCH_FILTER_14:
                // Frame skip glitch
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"select='not(mod(n,3))',setpts=N/FRAME_RATE/TB,rgbashift=rh=2:bv=2\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "select='not(mod(n,3))',setpts=N/FRAME_RATE/TB,rgbashift=rh=2:bv=2", "-c:a", "copy", outputPath};
                break;
            case GLITCH_FILTER_15:
                // Digital artifact
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -vf \"edgedetect=mode=colormix:high=0.1,eq=brightness=0.2:saturation=2\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-vf", "edgedetect=mode=colormix:high=0.1,eq=brightness=0.2:saturation=2", "-c:a", "copy", outputPath};
                break;
            case GLITCH_FILTER_16:
                // Compression artifact simulation
                if (isNewApiRequired())
                    commandNewApi = "-i " + inputPath + " -preset ultrafast -b:v 50k -vf \"noise=alls=10:allf=t,unsharp=5:5:1.0:5:5:0.0\" -c:a copy " + outputPath;
                else
                    command = new String[]{"-i", inputPath, "-preset", "ultrafast", "-b:v", "50k", "-vf", "noise=alls=10:allf=t,unsharp=5:5:1.0:5:5:0.0", "-c:a", "copy", outputPath};
                break;
            default:
                command = new String[]{};
        }

        return isNewApiRequired() ? (T) commandNewApi : (T) command;
    }

    public static ArrayList<ComModel> getListOfCommands() {
        ArrayList<ComModel> animalNames = new ArrayList<>();

        if (animalNames.size() == 0) {
            animalNames.add(new ComModel(ComModel.DEFAULT, R.drawable.input));
//            animalNames.add(new ComModel(ComModel.THRESHOLD, R.drawable.threshold));
//            animalNames.add(new ComModel(ComModel.CROSS_FADE, R.drawable.input));
            animalNames.add(new ComModel(ComModel.REVERSE_VIDEO, R.drawable.backward));
            animalNames.add(new ComModel(ComModel.SEPIA, R.drawable.sepia));
            animalNames.add(new ComModel(ComModel.BLACK_AND_WHITE, R.drawable.black_and_white));
            animalNames.add(new ComModel(ComModel.BOOMERANG, R.drawable.input));
            animalNames.add(new ComModel(ComModel.VINTAGE, R.drawable.vintage));
            animalNames.add(new ComModel(ComModel.GAMMA_CORRECTION, R.drawable.gamma_correction));
            animalNames.add(new ComModel(ComModel.COLOR_BALANCE, R.drawable.color_balance));
            animalNames.add(new ComModel(ComModel.COLOR_BALANCE_TWO, R.drawable.color_balance_two));
            animalNames.add(new ComModel(ComModel.SHARPER, R.drawable.input));
            animalNames.add(new ComModel(ComModel.FADE_IN, R.drawable.fade_in));
            animalNames.add(new ComModel(ComModel.FADE_OUT, R.drawable.fade_out));
            animalNames.add(new ComModel(ComModel.GLITCH, R.drawable.glitch));
            animalNames.add(new ComModel(ComModel.PENCIL_SKETCH_1, R.drawable.sketch_1_thumb));
            animalNames.add(new ComModel(ComModel.PENCIL_SKETCH_2, R.drawable.sketch_2_thumb));
            animalNames.add(new ComModel(ComModel.PENCIL_SKETCH_3, R.drawable.sketch_3_thumb));
            animalNames.add(new ComModel(ComModel.PENCIL_SKETCH_4, R.drawable.sketch_4_thumb));
            animalNames.add(new ComModel(ComModel.PENCIL_SKETCH_5, R.drawable.sketch_5_thumb));
            animalNames.add(new ComModel(ComModel.OIL_PAINT, R.drawable.oil_paint));
            animalNames.add(new ComModel(ComModel.CARTOON, R.drawable.cartoon));
            animalNames.add(new ComModel(ComModel.GLITCH_FILTER_0, R.drawable.glitch_filter_0));
            animalNames.add(new ComModel(ComModel.GLITCH_FILTER_1, R.drawable.glitch_filter_1));
            animalNames.add(new ComModel(ComModel.GLITCH_FILTER_2, R.drawable.glitch_filter_2));
            animalNames.add(new ComModel(ComModel.GLITCH_FILTER_3, R.drawable.glitch_filter_3));
            animalNames.add(new ComModel(ComModel.GLITCH_FILTER_4, R.drawable.glitch_filter_4));
            animalNames.add(new ComModel(ComModel.GLITCH_FILTER_5, R.drawable.glitch_filter_5));
            animalNames.add(new ComModel(ComModel.GLITCH_FILTER_6, R.drawable.glitch_filter_6));
            animalNames.add(new ComModel(ComModel.GLITCH_FILTER_7, R.drawable.glitch_filter_7));
            animalNames.add(new ComModel(ComModel.GLITCH_FILTER_8, R.drawable.glitch_filter_8));
            animalNames.add(new ComModel(ComModel.GLITCH_FILTER_9, R.drawable.glitch_filter_9));
            animalNames.add(new ComModel(ComModel.GLITCH_FILTER_10, R.drawable.glitch_filter_10));
            animalNames.add(new ComModel(ComModel.GLITCH_FILTER_11, R.drawable.glitch_filter_11));
            animalNames.add(new ComModel(ComModel.GLITCH_FILTER_12, R.drawable.glitch_filter_12));
            animalNames.add(new ComModel(ComModel.GLITCH_FILTER_13, R.drawable.glitch_filter_13));
            animalNames.add(new ComModel(ComModel.GLITCH_FILTER_14, R.drawable.glitch_filter_14));
            animalNames.add(new ComModel(ComModel.GLITCH_FILTER_15, R.drawable.glitch_filter_15));
            animalNames.add(new ComModel(ComModel.GLITCH_FILTER_16, R.drawable.glitch_filter_16));
            animalNames.add(new ComModel(ComModel.ZOOM_IN_PROGRESSIVELY, R.drawable.zoomed));
            animalNames.add(new ComModel(ComModel.ZOOMED, R.drawable.zoomed));
            animalNames.add(new ComModel(ComModel.MIRROR, R.drawable.mirror));
            animalNames.add(new ComModel(ComModel.MIRROR_VERTICAL, R.drawable.mirror_vertical));
            animalNames.add(new ComModel(ComModel.FLIP_VERTICAL, R.drawable.flip_vertical));
            animalNames.add(new ComModel(ComModel.FLIP_HORIZONTAL, R.drawable.flip_horizontal));
            animalNames.add(new ComModel(ComModel.ROTATE_90_DEGREE_CLOCKWISE, R.drawable.rotate_90_degree_clockwise));
            animalNames.add(new ComModel(ComModel.ROTATE_90_DEGERE_COUNTERCLOCKWISE, R.drawable.rotate_90_degree_counterclockwise));
            animalNames.add(new ComModel(ComModel.BLUR, R.drawable.blur));
            animalNames.add(new ComModel(ComModel.CONVOLUTION, R.drawable.convolution));
            animalNames.add(new ComModel(ComModel.CONVOLUTION_2, R.drawable.convolution_2));
            animalNames.add(new ComModel(ComModel.STEREOSCOPIC, R.drawable.stereoscopic));
            animalNames.add(new ComModel(ComModel.PIXELIZE, R.drawable.pixelize));
            animalNames.add(new ComModel(ComModel.BRIGHTNESS_PLUS, R.drawable.brightness_plus));
            animalNames.add(new ComModel(ComModel.BRIGHTNESS_MINUS, R.drawable.brightness_minus));
            animalNames.add(new ComModel(ComModel.SATURATION_PLUS, R.drawable.saturation_plus));
            animalNames.add(new ComModel(ComModel.SATURATION_MINUS, R.drawable.saturation_minus));
            animalNames.add(new ComModel(ComModel.STABILIZATION, R.drawable.input));
            animalNames.add(new ComModel(ComModel.GAMMA_R_PLUS, R.drawable.gamma_r_plus));
            animalNames.add(new ComModel(ComModel.GAMMA_R_MINUS, R.drawable.gamma_r_minus));
            animalNames.add(new ComModel(ComModel.GAMMA_G_PLUS, R.drawable.gamma_g_plus));
            animalNames.add(new ComModel(ComModel.GAMMA_G_MINUS, R.drawable.gamma_g_minus));
            animalNames.add(new ComModel(ComModel.GAMMA_B_PLUS, R.drawable.gamma_b_plus));
            animalNames.add(new ComModel(ComModel.GAMMA_B_MINUS, R.drawable.gamma_b_minus));
            animalNames.add(new ComModel(ComModel.COLOR_NEGATIVE, R.drawable.color_negative));
            animalNames.add(new ComModel(ComModel.CROSS_PROCESS, R.drawable.cross_process));
            animalNames.add(new ComModel(ComModel.DARKER, R.drawable.darker));
            animalNames.add(new ComModel(ComModel.LIGHTER, R.drawable.lighter));
            animalNames.add(new ComModel(ComModel.INCREASE_CONTRAST, R.drawable.increase_contrast));
            animalNames.add(new ComModel(ComModel.LINEAR_CONTRAST, R.drawable.linear_contrast));
            animalNames.add(new ComModel(ComModel.MEDIUM_CONTRAST, R.drawable.medium_contrast));
            animalNames.add(new ComModel(ComModel.STRONG_CONTRAST, R.drawable.strong_contrast));
            animalNames.add(new ComModel(ComModel.NEGATIVE, R.drawable.negative));
            animalNames.add(new ComModel(ComModel.CURVES_BLUE, R.drawable.curves_blue));
            animalNames.add(new ComModel(ComModel.ENHANCED_LBG_2, R.drawable.enhanced_lbg_2));
            animalNames.add(new ComModel(ComModel.ENHANCED_LBG_4, R.drawable.enhanced_lbg_4));
            animalNames.add(new ComModel(ComModel.ENHANCED_LBG_8, R.drawable.enhanced_lbg_8));
            animalNames.add(new ComModel(ComModel.ENHANCED_LBG_16, R.drawable.enhanced_lbg_16));
//            animalNames.add(new ComModel(ComModel.DEBAND, R.drawable.deband));
            animalNames.add(new ComModel(ComModel.NOISE, R.drawable.noise));
//            animalNames.add(new ComModel(ComModel.CRAZY, R.drawable.crazy));
            animalNames.add(new ComModel(ComModel.SWAP_RECT_ONE, R.drawable.swaprect_one));
            animalNames.add(new ComModel(ComModel.SWAP_RECT_TWO, R.drawable.swaprect_two));
            animalNames.add(new ComModel(ComModel.SWAP_RECT_THREE, R.drawable.swaprect_three));
            animalNames.add(new ComModel(ComModel.SWAP_RECT_FOUR, R.drawable.swaprect_four));
            animalNames.add(new ComModel(ComModel.SWAP_RECT_FIVE, R.drawable.swaprect_five));
            animalNames.add(new ComModel(ComModel.SWAP_RECT_SIX, R.drawable.swaprect_six));
        }

        return animalNames;
    }

    public static boolean isNewApiRequired() {
//        return android.os.Build.VERSION.SDK_INT > Build.VERSION_CODES.P;
        return true;
    }

    public static String getCharEscaptedPath(String path) {
        if (isNewApiRequired())
            return "'" + path + "'";
        else return path;
    }

    public static String[] combine(String[] arg1, String[] arg2, String[] arg3) {
        String[] result = new String[arg1.length + arg2.length + arg3.length];
        System.arraycopy(arg1, 0, result, 0, arg1.length);
        System.arraycopy(arg2, 0, result, arg1.length, arg2.length);
        System.arraycopy(arg3, 0, result, arg1.length + arg2.length, arg3.length);
        return result;
    }


    public static final String DEFAULT = "Default";
    public static final String THRESHOLD = "Threshold";
    public static final String CROSS_FADE = "Cross fade";
    public static final String OIL_PAINT = "Oil Paint";
    public static final String MIRROR_VERTICAL = "Mirror vertical";
    public static final String GLITCH = "Glitch";
    public static final String GLITCH_FILTER_0 = "Glitch 0";
    public static final String GLITCH_FILTER_1 = "Glitch 1";
    public static final String GLITCH_FILTER_2 = "Glitch 2";
    public static final String GLITCH_FILTER_3 = "Glitch 3";
    public static final String GLITCH_FILTER_4 = "Glitch 4";
    public static final String GLITCH_FILTER_5 = "Glitch 5";
    public static final String GLITCH_FILTER_6 = "Glitch 6";
    public static final String GLITCH_FILTER_7 = "Glitch 7";
    public static final String GLITCH_FILTER_8 = "Glitch 8";
    public static final String GLITCH_FILTER_9 = "Glitch 9";
    public static final String GLITCH_FILTER_10 = "Glitch 10";
    public static final String GLITCH_FILTER_11 = "Glitch 11";
    public static final String GLITCH_FILTER_12 = "Glitch 12";
    public static final String GLITCH_FILTER_13 = "Glitch 13";
    public static final String GLITCH_FILTER_14 = "Glitch 14";
    public static final String GLITCH_FILTER_15 = "Glitch 15";
    public static final String GLITCH_FILTER_16 = "Glitch 16";
    public static final String REVERSE_VIDEO = "Reverse Video";
    public static final String PENCIL_SKETCH_1 = "Pencil Sketch 1";
    public static final String PENCIL_SKETCH_2 = "Pencil Sketch 2";
    public static final String PENCIL_SKETCH_3 = "Pencil Sketch 3";
    public static final String PENCIL_SKETCH_4 = "Pencil Sketch 4";
    public static final String PENCIL_SKETCH_5 = "Pencil Sketch 5";
    public static final String BOOMERANG = "Boomerang";
    public static final String BLACK_AND_WHITE = "Black And White";
    public static final String SEPIA = "Sepia";
    public static final String GAMMA_CORRECTION = "Gamma Correction";
    public static final String STABILIZATION = "Stabilization";
    public static final String SHARPER = "Sharper";
    public static final String ZOOM_IN_PROGRESSIVELY = "Zoom In Progressively";
    public static final String FLIP_VERTICAL = "Flip Vertical";
    public static final String FLIP_HORIZONTAL = "Flip Horizontal";
    public static final String ROTATE_90_DEGREE_CLOCKWISE = "Rotate 90 Degr Clockwise";
    public static final String ROTATE_90_DEGERE_COUNTERCLOCKWISE = "Rotate 90 Degr Counterclockwise";
    public static final String BLUR = "Blur";
    public static final String CONVOLUTION = "Convolution";
    public static final String CONVOLUTION_2 = "Convolution 2";
    public static final String STEREOSCOPIC = "Stereoscopic";
    public static final String PIXELIZE = "Pixelize";
    public static final String BRIGHTNESS_PLUS = "Brightness Plus";
    public static final String BRIGHTNESS_MINUS = "Brightness Minus";
    public static final String SATURATION_PLUS = "Saturation Plus";
    public static final String SATURATION_MINUS = "Saturation Minus";
    public static final String GAMMA_R_PLUS = "Gamma R Plus";
    public static final String GAMMA_R_MINUS = "Gamma R Minus";
    public static final String GAMMA_G_PLUS = "Gamma G Plus";
    public static final String GAMMA_G_MINUS = "Gamma G Minus";
    public static final String GAMMA_B_PLUS = "Gamma B Plus";
    public static final String GAMMA_B_MINUS = "Gamma B Minus";
    public static final String COLOR_BALANCE = "Color Balance";
    public static final String COLOR_BALANCE_TWO = "Color Balance Two";
    public static final String COLOR_NEGATIVE = "Color Negative";
    public static final String CROSS_PROCESS = "Cross Process";
    public static final String DARKER = "Darker";
    public static final String LIGHTER = "Lighter";
    public static final String INCREASE_CONTRAST = "Increase Contrast";
    public static final String LINEAR_CONTRAST = "Linear Contrast";
    public static final String MEDIUM_CONTRAST = "Medium Contrast";
    public static final String STRONG_CONTRAST = "Strong Contrast";
    public static final String NEGATIVE = "Negative";
    public static final String CURVES_BLUE = "Curves Blue";
    public static final String ENHANCED_LBG_2 = "Enhanced LBG 2";
    public static final String ENHANCED_LBG_4 = "Enhanced LBG 4";
    public static final String ENHANCED_LBG_8 = "Enhanced LBG 8";
    public static final String ENHANCED_LBG_16 = "Enhanced LBG 16";
    public static final String DEBAND = "Deband";
    public static final String SWAP_RECT_ONE = "Swap Rect One";
    public static final String SWAP_RECT_TWO = "Swap Rect Two";
    public static final String SWAP_RECT_THREE = "Swap Rect Three";
    public static final String SWAP_RECT_FOUR = "Swap Rect Four";
    public static final String SWAP_RECT_FIVE = "Swap Rect Five";
    public static final String SWAP_RECT_SIX = "Swap Rect Six";
    public static final String NOISE = "Noise";
    public static final String CRAZY = "Crazy";
    public static final String MIRROR = "Mirror";
    public static final String CARTOON = "Cartoon";
    public static final String ZOOMED = "Zoomed";
    public static final String VINTAGE = "Vintage";
    public static final String FADE_IN = "Fade in";
    public static final String FADE_OUT = "Fade out";
    public static final String SPLIT_VIDEO = "Split video";
}
