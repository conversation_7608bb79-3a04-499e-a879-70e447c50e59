package com.videomontage.freeeditingapps.view.text

import android.app.Activity
import android.content.Context
import android.graphics.drawable.BitmapDrawable
import android.view.*
import android.widget.PopupWindow
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.adapter.ColorAdapter
import com.videomontage.freeeditingapps.adapter.FontAdapter
import com.videomontage.freeeditingapps.utils.assets.AssetsUtil
import com.videomontage.freeeditingapps.utils.assets.FontItem

class PopBubbleView_Second(private val context: Context) {
    private val TAG = PopBubbleView_Second::class.java.simpleName
    private var popupWindow: PopupWindow? = null
   lateinit var popupWindowView: View
    private val reportType: String? = null
    lateinit var fontAdapter: FontAdapter

    lateinit var colorAdapter: ColorAdapter
    lateinit var textOverlayProperties: TextOverlayProperties
    var textColor = "#d83636"

    /**
     * 初始化
     */
    fun initPopupWindow() {
//        if (popupWindowView != null) {
//            popupWindow!!.dismiss()
//        }
        popupWindowView = LayoutInflater.from(context).inflate(R.layout.pop_bubble_font, null)
        popupWindow = PopupWindow(popupWindowView, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT, true)
        //        popupWindow.setAnimationStyle(R.style.popup_window_scale);
        popupWindow!!.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE
        // 菜单背景色。加了一点透明度
//        ColorDrawable dw = new ColorDrawable(0xddffffff);
//        popupWindow.setBackgroundDrawable(dw);
        popupWindow!!.isOutsideTouchable = true
        popupWindow!!.setBackgroundDrawable(BitmapDrawable()) //解决部分机型按back键无法退出popupwindow


        // 设置背景半透明
//        backgroundAlpha(0.7f);
        popupWindow!!.setOnDismissListener(popupDismissListener())
        popupWindowView!!.setOnTouchListener(View.OnTouchListener { v, event -> /*
                 * if( popupWindow!=null && popupWindow.isShowing()){
                 * popupWindow.dismiss(); popupWindow=null; }
                 */
            // 这里如果返回true的话，touch事件将被拦截
            // 拦截后 PopupWindow的onTouchEvent不被调用，这样点击外部区域无法dismiss
            false
        })

//        val btn_click_me = popupWindowView.findViewById(R.id.ll_bubble_one) as LinearLayout
//        btn_click_me.setOnClickListener(View.OnClickListener {
//            onItemClickListener?.invoke(0)
//
//        })

        setupRecyclerView()
        setupColorRecyclerView()

    }

    private fun setupColorRecyclerView(){
        val colors = mutableListOf("#d83636", "#d88e36", "#d8bf36", "#f1f32e", "#c7f32e", "#82d836", "#36d86f", "#36d8d6", "#368ad8", "#3e36d8", "#7336d8", "#c336d8", "#d8369d", "#d8366b", "#FFFFFF", "#DDDDDD", "#CCCCCC", "#999999", "#666666", "#333333", "#000000")
        colorAdapter = ColorAdapter()
        colorAdapter.setOnItemClickListener {
            textColor = it
//            onColorItemClickListener?.invoke(it)

        }
        var fontRv = popupWindowView.findViewById(R.id.colorRv) as RecyclerView
        fontRv.apply {
            adapter = colorAdapter
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        }

        colorAdapter.differ.submitList(colors)
    }

    private fun setupRecyclerView() {
        fontAdapter = FontAdapter()
        fontAdapter.setOnItemClickListener {
            onItemClickListener?.invoke(TextOverlayProperties(it, textColor))

        }
        var fontRv = popupWindowView.findViewById(R.id.fontRV) as RecyclerView
        fontRv.apply {
            adapter = fontAdapter
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        }

        fontAdapter.differ.submitList(AssetsUtil.getFontList())
    }

    /**
     * 设置添加屏幕的背景透明度
     *
     * @param bgAlpha
     */
    fun backgroundAlpha(bgAlpha: Float) {
        val lp = (context as Activity).window.attributes
        lp.alpha = bgAlpha // 0.0-1.0
        context.window.attributes = lp
    }

//    @OnClick(R.id.ll_bubble_one, R.id.ll_bubble_two, R.id.ll_bubble_three, R.id.ll_bubble_four, R.id.ll_bubble_five, R.id.ll_bubble_six, R.id.ll_bubble_seven, R.id.ll_bubble_eight)
//    fun onViewClicked(view: View) {
//        Log.d(TAG, "onViewClicked: saffsfa")

//        when (view.id) {
//            R.id.ll_bubble_one -> {
//                if (bubbleSelectListener != null) {
//                    bubbleSelectListener!!.texterSelect(0)
//                }
//                dimss()
//            }
//            R.id.ll_bubble_two -> {
//                if (bubbleSelectListener != null) {
//                    bubbleSelectListener!!.texterSelect(1)
//                }
//                dimss()
//            }
//            R.id.ll_bubble_three -> {
//                if (bubbleSelectListener != null) {
//                    bubbleSelectListener!!.texterSelect(2)
//                }
//                dimss()
//            }
//            R.id.ll_bubble_four -> {
//                if (bubbleSelectListener != null) {
//                    bubbleSelectListener!!.texterSelect(3)
//                }
//                dimss()
//            }
//            R.id.ll_bubble_five -> {
//                if (bubbleSelectListener != null) {
//                    bubbleSelectListener!!.texterSelect(4)
//                }
//                dimss()
//            }
//            R.id.ll_bubble_six -> {
//                if (bubbleSelectListener != null) {
//                    bubbleSelectListener!!.texterSelect(5)
//                }
//                dimss()
//            }
//            R.id.ll_bubble_seven -> {
//                if (bubbleSelectListener != null) {
//                    bubbleSelectListener!!.texterSelect(6)
//                }
//                dimss()
//            }
//            R.id.ll_bubble_eight -> {
//                if (bubbleSelectListener != null) {
//                    bubbleSelectListener!!.texterSelect(7)
//                }
//                dimss()
//            }
//        }
//    }

    internal inner class popupDismissListener : PopupWindow.OnDismissListener {
        override fun onDismiss() {
            backgroundAlpha(1f)
        }
    }

    fun dimss() {
        if (popupWindow != null) {
            popupWindow!!.dismiss()
        }
    }

    val isShowing: Boolean
        get() = popupWindow!!.isShowing

    fun show() {
        if (popupWindow != null && !popupWindow!!.isShowing) {
            popupWindow!!.showAtLocation(LayoutInflater.from(context).inflate(R.layout.activity_update_personal_info, null),
                    Gravity.BOTTOM, 0, 0)
        }
    }

    interface BubbleSelectListener {
        fun texterSelect(bubbleIndex: Int)
    }

    private var onColorItemClickListener: ((fontItem: String) -> Int)? = null

    fun setOnColorItemClickListener(listener: (fontItem: String) -> Int) {
        onColorItemClickListener = listener
    }

    private var onItemClickListener: ((textOverlayProperties : TextOverlayProperties) -> Int)? = null

    fun setOnItemClickListener(listener: (textOverlayProperties : TextOverlayProperties) -> Int) {
        onItemClickListener = listener
    }

//    lateinit var bubbleSelectListener: BubbleSelectListener
//
//    @JvmName("setBubbleSelectListener1")
//    fun setBubbleSelectListener(bubbleSelectListener: BubbleSelectListener) {
//        this.bubbleSelectListener = bubbleSelectListener
//    }

    init {
        initPopupWindow()
    }

    data class TextOverlayProperties(val fontItem: FontItem, val textColor : String)
}