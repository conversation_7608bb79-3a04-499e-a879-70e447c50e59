package com.videomontage.freeeditingapps.view;

import android.app.Activity;
import android.app.Dialog;
import android.content.res.ColorStateList;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ProgressBar;
import android.widget.TextView;
import com.videomontage.freeeditingapps.R;


public class CustomDialog extends Dialog implements OnClickListener {

    private Activity c;
    private Dialog d;
    private TextView cancelTv, displayTextTv;
    private ProgressBar progressBarSaveDialogPercentage, progressBarSaveDialogIndeterminate;
    private boolean isSpinner;
    private String text;
    private CustomDialogListener customDialogListener;

    public CustomDialog(Activity a, String text, boolean isSpinner, CustomDialogListener customDialogListener) {
        super(a, R.style.DialogTheme);
//        super(a);
        this.c = a;
        this.isSpinner = isSpinner;
        this.text = text;
        this.customDialogListener = customDialogListener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.custom_dialog);

        getWindow()
                .setLayout(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                );

//        banner();

        displayTextTv = (TextView) findViewById(R.id.displayTextTv);
        displayTextTv.setText(text);
        cancelTv = (TextView) findViewById(R.id.cancelTv);
        cancelTv.setOnClickListener(this);


        progressBarSaveDialogPercentage = (ProgressBar) findViewById(R.id.progressBarSaveDialogPercentage);
        progressBarSaveDialogIndeterminate = (ProgressBar) findViewById(R.id.progressBarSaveDialogIndeterminate);
        if (isSpinner) {
            progressBarSaveDialogPercentage.setVisibility(View.GONE);
            progressBarSaveDialogIndeterminate.setVisibility(View.VISIBLE);
        } else {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                progressBarSaveDialogPercentage.setProgressTintList(ColorStateList.valueOf(getContext().getResources().getColor(R.color.colorAccent)));
            }
            progressBarSaveDialogPercentage.setPadding(20, 10, 10, 10);
            progressBarSaveDialogIndeterminate.setVisibility(View.GONE);
            progressBarSaveDialogPercentage.setVisibility(View.VISIBLE);
            progressBarSaveDialogPercentage.setProgress(0);
            progressBarSaveDialogPercentage.setMax(100);
        }


    }

//    private void banner() {
//        if (MainActivity.TEST)
//            loadAdsNative("ca-app-pub-3940256099942544/6300978111");
//        else
//            loadAdsNative("ca-app-pub-2572150356682336/9707320742");
//    }

    public void setProgress(int progress) {
        if (!isSpinner)
            progressBarSaveDialogPercentage.setProgress(progress);

    }


//    private void loadAdsNative(String adUnitID) {
//
//        FrameLayout adContainer = findViewById(R.id.fl_ad_banner111);
//        AdView adView = new AdView(c.getBaseContext());
//        adView.setAdSize(AdSize.SMART_BANNER);
//        adView.setAdUnitId(adUnitID);
//
//        // Initiate a generic request to load it with an ad
//        AdRequest adRequest = new AdRequest.Builder().build();
//        adView.loadAd(adRequest);
//        // Place the ad view.
////        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT);
//        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, AdSize.SMART_BANNER.getHeight());
//        adContainer.addView(adView, params);
//
//    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.cancelTv:

                dismiss();
                if (customDialogListener != null) {
                    customDialogListener.onDismiss();
                }
                break;
        }
    }

    public interface CustomDialogListener {
        void onDismiss();
    }
}

