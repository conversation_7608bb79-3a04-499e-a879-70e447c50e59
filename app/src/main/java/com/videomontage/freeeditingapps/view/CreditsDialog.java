package com.videomontage.freeeditingapps.view;

import android.app.Activity;
import android.app.Dialog;
import android.os.Build;
import android.os.Bundle;
import android.text.Html;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.webkit.WebView;
import android.widget.ImageView;
import android.widget.TextView;

import com.videomontage.freeeditingapps.R;

public class CreditsDialog extends Dialog implements View.OnClickListener {

    private Activity c;
    private Dialog d;
    private CreditsDialog.CustomDialogListener customDialogListener;
    private WebView creditsWv;
    private ImageView closeIv;
    private TextView italianTv, spanishLatamTv,frenchTv, spanishTv, polishTv, serbianAndCroatianTv, russianTv, germanTv;

    public CreditsDialog(Activity a, CreditsDialog.CustomDialogListener customDialogListener) {
        super(a, R.style.DialogTheme);
//        super(a);
        this.c = a;
        this.customDialogListener = customDialogListener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.credits_layout);

        getWindow()
                .setLayout(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                );

        germanTv = findViewById(R.id.germanTv);
        String german = "Miriam Ahlert, " + getContext().getResources().getString(R.string.language_german);
        germanTv.setText(german);

        russianTv = findViewById(R.id.russianTv);
        String russian = "Alexander Ashikhin, " + getContext().getResources().getString(R.string.language_russian);
        russianTv.setText(russian);

        frenchTv = findViewById(R.id.frenchTv);
        String french = "Jannifer Morgenroth-Debord, " + getContext().getResources().getString(R.string.language_french);
        frenchTv.setText(french);

        italianTv = findViewById(R.id.italianTv);
        String italian = "Luca Franco Lo Iacono, " + getContext().getResources().getString(R.string.language_italian);
        italianTv.setText(italian);

        spanishLatamTv = findViewById(R.id.spanishLatamTv);
        String spanishLatam = "Daniel Yah, " + getContext().getResources().getString(R.string.language_spanish_latam);
        spanishLatamTv.setText(spanishLatam);

        spanishTv = findViewById(R.id.spanishTv);
        String spanish = "Sergio Iván Ossa Londoño, " + getContext().getResources().getString(R.string.language_spanish);
        spanishTv.setText(spanish);

        polishTv = findViewById(R.id.polishTv);
        String polish = "Natasza Pankratjew, " + getContext().getResources().getString(R.string.language_polish);
        polishTv.setText(polish);

        serbianAndCroatianTv = findViewById(R.id.serbianAndCroatianTv);
        String serbianAndCroatian = "Tamara Tucakovic, " + getContext().getResources().getString(R.string.language_serbian_croatian);
        serbianAndCroatianTv.setText(serbianAndCroatian);

        closeIv = findViewById(R.id.closeIv);
        closeIv.setOnClickListener(this);
        creditsWv = findViewById(R.id.creditsWv);
        String url = "https://www.asoconnectme.com/";
        String htmlText = "<p>" + getContext().getResources().getString(R.string.app_brought_by)+ " "+
                "<b>Jahja Trifunovic</b>, Co-founder & Android developer\n" +
                "<b>Tamara Tucakovic</b>, Co-founder"+
                "</p>";
        creditsWv.loadData(htmlText, "text/html", "UTF-8");

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.closeIv:
                dismiss();
                break;
        }
    }

    public interface CustomDialogListener {
        void onDismiss();
    }
}