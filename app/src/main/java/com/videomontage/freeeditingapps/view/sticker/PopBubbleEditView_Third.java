package com.videomontage.freeeditingapps.view.sticker;


import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.BitmapDrawable;
import android.os.Handler;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.videomontage.freeeditingapps.R;
import com.videomontage.freeeditingapps.view.PopTopTipWindow;

public class PopBubbleEditView_Third {
    private EditText edContent;
    private TextView tvConfirm;
    private String TAG = PopBubbleEditView_Third.class.getSimpleName();

    private Context context;
    private PopupWindow popupWindow;
    private View popupWindowView;
    private String initText;


    public PopBubbleEditView_Third(Context context) {
        this.context = context;
        initPopupWindow();
    }

    /**
     * 初始化
     */
    public void initPopupWindow() {
        if (popupWindowView != null) {
            popupWindow.dismiss();
        }

        popupWindowView = LayoutInflater.from(context).inflate(R.layout.pop_bubble_edit_view, null);
        edContent = popupWindowView.findViewById(R.id.ed_content);
        tvConfirm = popupWindowView.findViewById(R.id.tv_confirm);
        popupWindow = new PopupWindow(popupWindowView, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT, true);
        popupWindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_UNCHANGED);
        popupWindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        popupWindow.setOutsideTouchable(true);
        popupWindow.setBackgroundDrawable(new BitmapDrawable());

        // 设置背景半透明
        popupWindow.setOnDismissListener(new popupDismissListener());

        popupWindowView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                return false;
            }
        });

        edContent.addTextChangedListener(textWatcher);

        edContent.setOnClickListener(view -> {
            // Your implementation here
        });
        tvConfirm.setOnClickListener(view -> {
            switch (view.getId()) {
                case R.id.ed_content:
                    break;
                case R.id.tv_confirm:
                    if (edContent.getText().toString().length() > 60) {
                        Toast.makeText(context,"输入字符不能超过30个", Toast.LENGTH_SHORT).show();
                        return;
                    }
                    if (onTextSendListener != null) {
                        onTextSendListener.onTextSend(edContent.getText().toString());
                    }
                    edContent.setText("");
                    dimss();
                    break;
            }
        });
    }

    private TextWatcher textWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            if(s != null && s.toString().length() > 30){
                String tempStr = s.toString().substring(0,30);
                edContent.removeTextChangedListener(textWatcher);
                edContent.setText(tempStr);
                edContent.setSelection(tempStr.length());
                edContent.addTextChangedListener(textWatcher);
                Toast.makeText(context,"输入文字不能超过30个", Toast.LENGTH_SHORT).show();
            }
        }

        @Override
        public void afterTextChanged(Editable s) {

        }
    };

    /**
     * 设置添加屏幕的背景透明度
     *
     * @param bgAlpha
     */
    public void backgroundAlpha(float bgAlpha) {
        WindowManager.LayoutParams lp = ((Activity) context).getWindow().getAttributes();
        lp.alpha = bgAlpha; // 0.0-1.0
        ((Activity) context).getWindow().setAttributes(lp);
    }

    class popupDismissListener implements PopupWindow.OnDismissListener {
        @Override
        public void onDismiss() {
            backgroundAlpha(1f);
        }
    }

    public void dimss() {
        if (popupWindow != null) {
            popupWindow.dismiss();
        }
    }

    public boolean isShowing() {
        return popupWindow.isShowing();
    }

    public void show(String initText) {
        if (popupWindow != null && !popupWindow.isShowing()) {
            if(!initText.equals("点击输入文字")){
                this.initText = initText;
                edContent.setText(initText);
                edContent.setSelection(initText.length());
            }
            InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.toggleSoftInput(1000, InputMethodManager.HIDE_NOT_ALWAYS);
            popupWindow.showAtLocation(LayoutInflater.from(context).inflate(R.layout.base_activity, null),
                    Gravity.BOTTOM, 0, 0);
        }
    }


    public interface OnTextSendListener{
        void onTextSend(String text);
    }

    public OnTextSendListener onTextSendListener;

    public void setOnTextSendListener(OnTextSendListener onTextSendListener){
        this.onTextSendListener = onTextSendListener;
    }


    private PopTopTipWindow topTipWindow;
    private long last = 0;
    public void showPop(String s,Context mContext){
        long star = System.currentTimeMillis();
        long cha = star - last;
        if(cha/1000 < 5){
            return;
        }
        last = star;
        if(topTipWindow != null && topTipWindow.isShowing()){
            topTipWindow.dimss();
            topTipWindow = null;
        }else {
            topTipWindow = new PopTopTipWindow(mContext, s);
            if (!((Activity) mContext).isFinishing()) {
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (topTipWindow != null && topTipWindow.isShowing()) {
                            topTipWindow.dimss();
                        }
                    }
                }, 1000);
            }
        }
    }

}

