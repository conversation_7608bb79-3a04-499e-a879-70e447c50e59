package com.videomontage.freeeditingapps.view.text;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.BitmapDrawable;
import android.os.Handler;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.PopupWindow;
import android.widget.TextView;
import android.widget.Toast;

import com.videomontage.freeeditingapps.R;
import com.videomontage.freeeditingapps.view.PopTopTipWindow;


public class PopBubbleEditView_Second {
    private EditText edContent;
    private TextView tvConfirm;
    private String TAG = PopBubbleEditView_Second.class.getSimpleName();

    private Context context;
    private PopupWindow popupWindow;
    private View popupWindowView;
    private static int MAX_CHARS = 200;

    public PopBubbleEditView_Second(Context context) {
        this.context = context;
        initPopupWindow();
    }

    public void initPopupWindow() {
        if (popupWindowView != null) {
            popupWindow.dismiss();
        }

        popupWindowView = LayoutInflater.from(context).inflate(R.layout.pop_bubble_edit_view, null);

        edContent = popupWindowView.findViewById(R.id.ed_content);
        tvConfirm = popupWindowView.findViewById(R.id.tv_confirm);

        popupWindow = new PopupWindow(popupWindowView, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT, true);
        popupWindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_UNCHANGED);
        popupWindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        popupWindow.setOutsideTouchable(true);
        popupWindow.setBackgroundDrawable(new BitmapDrawable());

        popupWindow.setOnDismissListener(this::backgroundAlpha);

        popupWindowView.setOnTouchListener((v, event) -> false);

        edContent.addTextChangedListener(textWatcher);

        tvConfirm.setOnClickListener(this::onViewClicked);
        edContent.setOnClickListener(this::onViewClicked);
    }

    private TextWatcher textWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            if(s != null && s.toString().length() > MAX_CHARS){
                String tempStr = s.toString().substring(0,MAX_CHARS);
                edContent.removeTextChangedListener(textWatcher);
                edContent.setText(tempStr);
                edContent.setSelection(tempStr.length());
                edContent.addTextChangedListener(textWatcher);
                Toast.makeText(context, context.getResources().getString(R.string.max_chars) + MAX_CHARS, Toast.LENGTH_SHORT).show();
            }
        }

        @Override
        public void afterTextChanged(Editable s) {}
    };

    public void backgroundAlpha() {
        WindowManager.LayoutParams lp = ((Activity) context).getWindow().getAttributes();
        lp.alpha = 1f;
        ((Activity) context).getWindow().setAttributes(lp);
    }

    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ed_content:
                break;
            case R.id.tv_confirm:
                if (edContent.getText().toString().length() > MAX_CHARS) {
                    Toast.makeText(context, context.getResources().getString(R.string.max_chars) + MAX_CHARS, Toast.LENGTH_SHORT).show();
                    return;
                }
                if (onTextSendListener != null) {
                    onTextSendListener.onTextSend(edContent.getText().toString());
                }
                edContent.setText("");
                dimss();
                break;
        }
    }

    public void dimss() {
        if (popupWindow != null) {
            popupWindow.dismiss();
        }
    }

    public boolean isShowing() {
        return popupWindow.isShowing();
    }

    public void show(String initText) {
        if (popupWindow != null && !popupWindow.isShowing()) {
            if(!initText.equals("Click to enter text")){
                edContent.setText(initText);
                edContent.setSelection(initText.length());
            }
            InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.toggleSoftInput(1000, InputMethodManager.HIDE_NOT_ALWAYS);
            popupWindow.showAtLocation(LayoutInflater.from(context).inflate(R.layout.base_activity, null),
                    Gravity.BOTTOM, 0, 0);
        }
    }

    public interface OnTextSendListener{
        void onTextSend(String text);
    }

    public OnTextSendListener onTextSendListener;

    public void setOnTextSendListener(OnTextSendListener onTextSendListener){
        this.onTextSendListener = onTextSendListener;
    }

    private PopTopTipWindow topTipWindow;
    private long last = 0;
    public void showPop(String s,Context mContext){
        long star = System.currentTimeMillis();
        long cha = star - last;
        if(cha/1000 < 5){
            return;
        }
        last = star;
        if(topTipWindow != null && topTipWindow.isShowing()){
            topTipWindow.dimss();
            topTipWindow = null;
        }else {
            topTipWindow = new PopTopTipWindow(mContext, s);
            if (!((Activity) mContext).isFinishing()) {
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (topTipWindow != null && topTipWindow.isShowing()) {
                            topTipWindow.dimss();
                        }
                    }
                }, 1000);
            }
        }
    }
}

