package com.videomontage.freeeditingapps.view;

import android.app.Dialog;
import android.content.DialogInterface;
import android.os.Bundle;

import androidx.annotation.NonNull;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import androidx.fragment.app.FragmentActivity;
import androidx.core.view.ViewCompat;
import androidx.core.view.ViewPropertyAnimatorListener;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.view.View;
import android.widget.TextView;

import com.videomontage.freeeditingapps.R;
import com.videomontage.freeeditingapps.adapter.EffectHistoryAdapter;
import com.videomontage.freeeditingapps.model.AppliedEffectVideoItem;
import com.videomontage.freeeditingapps.utils.CycleInterpolator;
import com.videomontage.freeeditingapps.utils.EffectsConstants;

public class ThresholdBottomSheet extends BottomSheetDialogFragment implements View.OnClickListener {
    private static final String TAG = "MyBottomSheet";
    private String effectName;
    private BottomSheetBehavior behavior;
    private View view;
    private BottomSheetClickeListener bottomSheetClickeListener;

    private EffectHistoryAdapter adapter;
    private RecyclerView recyclerView;
//    private ArrayList<AppliedEffectVideoItem> appliedEffectVideoItemArrayList;
    private int idsOfColors[] = {
            R.id.grayThresholdCv, R.id.blackThresholdCv, R.id.whiteThresholdCv, R.id.redThresholdCv, R.id.greenThresholdCv, R.id.blueThresholdCv,
            R.id.minGrayThresholdCv, R.id.minBlackThresholdCv, R.id.minWhiteThresholdCv, R.id.minRedThresholdCv, R.id.minGreenThresholdCv, R.id.minBlueThresholdCv,
            R.id.maxGrayThresholdCv, R.id.maxBlackThresholdCv, R.id.maxWhiteThresholdCv, R.id.maxRedThresholdCv, R.id.maxGreenThresholdCv, R.id.maxBlueThresholdCv
    };
    private int idsOfDoneThresholdColorIv[] = {
            R.id.grayDoneThresholdIv, R.id.blackDoneThresholdIv, R.id.whiteDoneThresholdIv, R.id.redDoneThresholdIv, R.id.greenDoneThresholdIv, R.id.blueDoneThresholdIv,
    };
    private int idsOfDoneMinThresholdColorIv[] = {
            R.id.minGrayDoneThresholdIv, R.id.minBlackDoneThresholdIv, R.id.minWhiteDoneThresholdIv, R.id.minRedDoneThresholdIv, R.id.minGreenDoneThresholdIv, R.id.minBlueDoneThresholdIv,
    };
    private int idsOfDoneMaxThresholdColorIv[] = {
            R.id.maxGrayDoneThresholdIv, R.id.maxBlackDoneThresholdIv, R.id.maxWhiteDoneThresholdIv, R.id.maxRedDoneThresholdIv, R.id.maxGreenDoneThresholdIv, R.id.maxBlueDoneThresholdIv,
    };
    private String colors[] = {"gray", "black", "white", "red", "green", "blue"};
    private int thresholdColorIndex = 0;
    private int minThresholdColorIndex = 1;
    private int maxThresholdColorIndex = 2;

    @NonNull
    @Override
    public Dialog onCreateDialog(final Bundle savedInstanceState) {
        final BottomSheetDialog dialog = (BottomSheetDialog) super.onCreateDialog(savedInstanceState);

        view = View.inflate(getContext(), R.layout.bottom_sheet_threshold, null);
        TextView effectTv = view.findViewById(R.id.effectTv);
        effectTv.setText(effectName);

        setListener();

        recyclerView = view.findViewById(R.id.historyRv);
        recyclerView.setLayoutManager(new LinearLayoutManager(view.getContext()));

        EffectsConstants.APPLIED_EFFECTS_VIDEO_ITEM_ARRAYLIST.add(new AppliedEffectVideoItem("Test", "", "Test"));
        EffectsConstants.APPLIED_EFFECTS_VIDEO_ITEM_ARRAYLIST.add(new AppliedEffectVideoItem("Test", "", "Test"));
        EffectsConstants.APPLIED_EFFECTS_VIDEO_ITEM_ARRAYLIST.add(new AppliedEffectVideoItem("Test", "", "Test"));
        EffectsConstants.APPLIED_EFFECTS_VIDEO_ITEM_ARRAYLIST.add(new AppliedEffectVideoItem("Test", "", "Test"));

        adapter = new EffectHistoryAdapter(view.getContext(), EffectsConstants.APPLIED_EFFECTS_VIDEO_ITEM_ARRAYLIST);
        adapter.setClickListener(new EffectHistoryAdapter.ItemClickListener() {
            @Override
            public void onItemClick(View view, int position) {

            }
        });
        recyclerView.setAdapter(adapter);

        dialog.setContentView(view);

        behavior = BottomSheetBehavior.from((View) view.getParent());
        return dialog;
    }


    public void show(final FragmentActivity fragmentActivity, String effectName) {
        this.effectName = effectName;
        show(fragmentActivity.getSupportFragmentManager(), TAG);

    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);

//        bottomSheetClickeListener.onDismissSubscription();
    }


    public void setBottomSheetClickeListener(BottomSheetClickeListener bottomSheetClickeListener) {
        this.bottomSheetClickeListener = bottomSheetClickeListener;
    }

    @Override
    public void onClick(View v) {
//        this.dismiss();

        Log.d(TAG, "onClick: sdfadsf");

        ViewCompat.animate(v)
                .setDuration(200)
                .scaleX(0.9f)
                .scaleY(0.9f)
                .setInterpolator(new CycleInterpolator())
                .setListener(new ViewPropertyAnimatorListener() {
                    @Override
                    public void onAnimationStart(final View view) {

                    }

                    @Override
                    public void onAnimationEnd(final View view) {
                        switch (v.getId()) {

                            // Threshold color
                            case R.id.grayThresholdCv:
                                thresholdColorIndex = 0;
                                setDoneThresholdColor();
                                break;
                            case R.id.blackThresholdCv:
                                thresholdColorIndex = 1;
                                setDoneThresholdColor();
                                break;
                            case R.id.whiteThresholdCv:
                                thresholdColorIndex = 2;
                                setDoneThresholdColor();
                                break;
                            case R.id.redThresholdCv:
                                thresholdColorIndex = 3;
                                setDoneThresholdColor();
                                break;
                            case R.id.greenThresholdCv:
                                thresholdColorIndex = 4;
                                setDoneThresholdColor();
                                break;
                            case R.id.blueThresholdCv:
                                thresholdColorIndex = 5;
                                setDoneThresholdColor();
                                break;

                            // Min Threshold color
                            case R.id.minGrayThresholdCv:
                                minThresholdColorIndex = 0;
                                setDoneMinThresholdColor();
                                break;
                            case R.id.minBlackThresholdCv:
                                minThresholdColorIndex = 1;
                                setDoneMinThresholdColor();
                                break;
                            case R.id.minWhiteThresholdCv:
                                minThresholdColorIndex = 2;
                                setDoneMinThresholdColor();
                                break;
                            case R.id.minRedThresholdCv:
                                minThresholdColorIndex = 3;
                                setDoneMinThresholdColor();
                                break;
                            case R.id.minGreenThresholdCv:
                                minThresholdColorIndex = 4;
                                setDoneMinThresholdColor();
                                break;
                            case R.id.minBlueThresholdCv:
                                minThresholdColorIndex = 5;
                                setDoneMinThresholdColor();
                                break;

                            // Max Threshold color
                            case R.id.maxGrayThresholdCv:
                                maxThresholdColorIndex = 0;
                                setDoneMaxThresholdColor();
                                break;
                            case R.id.maxBlackThresholdCv:
                                maxThresholdColorIndex = 1;
                                setDoneMaxThresholdColor();
                                break;
                            case R.id.maxWhiteThresholdCv:
                                maxThresholdColorIndex = 2;
                                setDoneMaxThresholdColor();
                                break;
                            case R.id.maxRedThresholdCv:
                                maxThresholdColorIndex = 3;
                                setDoneMaxThresholdColor();
                                break;
                            case R.id.maxGreenThresholdCv:
                                maxThresholdColorIndex = 4;
                                setDoneMaxThresholdColor();
                                break;
                            case R.id.maxBlueThresholdCv:
                                maxThresholdColorIndex = 5;
                                setDoneMaxThresholdColor();
                                break;

                        }

                    }

                    @Override
                    public void onAnimationCancel(final View view) {

                    }
                })
                .withLayer()
                .start();
        
    }

    private void setListener() {
        for (int i = 0; i < idsOfColors.length; i++) {
            view.findViewById(idsOfColors[i]).setOnClickListener(this);
        }

        view.findViewById(R.id.previewFB).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismiss();
                bottomSheetClickeListener.onDismissSubscription(colors[thresholdColorIndex], colors[minThresholdColorIndex], colors[maxThresholdColorIndex]);
            }
        });
    }

    private void setDoneThresholdColor() {
        for (int i = 0; i < idsOfDoneThresholdColorIv.length; i++) {
            view.findViewById(idsOfDoneThresholdColorIv[i]).setVisibility(View.GONE);

        }

        view.findViewById(idsOfDoneThresholdColorIv[thresholdColorIndex]).setVisibility(View.VISIBLE);

    }

    private void setDoneMinThresholdColor() {
        for (int i = 0; i < idsOfDoneMinThresholdColorIv.length; i++) {
            view.findViewById(idsOfDoneMinThresholdColorIv[i]).setVisibility(View.GONE);
        }
        view.findViewById(idsOfDoneMinThresholdColorIv[minThresholdColorIndex]).setVisibility(View.VISIBLE);
    }

    private void setDoneMaxThresholdColor() {
        for (int i = 0; i < idsOfDoneMaxThresholdColorIv.length; i++) {
            view.findViewById(idsOfDoneMaxThresholdColorIv[i]).setVisibility(View.GONE);
        }
        view.findViewById(idsOfDoneMaxThresholdColorIv[maxThresholdColorIndex]).setVisibility(View.VISIBLE);


    }


    public interface BottomSheetClickeListener {
        void onDismissSubscription(String thresholdColor, String minThresholdColor, String maxThresholdColor);
    }

}

