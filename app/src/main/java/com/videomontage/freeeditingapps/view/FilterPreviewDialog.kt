package com.videomontage.freeeditingapps.view

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.Window
import android.widget.Button
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.source.ProgressiveMediaSource
import com.google.android.exoplayer2.ui.PlayerView
import com.google.android.exoplayer2.upstream.DefaultDataSource
import com.google.android.exoplayer2.video.VideoSize
import com.videomontage.freeeditingapps.R
import timber.log.Timber
import java.io.File

/**
 * Dialog to show filter preview
 */
class FilterPreviewDialog(
    context: Context,
    private val effectName: String,
    private val previewPath: String,
    private val onApplyClick: () -> Unit
) : Dialog(context, android.R.style.Theme_Black_NoTitleBar_Fullscreen) {

    private lateinit var playerView: PlayerView
    private lateinit var titleTextView: TextView
    private lateinit var applyButton: Button
    private lateinit var cancelButton: Button
    private lateinit var progressBar: ProgressBar
    private lateinit var loadingText: TextView
    
    private var exoPlayer: ExoPlayer? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Timber.tag("DEBUG_FLOW").d("FilterPreviewDialog: onCreate for effect: $effectName")
        Timber.tag("DEBUG_FLOW").d("FilterPreviewDialog: Preview path: $previewPath")
        
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        setContentView(R.layout.dialog_filter_preview)

        initViews()
        setupPlayer()
    }

    private fun initViews() {
        playerView = findViewById(R.id.previewPlayerView)
        titleTextView = findViewById(R.id.effectNameTextView)
        applyButton = findViewById(R.id.applyFilterButton)
        cancelButton = findViewById(R.id.cancelButton)
        progressBar = findViewById(R.id.loadingProgressBar)
        loadingText = findViewById(R.id.loadingTextView)

        titleTextView.text = effectName

        applyButton.setOnClickListener {
            onApplyClick()
            dismiss()
        }

        cancelButton.setOnClickListener {
            dismiss()
        }
    }

    private fun setupPlayer() {
        Timber.tag("DEBUG_FLOW").d("FilterPreviewDialog: setupPlayer called")
        
        if (!File(previewPath).exists()) {
            Timber.tag("DEBUG_FLOW").e("FilterPreviewDialog: Preview file not found at: $previewPath")
            Toast.makeText(context, "Preview file not found", Toast.LENGTH_SHORT).show()
            dismiss()
            return
        }
        
        val previewFile = File(previewPath)
        Timber.tag("DEBUG_FLOW").d("FilterPreviewDialog: Preview file exists, size: ${previewFile.length()} bytes")

        progressBar.visibility = View.VISIBLE
        loadingText.visibility = View.VISIBLE

        exoPlayer = ExoPlayer.Builder(context).build().apply {
            Timber.tag("DEBUG_FLOW").d("FilterPreviewDialog: Setting up ExoPlayer")
            
            playerView.player = this
            playerView.useController = false
            playerView.resizeMode = com.google.android.exoplayer2.ui.AspectRatioFrameLayout.RESIZE_MODE_FIT
            
            // Force surface view rendering
            playerView.setShowBuffering(PlayerView.SHOW_BUFFERING_WHEN_PLAYING)
            playerView.keepScreenOn = true
            
            Timber.tag("DEBUG_FLOW").d("FilterPreviewDialog: PlayerView configured, resize mode: FIT")
            Timber.tag("DEBUG_FLOW").d("FilterPreviewDialog: PlayerView visibility: ${playerView.visibility}")
            Timber.tag("DEBUG_FLOW").d("FilterPreviewDialog: PlayerView dimensions: ${playerView.width}x${playerView.height}")
            
            val dataSourceFactory = DefaultDataSource.Factory(context)
            val mediaSource = ProgressiveMediaSource.Factory(dataSourceFactory)
                .createMediaSource(MediaItem.fromUri(previewPath))
            
            Timber.tag("DEBUG_FLOW").d("FilterPreviewDialog: MediaSource created from: $previewPath")
            
            setMediaSource(mediaSource)
            repeatMode = Player.REPEAT_MODE_ALL
            playWhenReady = true
            
            Timber.tag("DEBUG_FLOW").d("FilterPreviewDialog: Player configured - repeat: ALL, playWhenReady: true")
            
            addListener(object : Player.Listener {
                override fun onPlaybackStateChanged(playbackState: Int) {
                    val stateString = when (playbackState) {
                        Player.STATE_IDLE -> "IDLE"
                        Player.STATE_BUFFERING -> "BUFFERING"
                        Player.STATE_READY -> "READY"
                        Player.STATE_ENDED -> "ENDED"
                        else -> "UNKNOWN"
                    }
                    Timber.tag("DEBUG_FLOW").d("FilterPreviewDialog: Player state changed to: $stateString")
                    
                    when (playbackState) {
                        Player.STATE_READY -> {
                            Timber.tag("DEBUG_FLOW").d("FilterPreviewDialog: Player ready, starting playback")
                            progressBar.visibility = View.GONE
                            loadingText.visibility = View.GONE
                            play()
                        }
                        Player.STATE_BUFFERING -> {
                            Timber.tag("DEBUG_FLOW").d("FilterPreviewDialog: Player buffering")
                            progressBar.visibility = View.VISIBLE
                            loadingText.visibility = View.VISIBLE
                        }
                        Player.STATE_ENDED -> {
                            Timber.tag("DEBUG_FLOW").d("FilterPreviewDialog: Playback ended (will loop)")
                        }
                        Player.STATE_IDLE -> {
                            Timber.tag("DEBUG_FLOW").d("FilterPreviewDialog: Player idle")
                        }
                    }
                }

                override fun onPlayerError(error: com.google.android.exoplayer2.PlaybackException) {
                    Timber.tag("DEBUG_FLOW").e("FilterPreviewDialog: ExoPlayer error: ${error.message}")
                    Timber.tag("DEBUG_FLOW").e("FilterPreviewDialog: Error type: ${error.errorCode}")
                    Toast.makeText(context, "Error playing preview", Toast.LENGTH_SHORT).show()
                    dismiss()
                }
                
                override fun onVideoSizeChanged(videoSize: VideoSize) {
                    super.onVideoSizeChanged(videoSize)
                    Timber.tag("DEBUG_FLOW").d("FilterPreviewDialog: Video size changed - width: ${videoSize.width}, height: ${videoSize.height}")
                    Timber.tag("DEBUG_FLOW").d("FilterPreviewDialog: Pixel aspect ratio: ${videoSize.pixelWidthHeightRatio}")
                }
                
                override fun onRenderedFirstFrame() {
                    super.onRenderedFirstFrame()
                    Timber.tag("DEBUG_FLOW").d("FilterPreviewDialog: First frame rendered!")
                    Timber.tag("DEBUG_FLOW").d("FilterPreviewDialog: PlayerView final dimensions: ${playerView.width}x${playerView.height}")
                }
            })
            
            Timber.tag("DEBUG_FLOW").d("FilterPreviewDialog: Calling prepare()")
            prepare()
        }
    }

    override fun onStop() {
        super.onStop()
        exoPlayer?.release()
        exoPlayer = null
    }

    override fun dismiss() {
        exoPlayer?.release()
        exoPlayer = null
        super.dismiss()
    }
}