package com.videomontage.freeeditingapps.view.sticker;

import android.app.Activity;
import android.graphics.drawable.BitmapDrawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.PopupWindow;

import com.videomontage.freeeditingapps.R;

import androidx.fragment.app.FragmentManager;

/**
 * <pre>
 *     author : Administrator (Jacket)
 *     e-mail : <EMAIL>
 *     time   : 2018/01/31
 *     desc   :
 *     version: 3.2
 * </pre>
 */

public class PopBubbleView_Third {
    private String TAG = PopBubbleView_Third.class.getSimpleName();

    private Activity context;
    private PopupWindow popupWindow;
    private View popupWindowView;
    private String reportType;
    private FragmentManager fragmentManager;

    public PopBubbleView_Third(Activity context, FragmentManager fragmentManager) {
        this.context = context;
        this.fragmentManager = fragmentManager;
        initPopupWindow();
    }

    /**
     * 初始化
     */
    public void initPopupWindow() {
        if (popupWindowView != null) {
            popupWindow.dismiss();
        }
        popupWindowView = LayoutInflater.from(context).inflate(R.layout.pop_bubble_view, null);
        popupWindow = new PopupWindow(popupWindowView, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT, true);
//        popupWindow.setAnimationStyle(R.style.popup_window_scale);
        popupWindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        // 菜单背景色。加了一点透明度
//        ColorDrawable dw = new ColorDrawable(0xddffffff);
//        popupWindow.setBackgroundDrawable(dw);
        popupWindow.setOutsideTouchable(true);
        popupWindow.setBackgroundDrawable(new BitmapDrawable());      //解决部分机型按back键无法退出popupwindow


        // 设置背景半透明
//        backgroundAlpha(0.7f);

        popupWindow.setOnDismissListener(new popupDismissListener());

        popupWindowView.setOnTouchListener(new View.OnTouchListener() {

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                /*
                 * if( popupWindow!=null && popupWindow.isShowing()){
                 * popupWindow.dismiss(); popupWindow=null; }
                 */
                // 这里如果返回true的话，touch事件将被拦截
                // 拦截后 PopupWindow的onTouchEvent不被调用，这样点击外部区域无法dismiss
                return false;
            }
        });


//        thirdExample();

    }

//    private void thirdExample(){
//        FragmentTransaction ft = fragmentManager.beginTransaction();
//        Fragment prev = fragmentManager.findFragmentByTag("dialog");
//        if (prev != null) {
//            ft.remove(prev);
//        }
//        ft.addToBackStack(null);
//
//        // Create and show the dialog.
//        TabbedDialog dialog = new TabbedDialog();
//        dialog.setStyle(DialogFragment.STYLE_NORMAL, R.style.Dialog_NoTitle);
//
//        dialog.show(ft,"dialog");
//
//
//    }


    /**
     * 设置添加屏幕的背景透明度
     *
     * @param bgAlpha
     */
    public void backgroundAlpha(float bgAlpha) {
        WindowManager.LayoutParams lp = ((Activity) context).getWindow().getAttributes();
        lp.alpha = bgAlpha; // 0.0-1.0
        ((Activity) context).getWindow().setAttributes(lp);
    }

    //    @OnClick({R.id.ll_bubble_one, R.id.ll_bubble_two, R.id.ll_bubble_three, R.id.ll_bubble_four, R.id.ll_bubble_five, R.id.ll_bubble_six, R.id.ll_bubble_seven, R.id.ll_bubble_eight})
//    public void onViewClicked(View view) {
//        switch (view.getId()) {
//            case R.id.ll_bubble_one:
//                if (bubbleSelectListener != null) {
//                    bubbleSelectListener.stickerSelect(0);
//                }
//                dimss();
//                break;
//            case R.id.ll_bubble_two:
//                if (bubbleSelectListener != null) {
//                    bubbleSelectListener.stickerSelect(1);
//                }
//                dimss();
//                break;
//            case R.id.ll_bubble_three:
//                if (bubbleSelectListener != null) {
//                    bubbleSelectListener.stickerSelect(2);
//                }
//                dimss();
//                break;
//            case R.id.ll_bubble_four:
//                if (bubbleSelectListener != null) {
//                    bubbleSelectListener.stickerSelect(3);
//                }
//                dimss();
//                break;
//            case R.id.ll_bubble_five:
//                if (bubbleSelectListener != null) {
//                    bubbleSelectListener.stickerSelect(4);
//                }
//                dimss();
//                break;
//            case R.id.ll_bubble_six:
//                if (bubbleSelectListener != null) {
//                    bubbleSelectListener.stickerSelect(5);
//                }
//                dimss();
//                break;
//            case R.id.ll_bubble_seven:
//                if (bubbleSelectListener != null) {
//                    bubbleSelectListener.stickerSelect(6);
//                }
//                dimss();
//                break;
//            case R.id.ll_bubble_eight:
//                if (bubbleSelectListener != null) {
//                    bubbleSelectListener.stickerSelect(7);
//                }
//                dimss();
//                break;
//        }
//    }



    class popupDismissListener implements PopupWindow.OnDismissListener {
        @Override
        public void onDismiss() {
            backgroundAlpha(1f);
        }
    }

    public void dimss() {
        if (popupWindow != null) {
            popupWindow.dismiss();
        }
    }

    public boolean isShowing() {
        return popupWindow.isShowing();
    }


    public void show() {
        if (popupWindow != null && !popupWindow.isShowing()) {
            popupWindow.showAtLocation(LayoutInflater.from(context).inflate(R.layout.activity_update_personal_info, null),
                    Gravity.BOTTOM, 0, 0);
        }



    }

    public interface BubbleSelectListener {
        void stickerSelect(int bubbleIndex);
    }

    public BubbleSelectListener bubbleSelectListener;

    public void setBubbleSelectListener(BubbleSelectListener bubbleSelectListener) {
        this.bubbleSelectListener = bubbleSelectListener;
    }
}
