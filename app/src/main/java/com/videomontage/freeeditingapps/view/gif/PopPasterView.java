package com.videomontage.freeeditingapps.view.gif;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.BitmapDrawable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.PopupWindow;

import com.videomontage.freeeditingapps.R;
import com.videomontage.freeeditingapps.adapter.PasterAdapter;


public class PopPasterView implements PasterAdapter.PasterItemSelectListener {
    private String TAG = PopPasterView.class.getSimpleName();
    private Context context;
    private PopupWindow popupWindow;
    private View popupWindowView;
    private String reportType;

    private RecyclerView recyclerView;

    private int[] images = new int[]{
            <PERSON>.drawable.aini, <PERSON><PERSON>drawable.<PERSON>, <PERSON><PERSON>drawable.bait<PERSON>, <PERSON><PERSON>drawable.b<PERSON>, <PERSON>.drawable.b<PERSON>, <PERSON>.drawable.<PERSON>, <PERSON>.drawable.zan, R.drawable.mudengkoudai, R.drawable.buyue,  R.drawable.nizaidouwo, R.drawable.gandepiaoliang, R.drawable.xiase
    };

    public PopPasterView(Context context) {
        this.context = context;
        initPopupWindow();
    }

    public void initPopupWindow() {
        if (popupWindowView != null) {
            popupWindow.dismiss();
        }
        popupWindowView = LayoutInflater.from(context).inflate(R.layout.pop_paster_view, null);

        recyclerView = popupWindowView.findViewById(R.id.recycler_view);

        popupWindow = new PopupWindow(popupWindowView, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT, true);
        popupWindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        popupWindow.setOutsideTouchable(true);
        popupWindow.setBackgroundDrawable(new BitmapDrawable());

        popupWindow.setOnDismissListener(() -> backgroundAlpha(1f));

        popupWindowView.setOnTouchListener((v, event) -> false);

        initView();
    }

    private void initView() {
        PasterAdapter pasterAdapter = new PasterAdapter(context, images);
        pasterAdapter.setPasterItemSelectListener(this);
        recyclerView.setAdapter(pasterAdapter);
        recyclerView.setLayoutManager(new GridLayoutManager(context, 4));
    }

    public void backgroundAlpha(float bgAlpha) {
        WindowManager.LayoutParams lp = ((Activity) context).getWindow().getAttributes();
        lp.alpha = bgAlpha;
        ((Activity) context).getWindow().setAttributes(lp);
    }

    public void dimss() {
        if (popupWindow != null) {
            popupWindow.dismiss();
        }
    }

    public boolean isShowing() {
        return popupWindow.isShowing();
    }

    public void show() {
        if (popupWindow != null && !popupWindow.isShowing()) {
            popupWindow.showAtLocation(LayoutInflater.from(context).inflate(R.layout.activity_update_personal_info, null),
                    Gravity.BOTTOM, 0, 0);
        }
    }

    public interface PasterSelectListener {
        void pasterSelect(int resourceId, int gifId);
    }

    PasterSelectListener pasterSelectListener;

    public void setPasterSelectListener(PasterSelectListener pasterSelectListener) {
        this.pasterSelectListener = pasterSelectListener;
    }

    @Override
    public void pasterItemSelect(int resourseId, int gifId) {
        pasterSelectListener.pasterSelect(resourseId, gifId);
        dimss();
    }
}

