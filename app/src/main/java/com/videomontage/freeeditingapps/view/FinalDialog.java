package com.videomontage.freeeditingapps.view;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Outline;
import android.graphics.Paint;
import android.media.ThumbnailUtils;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.provider.MediaStore;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.view.Window;
import android.view.animation.TranslateAnimation;

import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.source.ProgressiveMediaSource;
import com.google.android.exoplayer2.source.LoopingMediaSource;
import com.google.android.exoplayer2.source.MediaSource;
import com.google.android.exoplayer2.ui.PlayerView;
import com.google.android.exoplayer2.upstream.DefaultDataSourceFactory;
import com.videomontage.freeeditingapps.R;
import com.videomontage.freeeditingapps.application.MyApplication;
import com.videomontage.freeeditingapps.model.TextOnVideo;

import java.util.ArrayList;
import java.util.HashMap;

import ja.burhanrashid52.photoeditor.OnPhotoEditorListener;
import ja.burhanrashid52.photoeditor.PhotoEditor;
import ja.burhanrashid52.photoeditor.PhotoEditorView;
import ja.burhanrashid52.photoeditor.TextStyleBuilder;
import ja.burhanrashid52.photoeditor.ViewType;

public class FinalDialog extends Dialog implements View.OnClickListener, OnPhotoEditorListener {

    private Activity c;
    private Dialog d;
    private String videoPath;
    private FinalDialogListener customDialogListener;
    private PlayerView videoView;
    private ExoPlayer player;
    private PhotoEditorView mPhotoEditorView;
    PhotoEditor mPhotoEditor;
    public static ArrayList<TextOnVideo> textOnVideos = new ArrayList<>();
    private HashMap<String, TextOnVideo> textOnVideoHashMap = new HashMap<>();
    View myView;
    boolean isUp;

    public FinalDialog(Activity a, String videoPath, FinalDialogListener customDialogListener) {
        super(a, R.style.DialogTheme);
//        super(a);
        this.c = a;
        this.videoPath = videoPath;
        this.customDialogListener = customDialogListener;


    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.final_dialog);

        getWindow()
                .setLayout(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                );

        initVideoView(videoPath);

        mPhotoEditorView = findViewById(R.id.photoEditorView);

        mPhotoEditor = new PhotoEditor.Builder(getContext(), mPhotoEditorView)
                .setPinchTextScalable(true) // set flag to make text scalable when pinch
                //.setDefaultTextTypeface(mTextRobotoTf)
                //.setDefaultEmojiTypeface(mEmojiTypeFace)
                .build(); // build photo editor sdk

        mPhotoEditor.setOnPhotoEditorListener(this);

        addTextOnVideo();

        Bitmap thumb = ThumbnailUtils.createVideoThumbnail(videoPath,
                MediaStore.Images.Thumbnails.MINI_KIND);

        mPhotoEditorView.getSource().setImageBitmap(makeTransparent(thumb, 0));

        myView = findViewById(R.id.my_view);

        myView.setVisibility(View.INVISIBLE);
        isUp = false;

    }

    public void slideUp(View view){
        view.setVisibility(View.VISIBLE);
        TranslateAnimation animate = new TranslateAnimation(
                0,                 // fromXDelta
                0,                 // toXDelta
                view.getHeight(),  // fromYDelta
                0);                // toYDelta
        animate.setDuration(500);
        animate.setFillAfter(true);
        view.startAnimation(animate);
    }

    // slide the view from its current position to below itself
    public void slideDown(View view){
        TranslateAnimation animate = new TranslateAnimation(
                0,                 // fromXDelta
                0,                 // toXDelta
                0,                 // fromYDelta
                view.getHeight()); // toYDelta
        animate.setDuration(500);
        animate.setFillAfter(true);
        view.startAnimation(animate);
    }

    public void onSlideViewButtonClick() {
        if (isUp) {
            slideDown(myView);
        } else {
            slideUp(myView);
        }
        isUp = !isUp;
    }

    private void addTextOnVideo() {
        final TextStyleBuilder styleBuilder = new TextStyleBuilder();
        styleBuilder.withTextColor(R.color.blue_color_picker);


        TextOnVideo textOnVideo = new TextOnVideo("Proba", 0, 0, 0, 0, R.color.blue_color_picker);
        mPhotoEditor.addText(textOnVideo.getText(), styleBuilder);
        textOnVideoHashMap.put(textOnVideo.getText(), textOnVideo);
    }

    public Bitmap makeTransparent(Bitmap src, int value) {
        int width = src.getWidth();
        int height = src.getHeight();
        Bitmap transBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(transBitmap);
        canvas.drawARGB(0, 0, 0, 0);
        // config paint
        final Paint paint = new Paint();
        paint.setAlpha(value);
        canvas.drawBitmap(src, 0, 0, paint);
        return transBitmap;
    }

    @Override
    public void onClick(View v) {
//        switch (v.getId()) {
//            case R.id.cancelTv:
//
//                dismiss();
//                if (customDialogListener != null) {
//                    customDialogListener.onDismiss();
//                }
//                break;
//        }
    }

    private void initVideoView(String path) {

        Log.d("ddd", "initVideoView: " + path);
        videoView = findViewById(R.id.videoView);


        try {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (player == null) {
                        Context context = getContext(); // or requireContext(), or use your Activity
                        player = new ExoPlayer.Builder(context).build();
                        videoView.setShutterBackgroundColor(Color.TRANSPARENT);
                    }
                    MediaItem mediaItem = MediaItem.fromUri(Uri.parse(path));
                    MediaSource assetVideo = new ProgressiveMediaSource.Factory(
                            new DefaultDataSourceFactory(MyApplication.self(), "MyExoplayer")
                    ).createMediaSource(mediaItem);
                    player.prepare(new LoopingMediaSource(assetVideo));
                    videoView.setPlayer(player);

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                        videoView.setOutlineProvider(new ViewOutlineProvider() {
                            @Override
                            public void getOutline(View view, Outline outline) {
                                outline.setRoundRect(0, 0, view.getWidth(), view.getHeight(), 15);
                            }
                        });
                        videoView.setClipToOutline(true);
                    }

                    player.setPlayWhenReady(true);
                }
            }, 1000);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public void dismiss() {

        if (player != null) {
            player.setPlayWhenReady(false);

            releasePlayer();
        }
        super.dismiss();
    }

    private void releasePlayer() {
        if (player != null) {
            player.stop();
            player.release();
            player = null;
        }
    }

    @Override
    public void onEditTextChangeListener(View rootView, String text, int colorCode) {

        onSlideViewButtonClick();
//        TextEditorDialogFragment textEditorDialogFragment =
//                TextEditorDialogFragment.show((AppCompatActivity) c, text, colorCode);
//        textEditorDialogFragment.setOnTextEditorListener(new TextEditorDialogFragment.TextEditor() {
//            @Override
//            public void onDone(String inputText, int colorCode) {
//                final TextStyleBuilder styleBuilder = new TextStyleBuilder();
//                styleBuilder.withTextColor(colorCode);
//
//                mPhotoEditor.editText(rootView, inputText, styleBuilder);
////                mTxtCurrentTool.setText(R.string.label_text);
//            }
//        });
    }

    @Override
    public void onAddViewListener(ViewType viewType, int numberOfAddedViews) {

    }

    @Override
    public void onRemoveViewListener(ViewType viewType, int numberOfAddedViews) {

    }

    @Override
    public void onStartViewChangeListener(ViewType viewType) {

    }

    @Override
    public void onStopViewChangeListener(ViewType viewType) {


    }

    @Override
    public void onTextStopMoving(String text, int xPosition, int yPosition) {
        Log.d("dddd", "onTextStopMoving: " + text);
        Log.d("dddd", "onTextStopMoving xPosition: " + xPosition);
        Log.d("dddd", "onTextStopMoving yPosition: " + yPosition);

        TextOnVideo textOnVideo = textOnVideoHashMap.get(text);
        textOnVideo.setxPosition(xPosition);
        textOnVideo.setyPosition(yPosition);
        textOnVideoHashMap.put(text, textOnVideo);
    }


    public interface FinalDialogListener {
        void onDismiss();
    }
}
