package com.videomontage.freeeditingapps.view.bubble;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.BitmapDrawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.PopupWindow;

import com.videomontage.freeeditingapps.R;

public class PopBubbleView {
    private String TAG = PopBubbleView.class.getSimpleName();

    private Context context;
    private PopupWindow popupWindow;
    private View popupWindowView;
    private String reportType;

    private LinearLayout llBubbleOne;
    private LinearLayout llBubbleTwo;
    private LinearLayout llBubbleThree;
    private LinearLayout llBubbleFour;
    private LinearLayout llBubbleFive;
    private LinearLayout llBubbleSix;
    private LinearLayout llBubbleSeven;
    private LinearLayout llBubbleEight;

    public PopBubbleView(Context context) {
        this.context = context;
        initPopupWindow();
    }

    public void initPopupWindow() {
        if (popupWindowView != null) {
            popupWindow.dismiss();
        }
        popupWindowView = LayoutInflater.from(context).inflate(R.layout.pop_bubble_view, null);
        popupWindow = new PopupWindow(popupWindowView, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT, true);
        popupWindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        popupWindow.setOutsideTouchable(true);
        popupWindow.setBackgroundDrawable(new BitmapDrawable());

        llBubbleOne = popupWindowView.findViewById(R.id.ll_bubble_one);
        llBubbleTwo = popupWindowView.findViewById(R.id.ll_bubble_two);
        llBubbleThree = popupWindowView.findViewById(R.id.ll_bubble_three);
        llBubbleFour = popupWindowView.findViewById(R.id.ll_bubble_four);
        llBubbleFive = popupWindowView.findViewById(R.id.ll_bubble_five);
        llBubbleSix = popupWindowView.findViewById(R.id.ll_bubble_six);
        llBubbleSeven = popupWindowView.findViewById(R.id.ll_bubble_seven);
        llBubbleEight = popupWindowView.findViewById(R.id.ll_bubble_eight);

        llBubbleOne.setOnClickListener(view -> onViewClicked(view));
        llBubbleTwo.setOnClickListener(view -> onViewClicked(view));
        llBubbleThree.setOnClickListener(view -> onViewClicked(view));
        llBubbleFour.setOnClickListener(view -> onViewClicked(view));
        llBubbleFive.setOnClickListener(view -> onViewClicked(view));
        llBubbleSix.setOnClickListener(view -> onViewClicked(view));
        llBubbleSeven.setOnClickListener(view -> onViewClicked(view));
        llBubbleEight.setOnClickListener(view -> onViewClicked(view));
    }

    public void backgroundAlpha(float bgAlpha) {
        WindowManager.LayoutParams lp = ((Activity) context).getWindow().getAttributes();
        lp.alpha = bgAlpha;
        ((Activity) context).getWindow().setAttributes(lp);
    }

    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ll_bubble_one:
                bubbleSelectListener.bubbleSelect(0);
                dimss();
                break;
            case R.id.ll_bubble_two:
                bubbleSelectListener.bubbleSelect(1);
                dimss();
                break;
            case R.id.ll_bubble_three:
                bubbleSelectListener.bubbleSelect(2);
                dimss();
                break;
            case R.id.ll_bubble_four:
                bubbleSelectListener.bubbleSelect(3);
                dimss();
                break;
            case R.id.ll_bubble_five:
                bubbleSelectListener.bubbleSelect(4);
                dimss();
                break;
            case R.id.ll_bubble_six:
                bubbleSelectListener.bubbleSelect(5);
                dimss();
                break;
            case R.id.ll_bubble_seven:
                bubbleSelectListener.bubbleSelect(6);
                dimss();
                break;
            case R.id.ll_bubble_eight:
                bubbleSelectListener.bubbleSelect(7);
                dimss();
                break;
        }
    }

    class popupDismissListener implements PopupWindow.OnDismissListener {
        @Override
        public void onDismiss() {
            backgroundAlpha(1f);
        }
    }

    public void dimss() {
        if (popupWindow != null) {
            popupWindow.dismiss();
        }
    }

    public boolean isShowing() {
        return popupWindow.isShowing();
    }

    public void show() {
        if (popupWindow != null && !popupWindow.isShowing()) {
            popupWindow.showAtLocation(LayoutInflater.from(context).inflate(R.layout.activity_update_personal_info, null),
                    Gravity.BOTTOM, 0, 0);
        }
    }

    public interface BubbleSelectListener {
        void bubbleSelect(int bubbleIndex);
    }

    private BubbleSelectListener bubbleSelectListener;

    public void setBubbleSelectListener(BubbleSelectListener bubbleSelectListener) {
        this.bubbleSelectListener = bubbleSelectListener;
    }
}
