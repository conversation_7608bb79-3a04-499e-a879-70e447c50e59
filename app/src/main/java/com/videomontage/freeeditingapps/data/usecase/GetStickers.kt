package com.videomontage.freeeditingapps.data.usecase

import android.content.Context
import android.util.Log
//import com.my.dropbox_lib.data.local.database.model.StickerCache
//import com.my.dropbox_lib.data.local.database.model.StickerGroupCache
//import com.my.dropbox_lib.domain.LoadStickers
import com.videomontage.freeeditingapps.fragment.viewmodel.StateLiveData

class GetStickers {

//    companion object Accessor {
//        private val _stateListOfStickers: StateLiveData<List<List<StickerCache>>> = StateLiveData()
//        val stateListOfStickers: StateLiveData<List<List<StickerCache>>>
//            get() = _stateListOfStickers
//
//        private val _stateListOfStickerGroupCache: StateLiveData<List<StickerGroupCache>> = StateLiveData()
//        val stateListOfStickerGroupCache: StateLiveData<List<StickerGroupCache>>
//            get() = _stateListOfStickerGroupCache
//
//    }
//
//    fun getStickers(context: Context) {
//        _stateListOfStickerGroupCache.postLoading()
//        _stateListOfStickers.postLoading()
//        LoadStickers.getStickerGroups(context) { stickerGroups ->
//
//            Log.d("ddd", "getStickers: " + stickerGroups)
//            _stateListOfStickerGroupCache.postSuccess(stickerGroups.sortedBy { stickerGroupCache -> stickerGroupCache.groupId })
//            var sortedGroupedStickersList = mutableListOf<List<StickerCache>>()
//
//            stickerGroups.map { stickerGroup ->
//                LoadStickers.getStickersOfSpecificGroup(stickerGroup, context) {
//
//                    sortedGroupedStickersList.addAll(listOf(it))
//                    sortedGroupedStickersList.sortBy { it.get(0).groupId }
//
//                }
//            }
//            _stateListOfStickers.postSuccess(sortedGroupedStickersList)
//
//        }
//    }
}