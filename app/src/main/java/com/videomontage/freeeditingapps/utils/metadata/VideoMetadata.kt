package com.videomontage.freeeditingapps.utils.metadata

import android.media.MediaMetadataRetriever

object VideoMetadata {
    const val VIDEO_ROTATION = "video rotation"
    const val VIDEO_WIDTH = "video width"
    const val VIDEO_HEIGHT = "video height"

    fun initVideoInfo(inputVideoPath: String): Map<String, Int> {
        val videoRotation: Int
        val retr = MediaMetadataRetriever()
        retr.setDataSource(inputVideoPath)
        val width = retr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)
        val height = retr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)
        val rotation = retr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION)

        val metadataMap = mutableMapOf<String, Int>()
        metadataMap[VIDEO_WIDTH] = width!!.toInt()
        metadataMap[VIDEO_HEIGHT] = height!!.toInt()

        if ((rotation == "180") && width!!.toInt() > height!!.toInt()) {
            videoRotation = 180
        } else if ((rotation == "90") && width!!.toInt() > height!!.toInt()) {
            videoRotation = 0
            metadataMap[VIDEO_WIDTH] = height.toInt()
            metadataMap[VIDEO_HEIGHT] = width.toInt()
        } else {
            videoRotation = rotation!!.toInt()
        }

        metadataMap[VIDEO_ROTATION] = videoRotation



        return metadataMap
    }
}