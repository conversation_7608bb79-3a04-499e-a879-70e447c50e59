/*
 * Copyright (C) 2012 <PERSON> Licensed under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with the
 * License. You may obtain a copy of the License at
 * http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law
 * or agreed to in writing, software distributed under the License is
 * distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */
package com.videomontage.freeeditingapps.utils

import android.provider.MediaStore

/**
 * Holds all of the sort orders for each list type.
 *
 * <AUTHOR> (<EMAIL>)
 */
object SortOrder {
    const val ID_SONG_A_Z = 0
    const val ID_SONG_Z_A = 1
    const val ID_SONG_DATE_ADDED = 2
    const val ID_SONG_DATE_ADDED_DESCENDING = 3

    /**
     * Song sort order entries.
     */
    interface SongSortOrder {
        companion object {
            /* Song sort order A-Z */
            const val SONG_A_Z = MediaStore.Video.Media.TITLE

            /* Song sort order Z-A */
            const val SONG_Z_A = SONG_A_Z + " DESC"

            /* Song sort order date added */
            const val SONG_DATE = MediaStore.Video.Media.DATE_ADDED + " ASC"

            /* Song sort order date added DESC */
            const val SONG_DATE_DESC = MediaStore.Video.Media.DATE_ADDED + " DESC"
        }
    }
}