package com.videomontage.freeeditingapps.utils;


import com.videomontage.freeeditingapps.BuildConfig;

public class FabricEvents {
    private static final boolean IS_DEBUG = BuildConfig.DEBUG;
    //------------------------ RAITING ------------------------------------
    public static final String RATING_VALUE_PRESSED = "Rating Value Pressed";
    public static final String NUMBER_OF_STARS = "Number of stars";

    //------------------------ RAITING ------------------------------------
    public static final String RATING_VALUE_SHOWED = "Rating Dialog Showed";
    public static final String NUMBER_SHOWED = "Rating showed";

    //------------------------ MAIN MENU ------------------------------------
    public static final String MAIN_MANU_PRESSED = "Main menu pressed";
    public static final String MAIN_MENU_OPTION = "Main menu option";

    //------------------------ EFFECT MENU ------------------------------------
    public static final String EFFECT_MENU_PRESSED = "Effect menu pressed";
    public static final String EFFECT_MENU_OPTION = "Effect menu option";

    public static void logEvent(String eventname, String attributeName, int attributeValue ) {
//        if (IS_DEBUG)return;
//        Answers.getInstance().logCustom(new CustomEvent(eventname)
//                .putCustomAttribute(attributeName, attributeValue));
    }
    public static void logEvent(String eventname, String attributeName, String attributeValue ) {
//        if (IS_DEBUG)return;
//        Answers.getInstance().logCustom(new CustomEvent(eventname)
//                .putCustomAttribute(attributeName, attributeValue));
    }
}
