package com.videomontage.freeeditingapps.utils.text_overlay;

import android.graphics.Paint;
import android.util.Log;

import java.util.ArrayList;

public class TextUtil {


    /**
     * Automatically split text
     *
     * @param content Text to be split
     * @param p       Pen, used to measure the width of text based on the font
     * @param width   Specified width
     * @return 一An array of strings, holding the text of each line
     */
    public static String[] autoSplitSecond(String content, Paint p, float width) {
        //Split content to array
        String[] words = content.split(" ");

        //Initialize empty list of strings, we will store line by line
        ArrayList<String> textLines = new ArrayList<>();
        int currentWordIndex = 0;

        StringBuilder stringBuilder = new StringBuilder();

        //Add word by word to string builder until max width
        while (currentWordIndex < words.length) {

            //Check if the word is first in the line, if it is than we don't need white space
            String currentWord = stringBuilder.length() == 0 ? words[currentWordIndex] : " " + words[currentWordIndex];

            //Check if the new possible added word exceed width
            if (p.measureText(stringBuilder.toString() + currentWord) < width) {
                stringBuilder
                        .append(" ")
                        .append(words[currentWordIndex]);
            } else { // If it exceed width added existing stringBuilder to the textLines and redo it from the begging
                textLines.add(stringBuilder.toString());
                stringBuilder = new StringBuilder();
                stringBuilder.append(words[currentWordIndex]);

            }

            if (currentWordIndex == words.length - 1)
                textLines.add(stringBuilder.toString());


            currentWordIndex++;
        }

        return textLines.toArray(new String[textLines.size()]);
    }
}
