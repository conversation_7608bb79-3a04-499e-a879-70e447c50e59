package com.videomontage.freeeditingapps.utils

import android.content.Context
import android.graphics.Bitmap
import jp.co.cyberagent.android.gpuimage.GPUImage
import jp.co.cyberagent.android.gpuimage.filter.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Manager class for applying image filters using GPUImage
 * Replaces the deprecated BitmapFilter functionality
 */
class FilterManager(private val context: Context) {
    
    private val gpuImage: GPUImage by lazy {
        GPUImage(context)
    }
    
    /**
     * Filter types that match the original BitmapFilter styles
     */
    enum class FilterType {
        SKETCH_STYLE,
        OIL_STYLE,
        CARTOON_STYLE,
        PENCIL_SKETCH,
        SEPIA,
        BLACK_WHITE,
        VINTAGE,
        BLUR,
        SHARPEN,
        EMBOSS
    }
    
    /**
     * Apply a filter to a bitmap
     * @param inputBitmap The source bitmap
     * @param filterType The type of filter to apply
     * @param intensity Filter intensity (0.0 to 1.0), not used for all filters
     * @return The filtered bitmap
     */
    suspend fun applyFilter(
        inputBitmap: Bitmap,
        filterType: FilterType,
        intensity: Float = 1.0f
    ): Bitmap = withContext(Dispatchers.Default) {
        gpuImage.setImage(inputBitmap)
        
        val filter = when (filterType) {
            FilterType.SKETCH_STYLE -> GPUImageSketchFilter()
            FilterType.OIL_STYLE -> GPUImageKuwaharaFilter().apply {
                setRadius(5) // Equivalent to BitmapFilter oil style parameter
            }
            FilterType.CARTOON_STYLE -> GPUImageToonFilter()
            FilterType.PENCIL_SKETCH -> GPUImageSketchFilter().apply {
                // Lighter sketch effect
                // Note: GPUImage doesn't have exact pencil sketch, using sketch with adjustments
            }
            FilterType.SEPIA -> GPUImageSepiaToneFilter()
            FilterType.BLACK_WHITE -> GPUImageGrayscaleFilter()
            FilterType.VINTAGE -> GPUImageVignetteFilter()
            FilterType.BLUR -> GPUImageGaussianBlurFilter().apply {
                setBlurSize(intensity * 2f) // Scale intensity to blur size
            }
            FilterType.SHARPEN -> GPUImageSharpenFilter().apply {
                setSharpness(intensity * 2f) // Scale intensity to sharpness
            }
            FilterType.EMBOSS -> GPUImageEmbossFilter()
        }
        
        gpuImage.setFilter(filter)
        gpuImage.getBitmapWithFilterApplied()
    }
    
    /**
     * Apply multiple filters in sequence
     * @param inputBitmap The source bitmap
     * @param filterTypes List of filters to apply in order
     * @return The filtered bitmap
     */
    suspend fun applyMultipleFilters(
        inputBitmap: Bitmap,
        filterTypes: List<FilterType>
    ): Bitmap = withContext(Dispatchers.Default) {
        var resultBitmap = inputBitmap
        
        filterTypes.forEach { filterType ->
            resultBitmap = applyFilter(resultBitmap, filterType)
        }
        
        resultBitmap
    }
    
    /**
     * Apply custom filter group for complex effects
     * @param inputBitmap The source bitmap
     * @param filters List of GPUImageFilter to apply
     * @return The filtered bitmap
     */
    suspend fun applyCustomFilterGroup(
        inputBitmap: Bitmap,
        filters: List<GPUImageFilter>
    ): Bitmap = withContext(Dispatchers.Default) {
        gpuImage.setImage(inputBitmap)
        
        val filterGroup = GPUImageFilterGroup(filters)
        gpuImage.setFilter(filterGroup)
        
        gpuImage.getBitmapWithFilterApplied()
    }
    
    /**
     * Get a preview bitmap with reduced size for performance
     * @param inputBitmap The source bitmap
     * @param filterType The type of filter to apply
     * @param maxSize Maximum dimension for the preview
     * @return The filtered preview bitmap
     */
    suspend fun getFilterPreview(
        inputBitmap: Bitmap,
        filterType: FilterType,
        maxSize: Int = 512
    ): Bitmap = withContext(Dispatchers.Default) {
        // Scale down for preview if needed
        val scaledBitmap = if (inputBitmap.width > maxSize || inputBitmap.height > maxSize) {
            val scale = maxSize.toFloat() / maxOf(inputBitmap.width, inputBitmap.height)
            Bitmap.createScaledBitmap(
                inputBitmap,
                (inputBitmap.width * scale).toInt(),
                (inputBitmap.height * scale).toInt(),
                true
            )
        } else {
            inputBitmap
        }
        
        applyFilter(scaledBitmap, filterType)
    }
    
    /**
     * Clean up resources
     */
    fun release() {
        // GPUImage handles cleanup internally, but we can force it if needed
        gpuImage.deleteImage()
    }
    
    companion object {
        /**
         * Map old BitmapFilter style constants to new FilterType
         */
        fun mapLegacyFilterType(legacyType: Int): FilterType {
            return when (legacyType) {
                5 -> FilterType.SKETCH_STYLE  // BitmapFilter.SKETCH_STYLE
                6 -> FilterType.OIL_STYLE     // BitmapFilter.OIL_STYLE with parameter 5
                else -> FilterType.SKETCH_STYLE // Default
            }
        }
    }
}