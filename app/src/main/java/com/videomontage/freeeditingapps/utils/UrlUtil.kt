package com.videomontage.freeeditingapps.utils

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.widget.Toast
import androidx.annotation.StringRes

object UrlUtil {

    private fun isAnyBrowserInstalled(context: Context): Boolean {
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse("http://www.google.com"))
        val packageManager = context.packageManager
        
        // Try multiple approaches to find browsers for Android 11+ compatibility
        // 1. Try with MATCH_ALL flag (Android 11+ compatibility)
        val listAll = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            packageManager.queryIntentActivities(intent, PackageManager.MATCH_ALL)
        } else {
            packageManager.queryIntentActivities(intent, 0)
        }
        
        // 2. Try with default flag
        val listDefault = packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY)
        
        // 3. Try with no flags
        val listNoFlags = packageManager.queryIntentActivities(intent, 0)
        
        // Use the list with most results
        val list = when {
            listAll.isNotEmpty() -> listAll
            listDefault.isNotEmpty() -> listDefault
            else -> listNoFlags
        }
        
        return list.isNotEmpty()
    }

    fun openLinkIfBrowserInstalled(context: Context, url: String, @StringRes errorMessage: Int) {
        if (isAnyBrowserInstalled(context)) {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            
            try {
                context.startActivity(intent)
            } catch (e: Exception) {
                Toast.makeText(context, "Failed to open browser: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        } else {
            Toast.makeText(context, context.resources.getString(errorMessage), Toast.LENGTH_SHORT).show()
        }
    }
}