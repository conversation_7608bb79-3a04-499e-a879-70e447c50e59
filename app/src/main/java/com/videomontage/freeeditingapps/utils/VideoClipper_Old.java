package com.videomontage.freeeditingapps.utils;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaExtractor;
import android.media.MediaFormat;
import android.media.MediaMetadataRetriever;
import android.media.MediaMuxer;
import android.media.MediaScannerConnection;
import android.os.Build;
import android.os.Environment;
import android.util.Log;
import android.widget.Toast;

import com.arthenica.mobileffmpeg.Config;
import com.arthenica.mobileffmpeg.ExecuteCallback;
import com.arthenica.mobileffmpeg.LogCallback;
import com.arthenica.mobileffmpeg.LogMessage;
import com.videomontage.freeeditingapps.R;
import com.videomontage.freeeditingapps.model.ComModel;
import com.videomontage.freeeditingapps.view.BaseImageView;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.arthenica.mobileffmpeg.Config.RETURN_CODE_CANCEL;
import static com.arthenica.mobileffmpeg.Config.RETURN_CODE_SUCCESS;


@TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR2)
public class VideoClipper_Old {
    private static final String TAG = "VideoClipper";

    final int TIMEOUT_USEC = 0;
    private String mInputVideoPath;
    private String mOutputVideoPath;

    MediaCodec videoDecoder;
    MediaCodec videoEncoder;
    MediaCodec audioDecoder;
    MediaCodec audioEncoder;

    MediaExtractor mVideoExtractor;
    MediaExtractor mAudioExtractor;
    MediaMuxer mMediaMuxer;
    static ExecutorService executorService = Executors.newFixedThreadPool(4);
    int muxVideoTrack = -1;
    int muxAudioTrack = -1;
    int videoTrackIndex = -1;
    int audioTrackIndex = -1;
    long startPosition;
    long clipDur;
    int videoWidth;
    int videoHeight;
    int videoRotation;
    MediaFormat videoFormat;
    MediaFormat audioFormat;
    boolean isOpenBeauty;
    boolean videoFinish = false;
    boolean audioFinish = false;
    boolean released = false;
    long before;
    long after;
    Object lock = new Object();
    boolean muxStarted = false;
    OnVideoCutFinishListener listener;
    private Context context;
    private String[] newCommand;
    private File file;
    private ArrayList<String> exeCmd;


    public VideoClipper_Old(Context context) {
        this.context = context;
    }


    private void execFFmpegBinary(final Object command, String path, String title) {


        if (ComModel.isNewApiRequired()) {
            String myCommand = (String) command;
            Log.d(TAG, "execFFmpegBinary: " + myCommand);

            long executionId = com.arthenica.mobileffmpeg.FFmpeg.executeAsync(myCommand, new ExecuteCallback() {

                @Override
                public void apply(final long executionId, final int rc) {
                    if (rc == RETURN_CODE_SUCCESS) {
                        Log.i(Config.TAG, "Command execution completed successfully.");

                        File file = new File(Environment.getExternalStorageDirectory() + File.separator + "/Movies/Montage/" + title);
                        MediaScannerConnection.scanFile(context, new String[] { file.getAbsolutePath() }, new String[] { "video/mp4" }, null);
                        listener.onFinish();


                    } else if (rc == RETURN_CODE_CANCEL) {
                        Log.i(Config.TAG, "Command execution cancelled by user.");
                        Toast.makeText(context, "FAILURE", Toast.LENGTH_SHORT).show();

//                        afterFFmpegFailure();

                    } else {
                        Log.i(Config.TAG, String.format("Command execution failed with rc=%d and the output below.", rc));
                        Config.printLastCommandOutput(Log.INFO);
                        Toast.makeText(context, "FAILURE", Toast.LENGTH_SHORT).show();

//                        afterFFmpegFailure();


                    }
                }
            });

            Config.enableLogCallback(new LogCallback() {
                public void apply(LogMessage message) {
//                    int durationFile = (int) Utils.getProgress(message.getText(), Long.parseLong(videoModel.getDuration()) / 1000);
//                    float percent = durationFile / (Float.parseFloat(videoModel.getDuration()) / 1000);
//                    if (progressDialog != null) {
//                        progressDialog.setProgress((int) (percent * 100));
//                    }
                }
            });

        }
    }





    public void setInputVideoPath(String inputPath) {
        mInputVideoPath = inputPath;
        initVideoInfo();
    }

    public void setOutputVideoPath(String outputPath) {
        mOutputVideoPath = outputPath;
    }


    public void setOnVideoCutFinishListener(OnVideoCutFinishListener listener) {
        this.listener = listener;
    }


    private ArrayList<BaseImageView> mViews = new ArrayList<>();
    private Resources mResources;

    public void clipVideo(long startPosition, long clipDur, ArrayList<BaseImageView> views, Resources resources) throws IOException {
        mViews = views;
        mResources = resources;


        debugMethod();

    }

//    private void applyWaterMark() {
//
////        save(context);
////        productionMethod();
//
//    }

    private void debugMethod() {

        StringBuilder sbDestinationCommand = new StringBuilder();
        StringBuilder sbFilterComplexCommand;

        StringBuilder sbInputCommand = new StringBuilder();
        sbInputCommand.append("-y ");
        sbInputCommand.append("-i ");
        sbInputCommand.append(mInputVideoPath);


        for (int i = 0; i < mViews.size(); i++) {
            sbInputCommand.append(" -i ");
            sbInputCommand.append(getOverlayPath(i, mViews.get(i).isGif()));

        }


        sbFilterComplexCommand = getFilterComplexCommand();

//        if (true)return;


        sbDestinationCommand.append("\" -c:v");
        sbDestinationCommand.append(" libx264");
        sbDestinationCommand.append(" -preset");
        sbDestinationCommand.append(" ultrafast ");

        String fileName = System.currentTimeMillis() + ".mp4";

        File output = new File(Environment.getExternalStoragePublicDirectory(
                Environment.DIRECTORY_MOVIES).toString()
                + File.separator + "Montage" + File.separator
                + fileName);
        try {
            output.createNewFile();

        } catch (Exception e) {
            e.printStackTrace();
        }

        sbDestinationCommand.append(output.getAbsolutePath());


        execFFmpegBinary(sbInputCommand.toString() + sbFilterComplexCommand.toString() + sbDestinationCommand.toString(), output.getAbsolutePath(), fileName);

    }

    private StringBuilder getFilterComplexCommand() {
        StringBuilder sb = new StringBuilder();

        sb.append(" -filter_complex \"");


        for (int i = 0; i < mViews.size(); i++) {
            BaseImageView baseImageView = mViews.get(i);
            boolean rotated = baseImageView.getRotateDegree() != 0.0;

            int inputPos = i + 1;
            String filterSeparator = inputPos < mViews.size() ? "[vid" + (inputPos + 1) + "];" : "";
            System.out.println("Rotated: " + rotated);

            //koristiti ovo kad je gif u pitanju sto se skejlovanja tice
            int iw = (int) (baseImageView.getViewWidth() / 630 * videoWidth);
            int ih = (int) (baseImageView.getViewHeight() / 1120 * videoHeight);

            if (rotated) {
                float angle = baseImageView.getAngle() * -1;

                String main = i == 0 ? "[0:v]" : "[vid" + inputPos + "]";
                String scale = baseImageView.isGif() ? "scale=" + iw + ":" + ih : "scale=iw:ih";

                sb.append(
//                        "[" + inputPos + ":v] scale=iw:ih [scaled" + inputPos + "];" +
                        "[" + inputPos + ":v]" + scale + "[scaled" + inputPos + "];" +
                                "[scaled" + inputPos + "] rotate=" + angle + "*PI/180:ow=rotw(" + angle + "*PI/180):oh=roth(" + angle + "*PI/180):c=0x00000000 [rotated" + inputPos + "];[rotated" + inputPos + "] format=yuva420p,colorchannelmixer=aa=1.0 [mix" + inputPos + "];" +
                                main + "[mix" + inputPos + "]"
                                + "overlay="
                                + ((baseImageView.getLeftBottomX() / 630 * videoWidth)) + "+" + baseImageView.getViewHeight() + "*sin(PI/180)"
                                + ":" + (baseImageView.getLowestY() / 1120 * videoHeight)
                                + ":enable='between(t," + baseImageView.getStartTime() / 1000 + "," + baseImageView.getEndTime() / 1000 + ")'" + filterSeparator);

            } else {
                if (i == 0)
                    sb.append("[0:v]");
                else
                    sb.append("[vid" + inputPos + "]");
                sb.append(
                        "[" + inputPos + ":v]"
                                + "overlay="
                                + ((baseImageView.getLeftBottomX() / 630 * videoWidth))
                                + ":" + (baseImageView.getLowestY() / 1120 * videoHeight)
                                + ":enable='between(t," + baseImageView.getStartTime() / 1000 + "," + baseImageView.getEndTime() / 1000 + ")'" + filterSeparator);


            }
        }

        return sb;
    }

    private static void copyInputStreamToFile(InputStream inputStream, File file)
            throws IOException {

        // append = false
        try (FileOutputStream outputStream = new FileOutputStream(file, false)) {
            int read;
            byte[] bytes = new byte[8192];
            while ((read = inputStream.read(bytes)) != -1) {
                outputStream.write(bytes, 0, read);
            }
        }

    }

    private String getOverlayPath(int index, boolean isGif) {
        File file;

        if (isGif) {
            InputStream inputStream = context.getResources().openRawResource(R.raw.dengliao);

//            String extStorageDirectory = Environment.getExternalStorageDirectory().toString();
            File cacheDir = new File(context.getCacheDir(), "temp_images");
            if (!cacheDir.exists())
                cacheDir.mkdir();

            file = new File(cacheDir, System.currentTimeMillis() + "_temp.gif");

            try {
                copyInputStreamToFile(inputStream, file);
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            BaseImageView baseImageView = mViews.get(index);

            boolean rotated = baseImageView.getAngle() != 0.0;

            Bitmap bm = Bitmap.createScaledBitmap(
                    rotated ? baseImageView.getmBitmap() : baseImageView.getBitmap(),
                    (int) (baseImageView.getViewWidth() / 630 * videoWidth), (int) (baseImageView.getViewHeight() / 1120 * videoHeight), true);

//            String extStorageDirectory = Environment.getExternalStorageDirectory().toString();
            File cacheDir = new File(context.getCacheDir(), "temp_images");
            if (!cacheDir.exists())
                cacheDir.mkdir();
            file = new File(cacheDir, System.currentTimeMillis() + "_temp.png");
            if (!file.exists()) {
                try {
                    FileOutputStream outStream = new FileOutputStream(file);
                    bm.compress(Bitmap.CompressFormat.PNG, 100, outStream);
                    outStream.flush();
                    outStream.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
//
//        return file.getAbsolutePath();

        return file.getAbsolutePath();
    }

//    private void productionMethod() {
//
//        //TODO Change logo.png in drawable folder for watermark
//        BaseImageView baseImageView = mViews.get(0);
//
//        boolean rotated = baseImageView.getAngle() != 0.0;
////        boolean rotated = false;
//
//        Bitmap bm = Bitmap.createScaledBitmap(
//                rotated ? baseImageView.getmBitmap() : baseImageView.getBitmap(),
//                (int) (mViews.get(0).getViewWidth() / 630 * videoWidth), (int) (mViews.get(0).getViewHeight() / 1120 * videoHeight), true);
//
////        for(int x = 0; x<bm.getWidth(); x++){
////            for(int y = 0; y<bm.getHeight(); y++){
////                if(bm.getPixel(x, y) == Color.TRANSPARENT){
////                    bm.setPixel(x, y, Color.WHITE);
////                }
////            }
////        }
//
//        String extStorageDirectory = Environment.getExternalStorageDirectory().toString();
//        file = new File(extStorageDirectory, System.currentTimeMillis() + "_temp.png");
//        if (!file.exists()) {
//            try {
//                FileOutputStream outStream = new FileOutputStream(file);
//                bm.compress(Bitmap.CompressFormat.PNG, 100, outStream);
//                outStream.flush();
//                outStream.close();
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//
//        exeCmd = new ArrayList<>();
//
//        File output = new File(Environment.getExternalStorageDirectory()
//                + File.separator + ""
//                + System.currentTimeMillis() + ".mp4");
//        try {
//            output.createNewFile();
//
//            exeCmd.add("-y");
//            exeCmd.add("-i");
//            exeCmd.add(mInputVideoPath);
//            exeCmd.add("-i");
//            exeCmd.add(file.getAbsolutePath());
//            exeCmd.add("-filter_complex");
//            if (rotated) {
//                float angle = mViews.get(0).getAngle() * -1;
//                exeCmd.add(
//                        "[1:v] scale=iw:ih [scaled];" +
////                            "[scaled] rotate=" + mViews.get(0).getAngle() * -1 + "*PI/180:ow=rotw(iw):oh=roth(ih):c=0x00000000 [rotated];[rotated] format=yuva420p,colorchannelmixer=aa=0.5 [mix];" +
//                                "[scaled] rotate=" + angle + "*PI/180:ow=rotw(" + angle + "*PI/180):oh=roth(" + angle + "*PI/180):c=0x00000000 [rotated];[rotated] format=yuva420p,colorchannelmixer=aa=0.5 [mix];" +
//                                "[0:v][mix]"
//                                + "overlay="
//                                + ((mViews.get(0).getLeftBottomX() / 630 * videoWidth)) + "+" + mViews.get(0).getViewHeight() + "*sin(PI/180)"
//                                + ":" + (mViews.get(0).getLowestY() / 1120 * videoHeight)
//                                + ":enable='between(t," + mViews.get(0).getStartTime() / 1000 + "," + mViews.get(0).getEndTime() / 1000 + ")'");
//
//            } else {
//                exeCmd.add(
////                    "[1:v] scale=iw:ih [scaled];" +
////                            "[scaled] rotate=" + mViews.get(0).getAngle() * -1 + "*PI/180:ow=rotw(iw):oh=roth(ih):c=0x00000000 [rotated];[rotated] format=yuva420p,colorchannelmixer=aa=0.5 [mix];" +
//                        "[0:v][1:v]"
//                                + "overlay="
//                                + ((mViews.get(0).getLeftBottomX() / 630 * videoWidth))
//                                + ":" + (mViews.get(0).getLowestY() / 1120 * videoHeight)
//                                + ":enable='between(t," + mViews.get(0).getStartTime() / 1000 + "," + mViews.get(0).getEndTime() / 1000 + ")'");
//
//
//            }
//
//            exeCmd.add("-c:v");
//            exeCmd.add("libx264");
//            exeCmd.add("-preset");
//            exeCmd.add("ultrafast");
//            exeCmd.add(output.getAbsolutePath());
//
//
//            newCommand = new String[exeCmd.size()];
//            for (int j = 0; j < exeCmd.size(); j++) {
//                newCommand[j] = exeCmd.get(j);
//            }
//
////            newCommand = new String[]{"-i", videoPath, "-i", imagePath, "-preset", "ultrafast", "-filter_complex", "[1:v]scale=2*trunc(" + (width / 2) + "):2*trunc(" + (height/ 2) + ") [ovrl], [0:v][ovrl]overlay=0:0" , output.getAbsolutePath()};
//            execFFmpegBinary(newCommand, output.getAbsolutePath(), "");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    private void initVideoInfo() {
        MediaMetadataRetriever retr = new MediaMetadataRetriever();
        retr.setDataSource(mInputVideoPath);
        String width = retr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH);
        String height = retr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT);
        String rotation = retr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION);
        videoWidth = Integer.parseInt(width);
        videoHeight = Integer.parseInt(height);
        if (rotation.equals("180") && Integer.parseInt(width) > Integer.parseInt(height)) {
            videoRotation = 180;
        } else {
            videoRotation = Integer.parseInt(rotation);
        }
    }

    private void initAudioCodec() {
        audioDecoder.configure(audioFormat, null, null, 0);
        audioDecoder.start();
        MediaFormat format = MediaFormat.createAudioFormat("audio/mp4a-latm", 44100, /*channelCount*/2);
        format.setInteger(MediaFormat.KEY_BIT_RATE, 3000000);
        format.setInteger(MediaFormat.KEY_AAC_PROFILE, MediaCodecInfo.CodecProfileLevel.AACObjectLC);
        audioEncoder.configure(format, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
        audioEncoder.start();
    }


    public interface OnVideoCutFinishListener {
        void onFinish();

        void onProgress(float percent);
    }
}
