//package com.videomontage.freeeditingapps.utils
//
//import android.app.Activity
//import android.content.Context
//import android.util.Log
//import android.widget.Toast
//import com.android.billingclient.api.*
//import com.android.billingclient.api.BillingFlowParams.ProductDetailsParams
//import com.android.billingclient.api.Purchase.PurchaseState
//
//
//class BillingUtil(context: Context) {
//
//    private val purchasesUpdatedListener =
//            PurchasesUpdatedListener { billingResult, purchases ->
//                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK && purchases != null) {
//                    for (purchase in purchases) {
//                        handlePurchase(purchase)
//                    }
//                } else if (billingResult.responseCode == BillingClient.BillingResponseCode.USER_CANCELED) {
//                    // Handle an error caused by a user cancelling the purchase flow.
//                } else {
//                    // Handle any other error codes.
//                }
//            }
//
//    private var billingClient = BillingClient.newBuilder(context)
//            .setListener(purchasesUpdatedListener)
//            .enablePendingPurchases()
//            .build()
//
//    fun handlePurchase(purchase: Purchase) {
//        // Purchase retrieved from BillingClient#queryPurchasesAsync or your PurchasesUpdatedListener.
////        val purchase : Purchase = ...;
//
//        // Verify the purchase.
//        // Ensure entitlement was not already granted for this purchaseToken.
//        // Grant entitlement to the user.
//
//        if (purchase.purchaseState === PurchaseState.PURCHASED) {
//            if (!purchase.isAcknowledged) {
//                val acknowledgePurchaseParams = AcknowledgePurchaseParams.newBuilder()
//                        .setPurchaseToken(purchase.purchaseToken)
//                        .build()
//                billingClient.acknowledgePurchase(acknowledgePurchaseParams, AcknowledgePurchaseResponseListener {
//                    Log.d("ddd", "handlePurchase: ${it.responseCode}")
//
//                })
//            }
//        }
//    }
//
//    fun connectBillingClient(activity: Activity) {
//
//        billingClient.startConnection(object : BillingClientStateListener {
//            override fun onBillingSetupFinished(billingResult: BillingResult) {
//                Log.d("ddd", "onBillingSetupFinished: ${billingResult.responseCode}")
//                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
////                    Toast.makeText(activity.baseContext, "Successfully connected to Google Play", Toast.LENGTH_LONG).show()
//                    // The BillingClient is ready. You can query purchases here.
//
//                    queryProducts(activity)
//
//                } else {
//                    Toast.makeText(activity, "Connection to Google Play is not established", Toast.LENGTH_LONG).show()
//                }
//            }
//
//            override fun onBillingServiceDisconnected() {
//                Log.d("ddd", "onBillingServiceDisconnected: ")
//                // Try to restart the connection on the next request to
//                // Google Play by calling the startConnection() method.
//                Toast.makeText(activity, "Connection with Google Play is lost!", Toast.LENGTH_LONG).show()
//            }
//        })
//    }
//
//    fun queryProducts(activity: Activity) {
//        val queryProductDetailsParams =
//                QueryProductDetailsParams.newBuilder()
//                        .setProductList(
//                                (listOf(QueryProductDetailsParams.Product.newBuilder()
//                                        .setProductId("premium")
//                                        .setProductType(BillingClient.ProductType.SUBS)
//                                        .build())))
//                        .build()
//
//        billingClient.queryProductDetailsAsync(
//                queryProductDetailsParams,
//                ProductDetailsResponseListener { billingResult, productDetailsList ->
//                    Log.d("ddd", "ProductDetailsResponseListener: ${billingResult.responseCode}")
////                    makePurchase(activity, productDetailsList.get(0),
////                            productDetailsList.get(0).subscriptionOfferDetails?.get(1)?.offerToken.toString())
//                    // check billingResult
//                    // process returned productDetailsList
//                    lateinit var subscriptionGroup: SubscriptionGroup
//                    productDetailsList.forEach {
////                        Log.d("ddd", "queryProducts:" +
////                                "\nTitle: ${it.title}" +
////                                "\nName: ${it.name}" +
////                                "\nDescription: ${it.description}" +
////                                "\nProduct ID: ${it.productId}" +
////                                "\nProduct Type: ${it.productType}")
//
//                        var subscriptionPlanList = mutableListOf<SubscriptionPlan>()
//                        it.subscriptionOfferDetails?.forEach {
//
//                            var subscriptionPlan = SubscriptionPlan(
//                                    tags = it.offerTags,
//                                    token = it.offerToken)
//
//                            it.pricingPhases.pricingPhaseList.forEach {
//
//                                subscriptionPlan.billingCycleCount = it.billingCycleCount
//                                subscriptionPlan.billingPeriod = it.billingPeriod
//                                subscriptionPlan.formattedPrice = it.formattedPrice
//                                subscriptionPlan.priceAmountMicros = it.priceAmountMicros
//                                subscriptionPlan.priceCurrencyCode = it.priceCurrencyCode
//                                subscriptionPlan.recurrenceMode = it.recurrenceMode
//                                subscriptionPlanList.add(subscriptionPlan)
//
//                            }
//
//                        }
//
////                        subscriptionPlanList.forEach {
////
////                            Log.d("ddd", "queryProducts: ${it}")
////
////                        }
//
//                        subscriptionGroup = SubscriptionGroup(it, subscriptionPlanList)
//                    }
//
//                    makePurchase(activity, subscriptionGroup.productDetails, subscriptionGroup.subscriptionPlan.get(1).token)
//
//                }
//        )
//    }
//
//    fun makePurchase(activity: Activity, productDetails: ProductDetails, selectedOfferToken: String) {
//
//        if (billingClient.isFeatureSupported(BillingClient.FeatureType.SUBSCRIPTIONS).responseCode == BillingClient.BillingResponseCode.OK) {
//            val billingFlowParams = BillingFlowParams.newBuilder()
//                    .setProductDetailsParamsList(
//                            mutableListOf(
//                                    ProductDetailsParams.newBuilder() // fetched via queryProductDetailsAsync
//                                            .setProductDetails(productDetails) // to get an offer token, call ProductDetails.getOfferDetails()
//                                            // for a list of offers that are available to the user
//                                            .setOfferToken(selectedOfferToken)
//                                            .build()
//                            )
//                    )
//                    .build()
//
//            val responseCode = billingClient.launchBillingFlow(activity, billingFlowParams).responseCode
//            Log.d("ddd", "makePurchase: ${responseCode}")
//        } else {
//
//            //https://developer.android.com/google/play/billing/integrate#process-the-result
//            Toast.makeText(activity.baseContext, " Google Play Store app on your device doesn't support subscriptions.", Toast.LENGTH_LONG).show()
//        }
//
//
//    }
//
//    fun queryPurchases() {
//        billingClient.queryPurchasesAsync(
//                QueryPurchasesParams.newBuilder()
//                        .setProductType(BillingClient.ProductType.SUBS)
//                        .build(),
//                PurchasesResponseListener { billingResult, mutableList ->
//
//                    Log.d("ddd", "queryPurchases: ${mutableList}")
//                }
//        );
//    }
//
//
//    data class SubscriptionGroup(
//            var productDetails: ProductDetails,
//            var subscriptionPlan: MutableList<SubscriptionPlan>
//    )
//
//    data class SubscriptionPlan(
//            var tags: List<String> = emptyList(),
//            var token: String = "",
//            var billingCycleCount: Int = -1,
//            var billingPeriod: String = "",
//            var formattedPrice: String = "",
//            var priceAmountMicros: Long = -1000,
//            var priceCurrencyCode: String = "",
//            var recurrenceMode: Int = -1
//    )
//}