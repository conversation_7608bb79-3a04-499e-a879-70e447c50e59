package com.videomontage.freeeditingapps.utils.videoclipper

import android.content.Context
import com.videomontage.freeeditingapps.utils.VideoLayoutSize
import com.videomontage.freeeditingapps.view.BaseImageView
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream


class GifCreator(private val context: Context, private val isLayoutRotated : Boolean) : FFmpegCreator(context) {

    private var mutableMapOfGifs = mutableMapOf<Int, String>()
    private var videoLayoutSize =  VideoLayoutSize(isLayoutRotated)

    fun runInParallel(gifMap: Map<Int, BaseImageView>, videoWidth: Int, videoHeight: Int, callback: (MutableMap<Int, String>) -> Unit) {

        val key = gifMap.size - 1
        val value = gifMap.get(gifMap.size - 1)

        createGif(
                value!!.gifId,
                key,
                value,
                videoWidth,
                videoHeight,
                gifMap
        ){
            callback.invoke(mutableMapOfGifs)
        }


    }

    private fun createGif(id: Int, index: Int, baseImageView: BaseImageView, videoWidth: Int, videoHeight: Int, gifMap: Map<Int, BaseImageView>, callback : () -> Unit) {
        val inputStream = context.resources.openRawResource(id)

//            String extStorageDirectory = Environment.getExternalStorageDirectory().toString();
        val cacheDir = File(context!!.cacheDir, "temp_gifs")
        if (!cacheDir.exists()) cacheDir.mkdir()
        val inputFile = File(cacheDir, System.currentTimeMillis().toString() + "_temp.gif")

        try {
            copyInputStreamToFile(inputStream, inputFile)
        } catch (e: IOException) {
            e.printStackTrace()
        }

        val fileName = System.currentTimeMillis().toString() + ".gif"

//        val output = File(Environment.getExternalStoragePublicDirectory(
//                Environment.DIRECTORY_MOVIES).toString()
//                + File.separator + "Montage" + File.separator
//                + fileName)

        val fileDest = File(cacheDir, fileName)

//        val baseImageView = mViews[index]
        val iw = (baseImageView.viewWidth / videoLayoutSize.getWidth() * videoWidth).toInt()
        val ih = (baseImageView.viewHeight / videoLayoutSize.getHeight() * videoHeight).toInt()

        execute("-hide_banner -v warning -i ${inputFile.absolutePath} -filter_complex \"[0:v] scale=$iw:$ih:flags=lanczos,split [a][b]; [a] palettegen=reserve_transparent=on:transparency_color=ffffff [p]; [b][p] paletteuse\" $fileDest", fileDest.absolutePath, fileName) {
            println("Index : $index, path: $it")
            mutableMapOfGifs.put(index, it)

            val newIndex = index - 1
            if (newIndex >= 0){
                val value = gifMap.get(newIndex)

                createGif(
                        value!!.gifId,
                        newIndex,
                        value,
                        videoWidth,
                        videoHeight,
                        gifMap, callback
                )
            }else
                callback.invoke()



        }

    }

    @Throws(IOException::class)
    private fun copyInputStreamToFile(inputStream: InputStream, file: File) {

        // append = false
        FileOutputStream(file, false).use { outputStream ->
            var read: Int
            val bytes: ByteArray = ByteArray(8192)
            while ((inputStream.read(bytes).also { read = it }) != -1) {
                outputStream.write(bytes, 0, read)
            }
        }
    }


    fun execute(command: String, path: String, title: String, callback: (path: String) -> Unit) {
        scanGallery(false)
        execFFmpegBinary(command, path, title) {

            when (it) {
                is FFmpegCreatorResult.Success -> {

                    callback.invoke(it.path)
                }
                is FFmpegCreatorResult.Fail -> {

                }
            }


        }
    }
}