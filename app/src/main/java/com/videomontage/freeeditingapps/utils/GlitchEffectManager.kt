package com.videomontage.freeeditingapps.utils

import android.content.Context
import com.arthenica.mobileffmpeg.Config
import com.arthenica.mobileffmpeg.FFmpeg
import com.videomontage.freeeditingapps.model.ComModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Manager class for applying glitch effects to videos using FFmpeg
 */
class GlitchEffectManager(private val context: Context) {

    /**
     * Glitch effect types
     */
    enum class GlitchType {
        DIGITAL_GLITCH,          // GLITCH_FILTER_0
        CHROMATIC_ABERRATION,    // GLITCH_FILTER_1
        SCANLINE,                // G<PERSON><PERSON>CH_FILTER_2
        PIXELATION,              // GLITCH_FILTER_3
        VHS_TRACKING,            // GLITCH_FILTER_4
        DISPLACEMENT_MAP,        // GLITCH_FILTER_5
        COLOR_SHIFT,             // GLITCH_FILTER_6
        DATAMOSH,                // GLITCH_FILTER_7
        BLOCK_DISTORTION,        // GL<PERSON>CH_FILTER_8
        INTERLACING,             // G<PERSON><PERSON><PERSON>_FILTER_9
        RGB_NOISE,               // GLITCH_FILTER_10
        HORIZONTAL_WAVE,         // GLITCH_FILTER_11
        POSTERIZATION,           // GLITCH_FILTER_12
        MOSAIC,                  // GLITCH_FILTER_13
        FRAME_SKIP,              // GLITCH_FILTER_14
        DIGITAL_ARTIFACT,        // GLITCH_FILTER_15
        COMPRESSION_ARTIFACT     // GLITCH_FILTER_16
    }

    /**
     * Apply a glitch effect to a video
     *
     * @param inputPath Path to the input video
     * @param outputPath Path for the output video
     * @param glitchType Type of glitch effect to apply
     * @param onProgress Progress callback (0-100)
     * @return true if successful, false otherwise
     */
    suspend fun applyGlitchEffect(
        inputPath: String,
        outputPath: String,
        glitchType: GlitchType,
        onProgress: ((Float) -> Unit)? = null
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            // Ensure output directory exists
            val outputFile = File(outputPath)
            outputFile.parentFile?.mkdirs()

            // Get the appropriate command based on glitch type
            val effectName = when (glitchType) {
                GlitchType.DIGITAL_GLITCH -> ComModel.GLITCH_FILTER_0
                GlitchType.CHROMATIC_ABERRATION -> ComModel.GLITCH_FILTER_1
                GlitchType.SCANLINE -> ComModel.GLITCH_FILTER_2
                GlitchType.PIXELATION -> ComModel.GLITCH_FILTER_3
                GlitchType.VHS_TRACKING -> ComModel.GLITCH_FILTER_4
                GlitchType.DISPLACEMENT_MAP -> ComModel.GLITCH_FILTER_5
                GlitchType.COLOR_SHIFT -> ComModel.GLITCH_FILTER_6
                GlitchType.DATAMOSH -> ComModel.GLITCH_FILTER_7
                GlitchType.BLOCK_DISTORTION -> ComModel.GLITCH_FILTER_8
                GlitchType.INTERLACING -> ComModel.GLITCH_FILTER_9
                GlitchType.RGB_NOISE -> ComModel.GLITCH_FILTER_10
                GlitchType.HORIZONTAL_WAVE -> ComModel.GLITCH_FILTER_11
                GlitchType.POSTERIZATION -> ComModel.GLITCH_FILTER_12
                GlitchType.MOSAIC -> ComModel.GLITCH_FILTER_13
                GlitchType.FRAME_SKIP -> ComModel.GLITCH_FILTER_14
                GlitchType.DIGITAL_ARTIFACT -> ComModel.GLITCH_FILTER_15
                GlitchType.COMPRESSION_ARTIFACT -> ComModel.GLITCH_FILTER_16
            }

            // Get the FFmpeg command
            val command = ComModel.getCommand<Any>(effectName, inputPath, outputPath)

            // Set up progress monitoring
            Config.enableStatisticsCallback { statistics ->
                statistics?.let {
                    // Calculate progress based on time if duration is available
                    val progress = if (it.time > 0 && it.videoFrameNumber > 0) {
                        // Estimate progress (this is approximate)
                        val estimatedProgress = (it.time / 1000.0).toFloat() // Convert to seconds
                        onProgress?.invoke(estimatedProgress.coerceIn(0f, 100f))
                    } else {
                        onProgress?.invoke(-1f) // Indeterminate progress
                    }
                }
            }

            // Execute the command
            val rc = when (command) {
                is String -> FFmpeg.execute(command)
                is Array<*> -> FFmpeg.execute(command as Array<String>)
                else -> Config.RETURN_CODE_CANCEL
            }

            // Check if execution was successful
            rc == Config.RETURN_CODE_SUCCESS
        } catch (e: Exception) {
            e.printStackTrace()
            false
        } finally {
            // Clean up statistics callback
            Config.resetStatistics()
        }
    }

    /**
     * Apply multiple glitch effects in sequence
     *
     * @param inputPath Path to the input video
     * @param outputPath Path for the final output video
     * @param glitchTypes List of glitch effects to apply in order
     * @param onProgress Progress callback (0-100)
     * @return true if successful, false otherwise
     */
    suspend fun applyMultipleGlitchEffects(
        inputPath: String,
        outputPath: String,
        glitchTypes: List<GlitchType>,
        onProgress: ((Float) -> Unit)? = null
    ): Boolean = withContext(Dispatchers.IO) {
        if (glitchTypes.isEmpty()) return@withContext false

        try {
            var currentInput = inputPath
            val tempDir = File(context.cacheDir, "glitch_temp")
            tempDir.mkdirs()

            glitchTypes.forEachIndexed { index, glitchType ->
                val isLast = index == glitchTypes.size - 1
                val currentOutput = if (isLast) {
                    outputPath
                } else {
                    File(tempDir, "glitch_temp_$index.mp4").absolutePath
                }

                val progress = index.toFloat() / glitchTypes.size * 100f
                onProgress?.invoke(progress)

                val success = applyGlitchEffect(currentInput, currentOutput, glitchType) { 
                    // Sub-progress for individual effect
                    val totalProgress = progress + (it / glitchTypes.size)
                    onProgress?.invoke(totalProgress)
                }

                if (!success) {
                    // Clean up temp files on failure
                    tempDir.deleteRecursively()
                    return@withContext false
                }

                // Use the output as input for the next iteration
                if (!isLast) {
                    currentInput = currentOutput
                }
            }

            // Clean up temp files
            tempDir.deleteRecursively()
            onProgress?.invoke(100f)
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * Get a preview frame with glitch effect applied
     *
     * @param videoPath Path to the video
     * @param glitchType Type of glitch effect
     * @param timeInSeconds Time position to extract frame from
     * @return Path to the preview image, or null if failed
     */
    suspend fun getGlitchPreview(
        videoPath: String,
        glitchType: GlitchType,
        timeInSeconds: Double = 0.0
    ): String? = withContext(Dispatchers.IO) {
        try {
            val tempDir = File(context.cacheDir, "glitch_preview")
            tempDir.mkdirs()

            // Extract frame
            val framePath = File(tempDir, "frame_${System.currentTimeMillis()}.jpg").absolutePath
            val extractCommand = "-ss $timeInSeconds -i ${ComModel.getCharEscaptedPath(videoPath)} -vframes 1 -q:v 2 $framePath"
            
            var rc = FFmpeg.execute(extractCommand)
            if (rc != Config.RETURN_CODE_SUCCESS) return@withContext null

            // Apply glitch effect to frame
            val outputPath = File(tempDir, "glitch_${System.currentTimeMillis()}.jpg").absolutePath
            val success = applyGlitchEffect(framePath, outputPath, glitchType)

            // Clean up temporary frame
            File(framePath).delete()

            if (success) outputPath else null
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    companion object {
        /**
         * Get human-readable name for glitch type
         */
        fun getGlitchName(glitchType: GlitchType): String {
            return when (glitchType) {
                GlitchType.DIGITAL_GLITCH -> "Digital Glitch"
                GlitchType.CHROMATIC_ABERRATION -> "Chromatic Aberration"
                GlitchType.SCANLINE -> "Scanline"
                GlitchType.PIXELATION -> "Pixelation"
                GlitchType.VHS_TRACKING -> "VHS Tracking"
                GlitchType.DISPLACEMENT_MAP -> "Displacement"
                GlitchType.COLOR_SHIFT -> "Color Shift"
                GlitchType.DATAMOSH -> "Datamosh"
                GlitchType.BLOCK_DISTORTION -> "Block Distortion"
                GlitchType.INTERLACING -> "Interlacing"
                GlitchType.RGB_NOISE -> "RGB Noise"
                GlitchType.HORIZONTAL_WAVE -> "Wave Distortion"
                GlitchType.POSTERIZATION -> "Posterization"
                GlitchType.MOSAIC -> "Mosaic"
                GlitchType.FRAME_SKIP -> "Frame Skip"
                GlitchType.DIGITAL_ARTIFACT -> "Digital Artifact"
                GlitchType.COMPRESSION_ARTIFACT -> "Compression"
            }
        }

        /**
         * Get description for glitch type
         */
        fun getGlitchDescription(glitchType: GlitchType): String {
            return when (glitchType) {
                GlitchType.DIGITAL_GLITCH -> "RGB channel shifting for digital corruption effect"
                GlitchType.CHROMATIC_ABERRATION -> "Simulates lens chromatic aberration"
                GlitchType.SCANLINE -> "Old CRT monitor scanline effect"
                GlitchType.PIXELATION -> "Low resolution pixelated look"
                GlitchType.VHS_TRACKING -> "VHS tape tracking error simulation"
                GlitchType.DISPLACEMENT_MAP -> "Warps video using displacement mapping"
                GlitchType.COLOR_SHIFT -> "Shifts color channels independently"
                GlitchType.DATAMOSH -> "Frame blending datamosh effect"
                GlitchType.BLOCK_DISTORTION -> "Divides and distorts video blocks"
                GlitchType.INTERLACING -> "Simulates interlaced video artifacts"
                GlitchType.RGB_NOISE -> "Adds colored noise with channel shifts"
                GlitchType.HORIZONTAL_WAVE -> "Horizontal wave displacement"
                GlitchType.POSTERIZATION -> "Reduces color levels for poster effect"
                GlitchType.MOSAIC -> "Large pixel mosaic with color shift"
                GlitchType.FRAME_SKIP -> "Drops frames for stuttering effect"
                GlitchType.DIGITAL_ARTIFACT -> "Edge detection digital artifacts"
                GlitchType.COMPRESSION_ARTIFACT -> "Heavy compression artifacts"
            }
        }
    }
}