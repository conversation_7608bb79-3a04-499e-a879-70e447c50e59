package com.videomontage.freeeditingapps.utils

import android.content.Context
import android.content.SharedPreferences
import com.videomontage.freeeditingapps.application.MyApplication

class SharedPrefs private constructor() {
    val mSharedPreferences: SharedPreferences

    init {
        mSharedPreferences =
            MyApplication.self().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    operator fun <T> get(key: String?, anonymousClass: Class<T>): T? {
        if (anonymousClass == String::class.java) {
            return mSharedPreferences.getString(key, "") as T?
        } else if (anonymousClass == Boolean::class.java) {
            return java.lang.Boolean.valueOf(mSharedPreferences.getBoolean(key, false)) as T
        } else if (anonymousClass == Float::class.java) {
            return java.lang.Float.valueOf(mSharedPreferences.getFloat(key, 0f)) as T
        } else if (anonymousClass == Int::class.java) {
            return Integer.valueOf(mSharedPreferences.getInt(key, 0)) as T
        } else if (anonymousClass == Long::class.java) {
            return java.lang.Long.valueOf(mSharedPreferences.getLong(key, 0)) as T
        }
        return null
    }

    operator fun <T> get(key: String?, anonymousClass: Class<T>, defaultValue: T): T {
        return when (anonymousClass) {
            String::class.java -> mSharedPreferences.getString(key, defaultValue as? String ?: "") as T
            Boolean::class.java -> mSharedPreferences.getBoolean(key, defaultValue as? Boolean ?: false) as T
            Float::class.java -> mSharedPreferences.getFloat(key, defaultValue as? Float ?: 0f) as T
            Int::class.java -> mSharedPreferences.getInt(key, defaultValue as? Int ?: 0) as T
            Long::class.java -> mSharedPreferences.getLong(key, defaultValue as? Long ?: 0L) as T
            else -> defaultValue
        }
    }


//    operator fun <T> get(key: String?, anonymousClass: Class<T>, defaultValue: T): T? {
//        return if (anonymousClass == String::class.java) {
//            mSharedPreferences.getString(key, defaultValue as String) as T?
//        } else if (anonymousClass == Boolean::class.java) {
//            java.lang.Boolean.valueOf(
//                mSharedPreferences.getBoolean(
//                    key,
//                    (defaultValue as Boolean)
//                )
//            ) as T
//        } else if (anonymousClass == Float::class.java) {
//            java.lang.Float.valueOf(
//                mSharedPreferences.getFloat(
//                    key,
//                    (defaultValue as Float)
//                )
//            ) as T
//        } else if (anonymousClass == Int::class.java) {
//            Integer.valueOf(
//                mSharedPreferences.getInt(
//                    key,
//                    (defaultValue as Int)
//                )
//            ) as T
//        } else if (anonymousClass == Long::class.java) {
//            java.lang.Long.valueOf(
//                mSharedPreferences.getLong(
//                    key,
//                    (defaultValue as Long)
//                )
//            ) as T
//        } else {
//            null
//        }
//    }

    fun <T> put(key: String?, data: T) {
        val editor = mSharedPreferences.edit()
        if (data is String) {
            editor.putString(key, data as String)
        } else if (data is Boolean) {
            editor.putBoolean(key, (data as Boolean))
        } else if (data is Float) {
            editor.putFloat(key, (data as Float))
        } else if (data is Int) {
            editor.putInt(key, (data as Int))
        } else if (data is Long) {
            editor.putLong(key, (data as Long))
        }
        editor.apply()
    }

    fun remove(key: String?) {
        val editor = mSharedPreferences.edit()
        editor.remove(key)
        editor.apply()
    }

    fun clear() {
        mSharedPreferences.edit().clear().apply()
    }

    companion object {
        private const val PREFS_NAME = "share_prefs"
        private var mInstance: SharedPrefs? = null
        @JvmStatic
        val instance: SharedPrefs?
            get() {
                if (mInstance == null) {
                    mInstance = SharedPrefs()
                }
                return mInstance
            }
    }
}