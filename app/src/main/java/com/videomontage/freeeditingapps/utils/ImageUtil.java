package com.videomontage.freeeditingapps.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.PaintFlagsDrawFilter;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.graphics.RectF;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.renderscript.Allocation;
import android.renderscript.Element;
import android.renderscript.RenderScript;
import android.renderscript.ScriptIntrinsicBlur;

import com.videomontage.freeeditingapps.R;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;

public class ImageUtil {

    public static int BITMAP_LARGE_SIZE = 1000;


    public static Bitmap getResizedBitmap(Bitmap image, int maxSize) {
        int width = image.getWidth();
        int height = image.getHeight();

        float bitmapRatio = (float) width / (float) height;
        if (bitmapRatio > 0) {
            width = maxSize;
            height = (int) (width / bitmapRatio);
        } else {
            height = maxSize;
            width = (int) (height * bitmapRatio);
        }
        return Bitmap.createScaledBitmap(image, width, height, true);
    }

    @SuppressLint("NewApi")
    public static Bitmap blur(Bitmap image, float BLUR_RADIUS, Context con) {
        if (null == image) return null;

        Bitmap outputBitmap = Bitmap.createBitmap(image);
        final RenderScript renderScript = RenderScript.create(con);
        Allocation tmpIn = Allocation.createFromBitmap(renderScript, image);
        Allocation tmpOut = Allocation.createFromBitmap(renderScript, outputBitmap);

        //Intrinsic Gausian blur filter
        ScriptIntrinsicBlur theIntrinsic = ScriptIntrinsicBlur.create(renderScript, Element.U8_4(renderScript));
        theIntrinsic.setRadius(BLUR_RADIUS);
        theIntrinsic.setInput(tmpIn);
        theIntrinsic.forEach(tmpOut);
        tmpOut.copyTo(outputBitmap);
        return outputBitmap;
    }

    public static Bitmap getSketchFromBH(Bitmap scaleBitmap, Bitmap a, Context d) {

        int colorPencilValue = 2;
        int colorPencilValue2 = 80;

        Bitmap a2 = getThresholdBitmap(a, colorPencilValue2);
        MakeBitmapForMerge(d, a2, getFSHbitmap(a2, R.drawable.sketch_6, R.drawable.sketch_6), PorterDuff.Mode.SCREEN);
        Bitmap bitmap3 = getThresholdBitmap(a, 120);
        MakeBitmapForMerge(d, bitmap3, getFSHbitmap(bitmap3, R.drawable.sketch_5, R.drawable.sketch_5), PorterDuff.Mode.SCREEN);
        mergeBitmapWithPorterMode(a2, bitmap3, PorterDuff.Mode.DARKEN);
        if (!(a2 == null || a2.isRecycled())) {
//			a2.recycle();
            System.gc();
        }
        Bitmap bitmap4 = getThresholdBitmap(a, 40);
        MakeBitmapForMerge(d, bitmap4, getFSHbitmap(bitmap4, R.drawable.sketch_1, R.drawable.sketch_1), PorterDuff.Mode.SCREEN);
        DrawRectToBitmap(bitmap4, 50.0f);
        mergeBitmapWithPorterMode(bitmap3, bitmap4, PorterDuff.Mode.MULTIPLY);
        if (!(bitmap3 == null || bitmap3.isRecycled())) {
            System.gc();
        }

        Bitmap bitmap;

        bitmap = new ColorLevels().getColorLevelBitmap(a2);
        mergeBitmapWithPorterMode(bitmap, bitmap4, PorterDuff.Mode.MULTIPLY);
        if (!(bitmap == null || bitmap.isRecycled())) {
            System.gc();
        }
        bitmap = getFilterBitmapMine(a, 90);
        mergeBitmapWithPorterMode(bitmap4, bitmap, PorterDuff.Mode.SCREEN);
        if (!(bitmap4 == null || bitmap4.isRecycled())) {
            System.gc();
        }
        System.gc();
        return bitmap;
    }

    public static float setScaleOrColorValue(int i) {
        if (i <= 0) {
            return 2.0f;
        }
        if (i > 0 && i <= 20) {
            return 2.4f;
        }
        if (i > 20 && i <= 50) {
            return 2.8f;
        }
        if (i <= 50 || i > 80) {
            return 4.0f;
        }
        return 3.4f;
    }

    public static Bitmap getFilterBitmapMine(Bitmap bitmap, int i) {
        float a = (float) (((((double) i) + 100.0d) / 200.0d) * ((double) setScaleOrColorValue(i)));
        Bitmap createBitmap = Bitmap.createBitmap(bitmap.getWidth(), bitmap.getHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(createBitmap);
        Paint paint = new Paint();
        paint.setAntiAlias(true);
        paint.setFilterBitmap(true);
        ColorMatrix colorMatrix = new ColorMatrix();
        colorMatrix.reset();
        colorMatrix.setSaturation(0);
        paint.setColorFilter(new ColorMatrixColorFilter(colorMatrix));
        canvas.drawBitmap(bitmap, 0.0f, 0.0f, paint);
        return createBitmap;
    }


    public static void DrawRectToBitmap(Bitmap bitmap, float f) {
        Canvas canvas = new Canvas();
        canvas.setBitmap(bitmap);
        Paint paint = new Paint();
        paint.setAntiAlias(true);
        paint.setColor(Color.argb((int) ((((double) f) / 100.0d) * 255.0d), 255, 255, 255));
        canvas.drawRect(new Rect(0, 0, bitmap.getWidth(), bitmap.getHeight()), paint);
    }

    public static int getFSHbitmap(Bitmap bitmap, int i, int i2) {
        return Math.min(bitmap.getWidth(), bitmap.getHeight()) > 480 ? i2 : i;
    }

    public static final Bitmap getThresholdBitmap(Bitmap bitmap, int thresoldValue) {
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();
        int[] mArray = new int[(width * height)];
        bitmap.getPixels(mArray, 0, width, 0, 0, width, height);
        for (int i = 0; i < width; i++) {
            for (int i2 = 0; i2 < height; i2++) {
                int i3 = mArray[(i2 * width) + i];
                if (((((i3 & 255) * 77) + ((((16711680 & i3) >> 16) * 28) + (((65280 & i3) >> 8) * 151))) >> 8) > thresoldValue) {
                    i3 = 255;
                } else {
                    i3 = 0;
                }
                mArray[(i2 * width) + i] = Color.rgb(i3, i3, i3);
            }
        }
        Bitmap createBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        createBitmap.setPixels(mArray, 0, width, 0, 0, width, height);
        System.gc();
        return createBitmap;
    }


    public static void MakeBitmapForMerge(Context context, Bitmap bitmap, int i, PorterDuff.Mode mode) {
        Bitmap createBitmap;
        Bitmap createBitmap2;
        Throwable th;
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inPreferredConfig = Bitmap.Config.RGB_565;
        InputStream openRawResource = context.getResources().openRawResource(i);
        Bitmap decodeStream = BitmapFactory.decodeStream(openRawResource, null, options);
        try {
            openRawResource.close();
        } catch (IOException e3) {
            e3.printStackTrace();
        }
        Rect rect = new Rect(0, 0, bitmap.getWidth(), bitmap.getHeight());
        if (bitmap.getWidth() > bitmap.getHeight()) {
            Matrix matrix = new Matrix();
            matrix.postRotate(90.0f);
            createBitmap = Bitmap.createBitmap(decodeStream, 0, 0, decodeStream.getWidth(), decodeStream.getHeight(), matrix, true);
        } else {
            createBitmap = decodeStream;
        }
        try {
            int width = bitmap.getWidth();
            int height = bitmap.getHeight();
            int width2 = createBitmap.getWidth();
            int height2 = createBitmap.getHeight();
            float max = Math.max(((float) width) / ((float) width2), ((float) height) / ((float) height2));
            float f = ((float) width2) * max;
            float f2 = ((float) height2) * max;
            max = (((float) width) - f) / 2.0f;
            float f3 = (((float) height) - f2) / 2.0f;
            RectF rectF = new RectF(max, f3, f + max, f2 + f3);
            createBitmap2 = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565);
            Canvas canvas = new Canvas(createBitmap2);
            canvas.setDrawFilter(new PaintFlagsDrawFilter(0, 3));
            canvas.drawBitmap(createBitmap, null, rectF, null);
//			createBitmap.recycle();
            try {
                rect = new Rect(0, 0, createBitmap2.getWidth(), createBitmap2.getHeight());
                Canvas canvas2 = new Canvas();
                canvas2.setBitmap(bitmap);
                Paint paint = new Paint();
                paint.setXfermode(new PorterDuffXfermode(mode));
                paint.setAntiAlias(true);
                paint.setFilterBitmap(true);
                canvas2.drawBitmap(createBitmap2, rect, rect, paint);
                System.gc();
            } catch (NullPointerException e) {
                System.gc();
            } catch (Exception e5) {
                createBitmap = createBitmap2;
                e5.printStackTrace();
                System.gc();
            } catch (Throwable th3) {
                th = th3;
                createBitmap2 = createBitmap;
                System.gc();
            }
        } catch (NullPointerException e2) {
            createBitmap2 = createBitmap;
            if (!(createBitmap2 == null || createBitmap2.isRecycled())) {
//				createBitmap2.recycle();
            }
            System.gc();
        } catch (Exception e7) {
            e7.printStackTrace();
            if (!(createBitmap == null || createBitmap.isRecycled())) {
//				createBitmap.recycle();
            }
            System.gc();
        }
    }

    public static Bitmap mergeBitmapWithPorterMode(Bitmap bitmap, Bitmap bitmap2, PorterDuff.Mode mode) {
        Canvas canvas = new Canvas();
        canvas.setBitmap(bitmap2);
        Paint paint = new Paint();
        paint.setXfermode(new PorterDuffXfermode(mode));
        paint.setAntiAlias(true);
        paint.setFilterBitmap(true);
        canvas.drawBitmap(bitmap, new Rect(0, 0, bitmap.getWidth(), bitmap.getHeight()), new Rect(0, 0, bitmap2.getWidth(), bitmap2.getHeight()), paint);
        return bitmap2;
    }

    public static void refreshMediaScanner(Context con, File file) {
        MediaScannerConnection.scanFile(con, new String[]{file.getPath()}, null,
                new MediaScannerConnection.OnScanCompletedListener() {
                    @Override
                    public void onScanCompleted(String path, Uri uri) {

                    }
                });
    }
}
