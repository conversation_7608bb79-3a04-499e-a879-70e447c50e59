package com.videomontage.freeeditingapps.utils

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.FragmentManager
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.activity.MainActivity
import com.videomontage.freeeditingapps.activity.VideoEditActivity
import com.videomontage.freeeditingapps.fragment.*
import com.videomontage.freeeditingapps.statistic.Statistic

object SelectedFragmentHelper {

    var FRAGMENT_INDEX = -1

    private fun processWithFragment(absFragment: AbsFragment, fragment: FragmentManager) {

        fragment.beginTransaction()
                .add(R.id.view_container, absFragment)
                .addToBackStack(null)
                .commit()
    }

    fun addFragment(action: Int,  fragment: FragmentManager, activity: Activity) {
        var absFragment: AbsFragment? = null
        val bundle = Bundle()
        bundle.putInt(Statistic.ACTION, action)
//        bundle.putString(Statistic.PATH_VIDEO, videoModelList.get(indexVideo).getPath())
        bundle.putParcelable(Statistic.VIDEO_MODEL, Statistic.SELECTED_VIDEO_MODEL)
        bundle.putString(Statistic.PATH_VIDEO, Statistic.SELECTED_VIDEO_MODEL.path)


        //here should be video model as argument
//        bundle.putParcelable(Statistic.VIDEO_MODEL, parcel)
        when (action) {
            MainActivity.INDEX_CUTTER -> {
                absFragment = CutterFragment.newInstance(bundle)
//                processWithFragment(absFragment, fragment)
            }
            MainActivity.INDEX_ADD_MUSIC -> {
                absFragment = AddMusicFragment.newInstance(bundle)
//                processWithFragment(absFragment)
            }
            MainActivity.INDEX_MERGER -> {
                absFragment = MergerFragment.newInstance(bundle)
//                processWithFragment(absFragment)
            }
            MainActivity.INDEX_SPEED -> {
                absFragment = SpeedFragment.newInstance(bundle)
//                processWithFragment(absFragment)
            }
            MainActivity.INDEX_EFFECTS -> {
                //                absFragment = EffectsFragment.newInstance(bundle);
                absFragment = MoreOptionsFragment.newInstance(bundle)
//                processWithFragment(absFragment)
            }
            MainActivity.INDEX_EDIT_ACTIVITY -> {
                val intent = Intent(activity, VideoEditActivity::class.java)
                //                intent.putExtra(StaticFinalValues.VIDEOFILEPATH, "/storage/emulated/0/aserbaoCamera/1604613729871.mp4");
                intent.putExtra(StaticFinalValues.VIDEOFILEPATH, "")
                activity.startActivity(intent)
            }


        }

        absFragment?.let { processWithFragment(it, fragment) }

    }

}