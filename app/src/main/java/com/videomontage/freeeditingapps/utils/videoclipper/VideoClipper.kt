package com.videomontage.freeeditingapps.utils.videoclipper

import android.content.Context
import android.content.res.Resources
import android.util.Log
import com.videomontage.freeeditingapps.utils.metadata.VideoMetadata
import com.videomontage.freeeditingapps.view.BaseImageView
import java.io.IOException
import java.util.*

class VideoClipper(
        val context: Context?
) {

    private val TAG = "VideoClipper"

    private var mInputVideoPath: String? = null
    private var mOutputVideoPath: String? = null

    private var videoWidth = 0
    private var videoHeight = 0
    private var videoRotation = 0
    var lock = Any()
    private var listener: OnVideoCutFinishListener? = null
    private var gifsPath = mutableMapOf<Int, String>()


//    private fun execFFmpegBinary(command: Any, path: String, title: String) {
//        if (ComModel.isNewApiRequired()) {
//            val myCommand = command as String
//            Log.d(TAG, "execFFmpegBinary: $myCommand")
//            val executionId = FFmpeg.executeAsync(myCommand) { executionId, rc ->
//                if (rc == Config.RETURN_CODE_SUCCESS) {
//                    Log.i(Config.TAG, "Command execution completed successfully.")
//                    val file = File(Environment.getExternalStorageDirectory().toString() + File.separator + "/Movies/Montage/" + title)
//                    MediaScannerConnection.scanFile(context, arrayOf(file.absolutePath), arrayOf("video/mp4"), null)
//                    listener!!.onFinish()
//                } else if (rc == Config.RETURN_CODE_CANCEL) {
//                    Log.i(Config.TAG, "Command execution cancelled by user.")
//                    Toast.makeText(context, "FAILURE", Toast.LENGTH_SHORT).show()
//
//                    //                        afterFFmpegFailure();
//                } else {
//                    Log.i(Config.TAG, String.format("Command execution failed with rc=%d and the output below.", rc))
//                    Config.printLastCommandOutput(Log.INFO)
//                    Toast.makeText(context, "FAILURE", Toast.LENGTH_SHORT).show()
//
//                    //                        afterFFmpegFailure();
//                }
//            }
//            Config.enableLogCallback {
//                //                    int durationFile = (int) Utils.getProgress(message.getText(), Long.parseLong(videoModel.getDuration()) / 1000);
////                    float percent = durationFile / (Float.parseFloat(videoModel.getDuration()) / 1000);
////                    if (progressDialog != null) {
////                        progressDialog.setProgress((int) (percent * 100));
////                    }
//            }
//        }
//    }


    fun setInputVideoPath(inputPath: String?) {
        mInputVideoPath = inputPath
        initVideoInfo()

    }

    fun setOutputVideoPath(outputPath: String?) {
        mOutputVideoPath = outputPath
    }


    fun setOnVideoCutFinishListener(listener: OnVideoCutFinishListener?) {
        this.listener = listener
    }


    private var mViews = ArrayList<BaseImageView>()
    private var mResources: Resources? = null

    @Throws(IOException::class)
    fun clipVideo(isLayoutRotated: Boolean, views: ArrayList<BaseImageView>, resources: Resources?) {
        mViews = views
        mResources = resources
        val list = views.filter { it.isGif }
        val gifs = list.associateBy({ list.indexOf(it) }, { it })
        val gifCreator = GifCreator(context!!, isLayoutRotated)
        val videoCreator = VideoCreator(context, isLayoutRotated)
        Log.d(TAG, "clipVideo: videoWidth $videoWidth")
        Log.d(TAG, "clipVideo: videoHeight $videoHeight")
        Log.d(TAG, "clipVideo: rotation ${videoRotation}")
        if (list.size > 0) {
            gifCreator.runInParallel(
                    gifs,
                    videoWidth,
                    videoHeight
            ) {

                gifsPath = it
//            debugMethod()
                videoCreator.debugMethod(
//                        isLayoutRotated,
                        mInputVideoPath!!,
                        mViews,
                        gifsPath,
                        videoWidth,
                        videoHeight
                ) {
                    listener?.onFinish()

                }
                println("Generating gifs is done")
            }
        } else {
            videoCreator.debugMethod(
//                    isLayoutRotated,
                    mInputVideoPath!!,
                    mViews,
                    gifsPath,
                    videoWidth,
                    videoHeight
            ){
                listener?.onFinish()

            }
        }


    }

//    private fun debugMethod() {
//
//
//        val sbDestinationCommand = StringBuilder()
//        val sbFilterComplexCommand: StringBuilder
//        val sbInputCommand = StringBuilder()
//        sbInputCommand.append("-y ")
//        sbInputCommand.append("-i ")
//        sbInputCommand.append(mInputVideoPath)
//        for (i in mViews.indices) {
//            sbInputCommand.append(" -i ")
////            sbInputCommand.append(getOverlayPath(i, mViews[i].isGif))
//            var overlayPath = if (mViews[i].isGif) gifsPath.get(i) else getOverlayPath(i, mViews[i].isGif)
//            sbInputCommand.append(overlayPath)
//        }
//        sbFilterComplexCommand = getFilterComplexCommand()
//
////        if (true)return;
//        sbDestinationCommand.append("\" -c:v")
//        sbDestinationCommand.append(" libx264")
//        sbDestinationCommand.append(" -preset")
//        sbDestinationCommand.append(" ultrafast ")
//        val fileName = System.currentTimeMillis().toString() + ".mp4"
//        val output = File(Environment.getExternalStoragePublicDirectory(
//                Environment.DIRECTORY_MOVIES).toString()
//                + File.separator + "Montage" + File.separator
//                + fileName)
//        try {
//            output.createNewFile()
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
//        sbDestinationCommand.append(output.absolutePath)
//        execFFmpegBinary(sbInputCommand.toString() + sbFilterComplexCommand.toString() + sbDestinationCommand.toString(), output.absolutePath, fileName)
//    }
//
//    private fun getFilterComplexCommand(): StringBuilder {
//        val sb = StringBuilder()
//        sb.append(" -filter_complex \"")
//        for (i in mViews.indices) {
//            val baseImageView = mViews[i]
//            val rotated = baseImageView.rotateDegree.toDouble() != 0.0
//            val inputPos = i + 1
//            val filterSeparator = if (inputPos < mViews.size) "[vid" + (inputPos + 1) + "];" else ""
//            println("Rotated: $rotated")
//
//            //koristiti ovo kad je gif u pitanju sto se skejlovanja tice
//            val iw = (baseImageView.viewWidth / 630 * videoWidth).toInt()
//            val ih = (baseImageView.viewHeight / 1120 * videoHeight).toInt()
//            if (rotated) {
//                val angle = baseImageView.angle * -1
//                val main = if (i == 0) "[0:v]" else "[vid$inputPos]"
//                val scale = if (baseImageView.isGif) "scale=$iw:$ih" else "scale=iw:ih"
//                sb.append( //                        "[" + inputPos + ":v] scale=iw:ih [scaled" + inputPos + "];" +
//                        "[" + inputPos + ":v]" + scale + "[scaled" + inputPos + "];" +
//                                "[scaled" + inputPos + "] rotate=" + angle + "*PI/180:ow=rotw(" + angle + "*PI/180):oh=roth(" + angle + "*PI/180):c=0x00000000 [rotated" + inputPos + "];[rotated" + inputPos + "] format=yuva420p,colorchannelmixer=aa=1.0 [mix" + inputPos + "];" +
//                                main + "[mix" + inputPos + "]"
//                                + "overlay="
//                                + baseImageView.leftBottomX / 630 * videoWidth + "+" + baseImageView.viewHeight + "*sin(PI/180)"
//                                + ":" + baseImageView.lowestY / 1120 * videoHeight
//                                + ":enable='between(t," + baseImageView.startTime / 1000 + "," + baseImageView.endTime / 1000 + ")'" + filterSeparator)
//            } else {
//                if (i == 0) sb.append("[0:v]") else sb.append("[vid$inputPos]")
//                sb.append(
//                        ("[" + inputPos + ":v]"
//                                + "overlay="
//                                + ((baseImageView.leftBottomX / 630 * videoWidth))
//                                + ":" + (baseImageView.lowestY / 1120 * videoHeight)
//                                + ":enable='between(t," + (baseImageView.startTime / 1000) + "," + (baseImageView.endTime / 1000) + ")'" + filterSeparator))
//            }
//        }
//        return sb
//    }
//
//    @Throws(IOException::class)
//    private fun copyInputStreamToFile(inputStream: InputStream, file: File) {
//
//        // append = false
//        FileOutputStream(file, false).use { outputStream ->
//            var read: Int
//            val bytes: ByteArray = ByteArray(8192)
//            while ((inputStream.read(bytes).also { read = it }) != -1) {
//                outputStream.write(bytes, 0, read)
//            }
//        }
//    }

//    private fun createGif(file: File, index: Int) {
//        val fileName = System.currentTimeMillis().toString() + ".gif"
//
////        val output = File(Environment.getExternalStoragePublicDirectory(
////                Environment.DIRECTORY_MOVIES).toString()
////                + File.separator + "Montage" + File.separator
////                + fileName)
//
//        val cacheDir = File(context!!.cacheDir, "temp_gifs")
//        if (!cacheDir.exists()) cacheDir.mkdir()
//        val fileDest = File(cacheDir, fileName)
//
//        val baseImageView = mViews[index]
//        val iw = (baseImageView.viewWidth / 630 * videoWidth).toInt()
//        val ih = (baseImageView.viewHeight / 1120 * videoHeight).toInt()
//
//        val gifCreator = GifCreator(context!!)
//        gifCreator.execute("-hide_banner -v warning -i ${file.absolutePath} -filter_complex \"[0:v] scale=$iw:$ih:flags=lanczos,split [a][b]; [a] palettegen=reserve_transparent=on:transparency_color=ffffff [p]; [b][p] paletteuse\" $fileDest", fileDest.absolutePath, fileName) {
//            gifsPath.put(index, it)
//
//        }
//    }

//    private fun getOverlayPath(index: Int, isGif: Boolean): String? {
//        val file: File
//        if (isGif) {
//            val inputStream = context!!.resources.openRawResource(R.raw.dengliao)
//
////            String extStorageDirectory = Environment.getExternalStorageDirectory().toString();
//            val cacheDir = File(context!!.cacheDir, "temp_images")
//            if (!cacheDir.exists()) cacheDir.mkdir()
//            file = File(cacheDir, System.currentTimeMillis().toString() + "_temp.gif")
//            createGif(file, index)
//
//            try {
//                copyInputStreamToFile(inputStream, file)
//            } catch (e: IOException) {
//                e.printStackTrace()
//            }
//        } else {
//            val baseImageView = mViews[index]
//            val rotated = baseImageView.angle.toDouble() != 0.0
//            val bm = Bitmap.createScaledBitmap(
//                    if (rotated) baseImageView.getmBitmap() else baseImageView.bitmap,
//                    (baseImageView.viewWidth / 630 * videoWidth).toInt(), (baseImageView.viewHeight / 1120 * videoHeight).toInt(), true)
//
////            String extStorageDirectory = Environment.getExternalStorageDirectory().toString();
//            val cacheDir = File(context!!.cacheDir, "temp_images")
//            if (!cacheDir.exists()) cacheDir.mkdir()
//            file = File(cacheDir, System.currentTimeMillis().toString() + "_temp.png")
//            if (!file.exists()) {
//                try {
//                    val outStream = FileOutputStream(file)
//                    bm.compress(Bitmap.CompressFormat.PNG, 100, outStream)
//                    outStream.flush()
//                    outStream.close()
//                } catch (e: Exception) {
//                    e.printStackTrace()
//                }
//            }
//        }
//        //
////        return file.getAbsolutePath();
//        return file.absolutePath
//    }

    private fun initVideoInfo() {
        val videoMetadata = mInputVideoPath?.let { VideoMetadata.initVideoInfo(it) }
        videoRotation = videoMetadata!![VideoMetadata.VIDEO_ROTATION]!!
        videoWidth = videoMetadata[VideoMetadata.VIDEO_WIDTH]!!
        videoHeight = videoMetadata[VideoMetadata.VIDEO_HEIGHT]!!

//        val retr = MediaMetadataRetriever()
//        retr.setDataSource(mInputVideoPath)
//        val width = retr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)
//        val height = retr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)
//        val rotation = retr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION)
//        videoWidth = width!!.toInt()
//        videoHeight = height!!.toInt()
//        if ((rotation == "180") && width.toInt() > height.toInt()) {
//            videoRotation = 180
//        } else {
//            videoRotation = rotation!!.toInt()
//        }
    }


    interface OnVideoCutFinishListener {
        fun onFinish()
        fun onProgress(percent: Float)
    }
}