package com.videomontage.freeeditingapps.utils

import android.annotation.SuppressLint
import android.content.ContentUris
import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.DocumentsContract
import android.provider.MediaStore
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream

/**
 * Utility class for converting content:// URIs to file paths for FFmpeg processing
 * Adapted from createphoto module's URIToPath.java with modern Android support
 */
object UriToPathUtil {

    /**
     * Get a file path from a Uri. This will get the path for Storage Access
     * Framework Documents, as well as the _data field for the MediaStore and
     * other file-based ContentProviders.
     *
     * @param context The context.
     * @param uri The Uri to query.
     * @return File path or null if unable to resolve
     */
    @SuppressLint("NewApi")
    fun getPath(context: Context, uri: Uri): String? {
        Timber.tag("DEBUG_FLOW").d("UriToPathUtil: Converting URI to path: $uri")
        
        val isKitKat = Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT

        // DocumentProvider
        if (isKitKat && DocumentsContract.isDocumentUri(context, uri)) {
            // ExternalStorageProvider
            if (isExternalStorageDocument(uri)) {
                val docId = DocumentsContract.getDocumentId(uri)
                val split = docId.split(":")
                val type = split[0]

                if ("primary".equals(type, ignoreCase = true)) {
                    val path = Environment.getExternalStorageDirectory().toString() + "/" + split[1]
                    Timber.tag("DEBUG_FLOW").d("UriToPathUtil: External storage path: $path")
                    return path
                }
            }
            // DownloadsProvider
            else if (isDownloadsDocument(uri)) {
                val id = DocumentsContract.getDocumentId(uri)
                val contentUri = ContentUris.withAppendedId(
                    Uri.parse("content://downloads/public_downloads"), 
                    id.toLongOrNull() ?: 0L
                )
                val path = getDataColumn(context, contentUri, null, null)
                Timber.tag("DEBUG_FLOW").d("UriToPathUtil: Downloads path: $path")
                return path
            }
            // MediaProvider
            else if (isMediaDocument(uri)) {
                val docId = DocumentsContract.getDocumentId(uri)
                val split = docId.split(":")
                val type = split[0]

                val contentUri = when (type) {
                    "image" -> MediaStore.Images.Media.EXTERNAL_CONTENT_URI
                    "video" -> MediaStore.Video.Media.EXTERNAL_CONTENT_URI
                    "audio" -> MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
                    else -> null
                }

                if (contentUri != null) {
                    val selection = "_id=?"
                    val selectionArgs = arrayOf(split[1])
                    val path = getDataColumn(context, contentUri, selection, selectionArgs)
                    Timber.tag("DEBUG_FLOW").d("UriToPathUtil: Media provider path: $path")
                    return path
                }
            }
        }
        // MediaStore (and general)
        else if ("content".equals(uri.scheme, ignoreCase = true)) {
            val path = getDataColumn(context, uri, null, null)
            Timber.tag("DEBUG_FLOW").d("UriToPathUtil: Content URI path: $path")
            return path
        }
        // File
        else if ("file".equals(uri.scheme, ignoreCase = true)) {
            val path = uri.path
            Timber.tag("DEBUG_FLOW").d("UriToPathUtil: File URI path: $path")
            return path
        }

        Timber.tag("DEBUG_FLOW").w("UriToPathUtil: Unable to resolve URI to path: $uri")
        return null
    }

    /**
     * Get a file path that FFmpeg can use. If direct path resolution fails,
     * copies the content to a temporary file.
     *
     * @param context The context
     * @param uri The content URI
     * @param fileName Optional filename for temporary file
     * @return Usable file path for FFmpeg or null if failed
     */
    fun getFFmpegCompatiblePath(context: Context, uri: Uri, fileName: String? = null): String? {
        // First try direct path resolution
        val directPath = getPath(context, uri)
        if (directPath != null && File(directPath).exists()) {
            Timber.tag("DEBUG_FLOW").d("UriToPathUtil: Using direct path: $directPath")
            return directPath
        }

        // If direct path fails, copy to temporary file
        Timber.tag("DEBUG_FLOW").d("UriToPathUtil: Direct path failed, copying to temp file")
        return copyUriToTempFile(context, uri, fileName)
    }

    /**
     * Copy content from URI to a temporary file for FFmpeg processing
     *
     * @param context The context
     * @param uri The content URI
     * @param fileName Optional filename
     * @return Path to temporary file or null if failed
     */
    private fun copyUriToTempFile(context: Context, uri: Uri, fileName: String?): String? {
        return try {
            val contentResolver = context.contentResolver
            val inputStream: InputStream = contentResolver.openInputStream(uri) ?: return null
            
            // Generate filename if not provided
            val tempFileName = fileName ?: "temp_video_${System.currentTimeMillis()}.mp4"
            val tempFile = File(context.cacheDir, "video_processing/$tempFileName")
            
            // Create parent directory if needed
            tempFile.parentFile?.mkdirs()
            
            // Copy content to temp file
            val outputStream = FileOutputStream(tempFile)
            inputStream.use { input ->
                outputStream.use { output ->
                    input.copyTo(output)
                }
            }
            
            val path = tempFile.absolutePath
            Timber.tag("DEBUG_FLOW").d("UriToPathUtil: Created temp file: $path")
            path
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e(e, "UriToPathUtil: Failed to copy URI to temp file")
            null
        }
    }

    /**
     * Get the value of the data column for this Uri. This is useful for
     * MediaStore Uris, and other file-based ContentProviders.
     */
    private fun getDataColumn(
        context: Context, uri: Uri, selection: String?, selectionArgs: Array<String>?
    ): String? {
        var cursor: Cursor? = null
        val column = "_data"
        val projection = arrayOf(column)

        try {
            cursor = context.contentResolver.query(uri, projection, selection, selectionArgs, null)
            if (cursor != null && cursor.moveToFirst()) {
                val columnIndex = cursor.getColumnIndexOrThrow(column)
                return cursor.getString(columnIndex)
            }
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e(e, "UriToPathUtil: Error querying data column")
        } finally {
            cursor?.close()
        }
        return null
    }

    /**
     * @param uri The Uri to check.
     * @return Whether the Uri authority is ExternalStorageProvider.
     */
    private fun isExternalStorageDocument(uri: Uri): Boolean {
        return "com.android.externalstorage.documents" == uri.authority
    }

    /**
     * @param uri The Uri to check.
     * @return Whether the Uri authority is DownloadsProvider.
     */
    private fun isDownloadsDocument(uri: Uri): Boolean {
        return "com.android.providers.downloads.documents" == uri.authority
    }

    /**
     * @param uri The Uri to check.
     * @return Whether the Uri authority is MediaProvider.
     */
    private fun isMediaDocument(uri: Uri): Boolean {
        return "com.android.providers.media.documents" == uri.authority
    }

    /**
     * Clean up temporary files created for FFmpeg processing
     */
    fun cleanupTempFiles(context: Context) {
        try {
            val tempDir = File(context.cacheDir, "video_processing")
            if (tempDir.exists()) {
                val files = tempDir.listFiles()
                files?.forEach { file ->
                    if (file.isFile) {
                        file.delete()
                        Timber.tag("DEBUG_FLOW").d("UriToPathUtil: Deleted temp file: ${file.name}")
                    }
                }
            }
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e(e, "UriToPathUtil: Error cleaning up temp files")
        }
    }
}