package com.videomontage.freeeditingapps.utils

import android.content.Context
import android.graphics.Bitmap
import android.media.MediaMetadataRetriever
import android.os.Environment
import android.os.Handler
import android.os.Looper
import timber.log.Timber
import com.arthenica.mobileffmpeg.Config
import com.arthenica.mobileffmpeg.FFmpeg
import com.videomontage.freeeditingapps.model.ComModel
import kotlinx.coroutines.*
import java.io.File
import java.util.concurrent.ConcurrentHashMap

/**
 * Manages filter preview generation for video effects
 * Generates short preview clips for each filter effect
 */
class FilterPreviewManager(private val context: Context) {
    companion object {
        private const val TAG = "DEBUG_FilterPreview"
        private const val PREVIEW_DURATION = 3 // seconds
        private const val PREVIEW_WIDTH = 480  // Max width, height will be calculated to maintain aspect ratio
        private const val MAX_CONCURRENT_PREVIEWS = 3
    }

    private val previewCache = ConcurrentHashMap<String, String>()
    private val generatingPreviews = ConcurrentHashMap<String, Boolean>()
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val semaphore = kotlinx.coroutines.sync.Semaphore(MAX_CONCURRENT_PREVIEWS)

    interface PreviewGenerationListener {
        fun onPreviewReady(effectName: String, previewPath: String)
        fun onPreviewFailed(effectName: String, error: String)
        fun onProgress(effectName: String, progress: Float)
    }

    /**
     * Generate preview for a specific filter effect
     */
    fun generatePreview(
        videoPath: String,
        effect: ComModel,
        listener: PreviewGenerationListener
    ) {
        val effectName = effect.effectName
        
        // TEMPORARY: Clear sepia preview to test new encoding
        if (effectName == "Sepia") {
            val sepiaFile = File(context.cacheDir, "filter_previews/Sepia_preview.mp4")
            if (sepiaFile.exists()) {
                sepiaFile.delete()
                Timber.tag("DEBUG_FLOW").d("FilterPreviewManager: Deleted old Sepia preview for regeneration")
            }
            previewCache.remove("Sepia")
        }
        
        // Check if preview already exists
        previewCache[effectName]?.let { cachedPath ->
            if (File(cachedPath).exists()) {
                listener.onPreviewReady(effectName, cachedPath)
                return
            }
        }

        // Check if already generating
        if (generatingPreviews[effectName] == true) {
            return
        }

        generatingPreviews[effectName] = true
        
        coroutineScope.launch {
            try {
                semaphore.acquire()
                generatePreviewInternal(videoPath, effect, listener)
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    listener.onPreviewFailed(effectName, e.message ?: "Unknown error")
                }
            } finally {
                generatingPreviews[effectName] = false
                semaphore.release()
            }
        }
    }

    private suspend fun generatePreviewInternal(
        videoPath: String,
        effect: ComModel,
        listener: PreviewGenerationListener
    ) = withContext(Dispatchers.IO) {
        val effectName = effect.effectName
        
        val outputDir = File(context.cacheDir, "filter_previews")
        if (!outputDir.exists()) {
            outputDir.mkdirs()
        }

        val outputPath = File(outputDir, "${effectName.replace(" ", "_")}_preview.mp4").absolutePath
        
        // Delete existing preview file if it exists
        val outputFile = File(outputPath)
        if (outputFile.exists()) {
            outputFile.delete()
        }

        // Get video duration
        val duration = getVideoDuration(videoPath)
        val startTime = duration / 2 - PREVIEW_DURATION / 2
        
        // Build FFmpeg command for preview
        val command = buildPreviewCommand(videoPath, effect, outputPath, startTime, PREVIEW_DURATION)

        var lastProgress = 0f
        
        // Use string command directly for new API
        FFmpeg.executeAsync(command) { executionId, returnCode ->
            
            when (returnCode) {
                Config.RETURN_CODE_SUCCESS -> {
                    previewCache[effectName] = outputPath
                    
                    // TEMPORARY: Copy sepia preview to external storage for debugging
                    if (effectName == "Sepia") {
                        try {
                            val sourceFile = File(outputPath)
                            val downloadDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
                            val destFile = File(downloadDir, "Sepia_preview_debug.mp4")
                            sourceFile.copyTo(destFile, overwrite = true)
                            Timber.tag("DEBUG_FLOW").d("FilterPreviewManager: Copied Sepia preview to ${destFile.absolutePath}")
                        } catch (e: Exception) {
                            Timber.tag("DEBUG_FLOW").e("FilterPreviewManager: Failed to copy preview: ${e.message}")
                        }
                    }
                    
                    coroutineScope.launch(Dispatchers.Main) {
                        listener.onPreviewReady(effectName, outputPath)
                    }
                }
                Config.RETURN_CODE_CANCEL -> {
                    // Preview generation cancelled
                }
                else -> {
                    // Only log actual errors, not the full FFmpeg output
                    val lastLog = Config.getLastCommandOutput()
                    
                    val errorLines = lastLog.lines().filter { line ->
                        line.contains("Error", ignoreCase = true) || 
                        line.contains("Invalid", ignoreCase = true) ||
                        line.contains("failed", ignoreCase = true) ||
                        line.contains("No such", ignoreCase = true) ||
                        line.contains("was expected to have", ignoreCase = true)
                    }.joinToString("\n")
                    
                    if (errorLines.isNotEmpty()) {
                        Timber.e("FilterPreviewManager: $effectName failed - $errorLines")
                    }
                    
                    coroutineScope.launch(Dispatchers.Main) {
                        listener.onPreviewFailed(effectName, "FFmpeg error: $returnCode")
                    }
                }
            }
        }

        // Monitor progress and log output
        Config.enableStatisticsCallback { statistics ->
            val time = statistics.time
            val progress = (time.toFloat() / (PREVIEW_DURATION * 1000)).coerceIn(0f, 1f)
            if (progress - lastProgress > 0.1f) {
                lastProgress = progress
                coroutineScope.launch(Dispatchers.Main) {
                    listener.onProgress(effectName, progress)
                }
            }
        }
        
        // Enable log callback to see FFmpeg messages
        Config.enableLogCallback { message ->
            if (message.text.contains("error", ignoreCase = true) || 
                message.text.contains("warning", ignoreCase = true) ||
                message.text.contains("invalid", ignoreCase = true)) {
                Timber.tag("DEBUG_FLOW").d("FilterPreviewManager: FFmpeg log: ${message.text}")
            }
        }
    }

    private fun buildPreviewCommand(
        inputPath: String,
        effect: ComModel,
        outputPath: String,
        startTime: Float,
        duration: Int
    ): String {
        val baseCommand = "-y -ss $startTime -i \"$inputPath\" -t $duration"
        
        // Get the filter command from the effect
        val filterCommand = ComModel.getCommand<Any>(effect.effectName, inputPath, outputPath)
        
        val filterCommandStr = filterCommand as? String ?: return "$baseCommand -c:v libx264 -preset fast -vf scale=$PREVIEW_WIDTH:-2:force_original_aspect_ratio=decrease -pix_fmt yuv420p \"$outputPath\""

        // Extract the filter part from the command and rebuild with preview parameters
        // Look for complex filters that use multiple inputs/outputs or special syntax
        val needsComplexFilter = filterCommandStr.contains("split[") || 
                                filterCommandStr.contains("][") ||
                                filterCommandStr.contains("displace") ||
                                filterCommandStr.contains("blend") ||
                                filterCommandStr.contains("-filter_complex")
        
        val filterRegex = if (needsComplexFilter) {
            "-filter_complex\\s+[\"']?([^\"']+)[\"']?".toRegex()
        } else {
            "-vf\\s+[\"']?([^\"']+)[\"']?".toRegex()
        }
        
        val filterMatch = filterRegex.find(filterCommandStr)
        
        val filter = when {
            needsComplexFilter && filterMatch != null -> {
                val complexFilter = filterMatch.groupValues[1]
                
                // For complex filters with multiple filter chains (contains semicolon)
                if (complexFilter.contains(";")) {
                    // Find the last output pad name (e.g., [color_effect])
                    val outputPadRegex = "\\[([^\\]]+)\\]\\s*$".toRegex()
                    val outputMatch = outputPadRegex.find(complexFilter)
                    
                    if (outputMatch != null) {
                        val lastOutputPad = outputMatch.groupValues[1]
                        // Append scale filter using the last output pad - maintain aspect ratio
                        "$complexFilter;[$lastOutputPad]scale=$PREVIEW_WIDTH:-2:force_original_aspect_ratio=decrease[scaled]"
                    } else {
                        // Fallback: just append scale
                        "$complexFilter;[0:v]scale=$PREVIEW_WIDTH:-2:force_original_aspect_ratio=decrease[scaled]"
                    }
                } else {
                    // Simple complex filter, just append scale
                    "$complexFilter,scale=$PREVIEW_WIDTH:-2:force_original_aspect_ratio=decrease"
                }
            }
            filterMatch != null -> {
                val simpleFilter = filterMatch.groupValues[1]
                "$simpleFilter,scale=$PREVIEW_WIDTH:-2:force_original_aspect_ratio=decrease"
            }
            else -> {
                "scale=$PREVIEW_WIDTH:-2:force_original_aspect_ratio=decrease"
            }
        }
        
        val finalCommand = if (needsComplexFilter) {
            // For complex filters with mapping
            "$baseCommand -filter_complex \"$filter\" -map [scaled] -c:v libx264 -preset fast -pix_fmt yuv420p -an \"$outputPath\""
        } else {
            // For simple filters
            "$baseCommand -vf \"$filter\" -c:v libx264 -preset fast -pix_fmt yuv420p -an \"$outputPath\""
        }
        
        return finalCommand
    }

    private fun getVideoDuration(videoPath: String): Float {
        return try {
            val retriever = MediaMetadataRetriever()
            retriever.setDataSource(videoPath)
            val durationStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
            retriever.release()
            (durationStr?.toLong() ?: 0L) / 1000f
        } catch (e: Exception) {
            Timber.e(e, "FilterPreviewManager: Error getting video duration")
            10f // Default to 10 seconds
        }
    }

    /**
     * Generate previews for multiple effects
     */
    fun generatePreviews(
        videoPath: String,
        effects: List<ComModel>,
        listener: PreviewGenerationListener
    ) {
        effects.forEach { effect ->
            generatePreview(videoPath, effect, listener)
        }
    }

    /**
     * Clear all cached previews
     */
    private fun clearCache() {
        coroutineScope.launch {
            val previewDir = File(context.cacheDir, "filter_previews")
            if (previewDir.exists()) {
                previewDir.listFiles()?.forEach { it.delete() }
            }
            previewCache.clear()
        }
    }

    /**
     * Get cached preview path if available
     */
    fun getCachedPreview(effectName: String): String? {
        return previewCache[effectName]?.takeIf { File(it).exists() }
    }

    /**
     * Cancel all ongoing preview generations
     */
    fun cancelAll() {
        FFmpeg.cancel()
        coroutineScope.cancel()
    }

    fun destroy() {
        cancelAll()
        clearCache()
    }
}