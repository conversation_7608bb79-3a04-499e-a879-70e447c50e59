package com.videomontage.freeeditingapps.utils.assets

import android.content.Context
import android.util.Log
import java.io.IOException

object AssetsUtil {

    private var fontList = mutableListOf<FontItem>()

    fun listAssetFiles(path: String, context: Context): Boolean {
        val list: Array<String>
        try {
            list = context.getAssets().list(path) as Array<String>
            if (list.size > 0) {
                // This is a folder
                for (file in list) {
                    if (!listAssetFiles("$path/$file", context)) return false else {
                        // This is a file
                        // TODO: add file name to an array list
//                        Log.d("TAG", "listAssetFiles: $file")
                        if (!file.contains(".png"))
                            fontList.add(FontItem(
                                    fontFile = file,
                                    fontImage = file.replace("otf", "png").replace("ttf", "png")
                            ))
                    }
                }
            }
        } catch (e: IOException) {
            return false
        }
        return true
    }

    fun getFontList() : List<FontItem> = fontList
}