package com.videomontage.freeeditingapps.utils.videoclipper

import android.content.Context
import android.content.res.Resources
import android.media.MediaScannerConnection
import android.os.Environment
import android.util.Log
import android.widget.Toast
import com.arthenica.mobileffmpeg.Config
import com.arthenica.mobileffmpeg.FFmpeg
import java.io.File

open class FFmpegCreator(
        private val context: Context
) {

    private val TAG = FFmpegCreator::class.java.simpleName
    private var scanGallery = false

    protected fun execFFmpegBinary(command: String, path: String, title: String, result: (FFmpegCreatorResult) -> Unit) {
        Log.d(TAG, "execFFmpegBinary: $command")
        val executionId = FFmpeg.executeAsync(command) { executionId, rc ->
            if (rc == Config.RETURN_CODE_SUCCESS) {
                Log.i(Config.TAG, "Command execution completed successfully.")
                if (scanGallery)
                    scanGalleryAfterSaving(title)
                result.invoke(FFmpegCreatorResult.Success(path))
            } else if (rc == Config.RETURN_CODE_CANCEL) {
                Log.i(Config.TAG, "Command execution cancelled by user.")
                Toast.makeText(context, "FAILURE", Toast.LENGTH_SHORT).show()

                //                        afterFFmpegFailure();
            } else {
                result.invoke(FFmpegCreatorResult.Fail)

                Log.i(Config.TAG, String.format("Command execution failed with rc=%d and the output below.", rc))
                Config.printLastCommandOutput(Log.INFO)
                Toast.makeText(context, "FAILURE", Toast.LENGTH_SHORT).show()

            }

            scanGallery = false

        }
//            Config.enableLogCallback {
//                //                    int durationFile = (int) Utils.getProgress(message.getText(), Long.parseLong(videoModel.getDuration()) / 1000);
////                    float percent = durationFile / (Float.parseFloat(videoModel.getDuration()) / 1000);
////                    if (progressDialog != null) {
////                        progressDialog.setProgress((int) (percent * 100));
////                    }
//            }
    }

    private fun scanGalleryAfterSaving(title: String) {
        val file = File(Environment.getExternalStorageDirectory().toString() + File.separator + "/Movies/Montage/" + title)
        MediaScannerConnection.scanFile(context, arrayOf(file.absolutePath), arrayOf("video/mp4"), null)
    }

    // Should be enabled if video is exported on external storage(DCIM/Movies/Photo)
    protected fun scanGallery(scan: Boolean) {
        scanGallery = scan
    }

    fun getScreenWidth(): Int {
        return Resources.getSystem().getDisplayMetrics().widthPixels
    }

    fun getScreenHeight(): Int {
        return Resources.getSystem().getDisplayMetrics().heightPixels
    }

    sealed class FFmpegCreatorResult {
        data class Success(val path: String) : FFmpegCreatorResult()
        object Fail : FFmpegCreatorResult()
    }
}