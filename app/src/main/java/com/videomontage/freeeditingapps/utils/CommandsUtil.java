package com.videomontage.freeeditingapps.utils;

import android.content.Context;
import android.content.ContextWrapper;
import android.media.MediaPlayer;
import android.net.Uri;
import android.util.Log;

import com.videomontage.freeeditingapps.model.CommandModel;

import java.io.File;
import java.util.ArrayList;
import java.util.concurrent.TimeUnit;

public class CommandsUtil {

    public static ArrayList commands;
    public static final String THUMBNAILS_DIR = "thumbnails";
    public static int COUNTER = 0;
    public static String LAST_SEEN_VID = "";
    public static void initCommands(String inPath, String outPath, Context context){
        COUNTER = 0;
        String[] gamaAndSaturationCommand = {"-i", inPath, "-filter_complex", "eq=gamma=7.0:saturation=1.3", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "gamaAndSaturation.png", context).getAbsolutePath()};
        String[] zoomConstantCommand = {"-i", inPath, "-filter_complex", "scale=2*iw:-1, crop=iw/2:ih/2", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "zoomConstant.png", context).getAbsolutePath()};
        String[] fadeInCommand = {"-i", inPath, "-filter_complex", "fade=in:st=0:d=4; afade=in:st=0:d=4", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "fadeIn.png", context).getAbsolutePath()};
        String[] fadeoutCommand = {"-i", inPath, "-filter_complex", "fade=out:st=6:d=4; afade=out:st=6:d=4", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "fadeOut.png", context).getAbsolutePath()};

        String[] boomerangCommand = {"-y" ,"-i",inPath,  "-filter_complex", "[0]reverse[r];[0][r][0]concat=n=3,setpts=0.5*PTS", "-frames:v", " 1",  createFile(THUMBNAILS_DIR, "boomerang.png", context).getAbsolutePath()};
        String[] boxCommand = {"-i", inPath, "-filter_complex", "[0:v]scale=720:720,boxblur=luma_radius=min(h\\,w)/20:luma_power=1:chroma_radius=min(cw\\,ch)/20:chroma_power=1[bg];[0:v]scale=720:720:force_original_aspect_ratio=decrease[fg];[bg][fg]overlay=(W-w)/2:(H-h)/2[outv]", "-map", "[outv]", "-map", "0:a?", outPath};
        String[] niceFilterCommand = {"-i", inPath, "-filter_complex", "[0:v]eq=contrast=1:brightness=0:saturation=1:gamma=1:gamma_r=1:gamma_g=1:gamma_b=1:gamma_weight=1[outv]", "-map", "[outv]", "-map", "0:a?", outPath};
//        String[] invertColorsCommand = {"-y", "-i", videoModel.getPath(), "-vf", "negate", newPath};
        String[] blackAndWhiteCommand = {"-y", "-i", inPath, "-vf", "hue=s=0", "-preset", "ultrafast", "-c:a", "copy", "-frames:v", "1", createFile(THUMBNAILS_DIR, "blackAndWhite.png", context).getAbsolutePath()};
        String[] sepiaCommand = {"-i", inPath, "-strict", "experimental", "-preset", "ultrafast", "-filter_complex", "[0:v]colorchannelmixer=.393:.769:.189:0:.349:.686:.168:0:.272:.534:.131[colorchannelmixed];[colorchannelmixed]eq=1.0:0:1.3:2.4:1.0:1.0:1.0:1.0[color_effect]", "-map", "[color_effect]", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "sepia.png", context).getAbsolutePath()};
        String[] vignetteCommand = {"-i", inPath, "-preset", "ultrafast", "-vf", "vignette=angle=PI/4", outPath};
        String[] gammaCorrenctionCommand = {"-i", inPath, "-preset", "ultrafast", "-vf", "eq=gamma=7.0:saturation=1.3","-c:a", "copy", "-frames:v", "1", createFile(THUMBNAILS_DIR, "gammaCorrection.png", context).getAbsolutePath()};
        String[] stabilizationCommand = {"-i", inPath, "-preset", "ultrafast","-frames:v", " 1", createFile(THUMBNAILS_DIR, "stabilization.png", context).getAbsolutePath()};
        String[] sharpCommand = {"-i", inPath, "-preset", "ultrafast", "-vf", "unsharp", "-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "sharp.png", context).getAbsolutePath()};
        String[] zoomInCommand = {"-i", inPath, "-preset", "ultrafast", "-vf", "scale=2*iw:-1, crop=iw/2:ih/2", "-c:a", "copy", outPath};
        // this command makes video longer so progress bar not working as expected
        String[] zoomInProgressivelyCommand = {"-i", inPath, "-filter_complex", "scale=2*iw:-1, crop=iw/2:ih/2", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "zoomInProgressively.png", context).getAbsolutePath()};
        String[] flipVerticalCommand = {"-i", inPath, "-vf", "vflip", "-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "flipVertical.png", context).getAbsolutePath()};
        String[] flipHorizontalCommand = {"-i", inPath, "-vf", "hflip", "-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "flipHorizontal.png", context).getAbsolutePath()};
        String[] rotate90DegreesClockwiseCommand = {"-i", inPath, "-vf", "transpose=1", "-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "rotate90DegreesClockwise.png", context).getAbsolutePath()};
        String[] rotate90DegreesCounterclockwiseCommand = {"-i", inPath, "-vf", "transpose=2", "-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "rotate90DegreesCounterclockwise.png", context).getAbsolutePath()};
        String[] blurCommand = {"-i", inPath, "-filter_complex", "[0:v]boxblur=10[bg];[0:v]crop=100:100:60:30[fg];[bg][fg]overlay=60:30", "-frames:v", " 1",  createFile(THUMBNAILS_DIR, "blur.png", context).getAbsolutePath()};
        String[] convolutionCommand = {"-i", inPath, "-vf", "convolution=\"-2 -1 0 -1 1 1 0 1 2:-2 -1 0 -1 1 1 0 1 2:-2 -1 0 -1 1 1 0 1 2:-2 -1 0 -1 1 1 0 1 2\"", "-c:a", "copy", "-frames:v", " 1",  createFile(THUMBNAILS_DIR, "convolution.png", context).getAbsolutePath()};
        String[] convolution2Command = {"-i", inPath, "-vf", "convolution=1 1 1 1 -8 1 1 1 1:1 1 1 1 -8 1 1 1 1:1 1 1 1 -8 1 1 1 1:1 1 1 1 -8 1 1 1 1:5:5:5:1:0:128:128:0", "-c:a", "copy", "-frames:v", " 1",  createFile(THUMBNAILS_DIR, "convolution2.png", context).getAbsolutePath()};
//        String[] verticalVideoWithBlurAsideCommand = {"-i", videoModel.getPath(), "-lavfi", "[0:v]scale=ih*16/9:-1,boxblur=luma_radius=min(h\\,w)/20:luma_power=1:chroma_radius=min(cw\\,ch)/20:chroma_power=1[bg];[bg][0:v]overlay=(W-w)/2:(H-h)/2,crop=h=iw*9/16","-vb", "800K","-pix_fmt","yuv420p","-y", newPath};
        //startd play probably dont support this video but vlc does
        String[] stereoscopicCommand = {"-i", inPath, "-vf", "stereo3d=sbs2l:arcc", "-c:a", "copy","-pix_fmt","yuv420p","-y", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "stereoscopic.png", context).getAbsolutePath()};
        String[] pixelizeCommand = {"-i", inPath, "-vf", "scale=iw/10:ih/10,scale=10*iw:10*ih:flags=neighbor", "-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "pixelize.png", context).getAbsolutePath()};
        String[] brightnessPlusCommand = {"-i", inPath, "-vf", "eq=brightness=0.3", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "brightnessPlus.png", context).getAbsolutePath()};
        String[] brightnessMinusCommand = {"-i", inPath, "-vf", "eq=brightness=-0.3", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "brightnessMinus.png", context).getAbsolutePath()};
        String[] saturationMinusCommand = {"-i", inPath, "-vf", "eq=saturation=0.7", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "saturationMinus.png", context).getAbsolutePath()};
        String[] saturationPlusCommand = {"-i", inPath, "-vf", "eq=saturation=1.3", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "saturationPlus.png", context).getAbsolutePath()};

        String[] gammaRMinusCommand = {"-i", inPath, "-vf", "eq=gamma_r=0.5", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "gammaRMinus.png", context).getAbsolutePath()};
        String[] gammaRPlusCommand = {"-i", inPath, "-vf", "eq=gamma_r=1.5", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "gammaRPlus.png", context).getAbsolutePath()};

        String[] gammaGMinusCommand = {"-i", inPath, "-vf", "eq=gamma_g=0.5", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "gammaGMinus.png", context).getAbsolutePath()};
        String[] gammaGPlusCommand = {"-i", inPath, "-vf", "eq=gamma_g=1.5", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "gammaGPlus.png", context).getAbsolutePath()};

        String[] gammaBMinusCommand = {"-i", inPath, "-vf", "eq=gamma_b=0.5", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "gammaBMinus.png", context).getAbsolutePath()};
        String[] gammaBPlusCommand = {"-i", inPath, "-vf", "eq=gamma_b=1.5", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "gammaBPlus.png", context).getAbsolutePath()};

        String[] colorbalanceCommand = {"-i", inPath, "-vf", "colorbalance=rs=-0.3:bs=0.3:rh=0.1:bh=-0.1", "-pix_fmt","yuv420p","-y", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "colorbalance.png", context).getAbsolutePath()};
        String[] colorbalance2Command = {"-i", inPath, "-vf", "colorbalance=gs=0.3:rh=0.1:bh=0.1","-pix_fmt","yuv420p","-y", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "colorbalance2.png", context).getAbsolutePath()};
        String[] vintageCommand = {"-i", inPath, "-vf", "curves=vintage", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "vintage.png", context).getAbsolutePath()};

        String[] colorNegativeCommand = {"-i", inPath, "-vf", "curves=color_negative", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "colorNegative.png", context).getAbsolutePath()};
        String[] crossProcessCommand = {"-i", inPath, "-vf", "curves=cross_process", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "crossProcess.png", context).getAbsolutePath()};
        String[] darkerCommand = {"-i", inPath, "-vf", "curves=darker", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "darker.png", context).getAbsolutePath()};
        String[] increaseContrastCommand = {"-i", inPath, "-vf", "curves=increase_contrast", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "increaseContrast.png", context).getAbsolutePath()};
        String[] lighterCommand = {"-i", inPath, "-vf", "curves=lighter", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "lighter.png", context).getAbsolutePath()};
        String[] linearContrastCommand = {"-i", inPath, "-vf", "curves=linear_contrast", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "linearContrast.png", context).getAbsolutePath()};
        String[] mediumContrastCommand = {"-i", inPath, "-vf", "curves=medium_contrast", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "mediumContrast.png", context).getAbsolutePath()};
        String[] strongContrastCommand = {"-i", inPath, "-vf", "curves=strong_contrast", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "strongContrast.png", context).getAbsolutePath()};
        String[] negativeCommand = {"-i", inPath, "-vf", "curves=negative", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "negative.png", context).getAbsolutePath()};
        String[] curvesBlueCommand = {"-i", inPath, "-vf", "curves=blue='0/0 0.5/0.58 1/1'", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "curvesBlue.png", context).getAbsolutePath()};

        String[] elbg2Command = {"-i", inPath, "-vf", "elbg=2:n=1", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "elbg2.png", context).getAbsolutePath()};
        String[] elbg4Command = {"-i", inPath, "-vf", "elbg=4:n=1", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "elbg4.png", context).getAbsolutePath()};
        String[] elbg8Command = {"-i", inPath, "-vf", "elbg=8:n=1", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "elbg8.png", context).getAbsolutePath()};
        String[] elbg16Command = {"-i", inPath, "-vf", "elbg=16:n=1", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "elbg16.png", context).getAbsolutePath()};

        String[] debandCommand = {"-i", inPath, "-vf", "deband=1thr=0.5:2thr=0.5:3thr=0.5", "-pix_fmt","yuv420p","-color_range", "2","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "deband.png", context).getAbsolutePath()};
        String[] swapRect6x6ver1Command = {"-i", inPath, "-vf", "swaprect=w/6:h/6:0:5*h/6,swaprect=w/6:h/6:5*w/6:h/2,swaprect=w/6:h/6:w/3:5*h/6,swaprect=w/6:h/6:0:h/3,swaprect=w/6:h/6:w/6:0,swaprect=w/6:h/6:w/2:h/6,swaprect=w/6:h/6:5*w/6:5*h/6,swaprect=w/6:h/6:2*w/3:0,swaprect=w/6:h/6:w/3:h/6,swaprect=w/6:h/6:2*w/3:h/3,swaprect=w/6:h/6:0:h/2,swaprect=w/6:h/6:2*w/3:2*h/3,swaprect=w/6:h/6:w/2:5*h/6,swaprect=w/6:h/6:w/2:0,swaprect=w/6:h/6:5*w/6:0,swaprect=w/6:h/6:w/2:h/3,swaprect=w/6:h/6:2*w/3:h/2,swaprect=w/6:h/6:0:h/6,swaprect=w/6:h/6:w/6:h/3,swaprect=w/6:h/6:5*w/6:2*h/3,swaprect=w/6:h/6:0:2*h/3,swaprect=w/6:h/6:w/3:h/3,swaprect=w/6:h/6:2*w/3:5*h/6,swaprect=w/6:h/6:w/6:2*h/3,swaprect=w/6:h/6:w/6:h/6,swaprect=w/6:h/6:2*w/3:h/6,swaprect=w/6:h/6:w/3:2*h/3,swaprect=w/6:h/6:w/2:h/2,swaprect=w/6:h/6:w/2:2*h/3,swaprect=w/6:h/6:w/3:h/2,swaprect=w/6:h/6:w/6:h/2,swaprect=w/6:h/6:5*w/6:h/3,swaprect=w/6:h/6:w/6:5*h/6,swaprect=w/6:h/6:5*w/6:h/6,swaprect=w/6:h/6:0:0,swaprect=w/6:h/6:w/3:0", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "swapRect6x6ver1.png", context).getAbsolutePath()};
        String[] swapRect6x6ver2Command = {"-i", inPath, "-vf", "swaprect=w/6:h/6:w/3:0,swaprect=w/6:h/6:0:0,swaprect=w/6:h/6:5*w/6:h/6,swaprect=w/6:h/6:w/6:5*h/6,swaprect=w/6:h/6:5*w/6:h/3,swaprect=w/6:h/6:w/6:h/2,swaprect=w/6:h/6:w/3:h/2,swaprect=w/6:h/6:w/2:2*h/3,swaprect=w/6:h/6:w/2:h/2,swaprect=w/6:h/6:w/3:2*h/3,swaprect=w/6:h/6:2*w/3:h/6,swaprect=w/6:h/6:w/6:h/6,swaprect=w/6:h/6:w/6:2*h/3,swaprect=w/6:h/6:2*w/3:5*h/6,swaprect=w/6:h/6:w/3:h/3,swaprect=w/6:h/6:0:2*h/3,swaprect=w/6:h/6:5*w/6:2*h/3,swaprect=w/6:h/6:w/6:h/3,swaprect=w/6:h/6:0:h/6,swaprect=w/6:h/6:2*w/3:h/2,swaprect=w/6:h/6:w/2:h/3,swaprect=w/6:h/6:5*w/6:0,swaprect=w/6:h/6:w/2:0,swaprect=w/6:h/6:w/2:5*h/6,swaprect=w/6:h/6:2*w/3:2*h/3,swaprect=w/6:h/6:0:h/2,swaprect=w/6:h/6:2*w/3:h/3,swaprect=w/6:h/6:w/3:h/6,swaprect=w/6:h/6:2*w/3:0,swaprect=w/6:h/6:5*w/6:5*h/6,swaprect=w/6:h/6:w/2:h/6,swaprect=w/6:h/6:w/6:0,swaprect=w/6:h/6:0:h/3,swaprect=w/6:h/6:w/3:5*h/6,swaprect=w/6:h/6:5*w/6:h/2,swaprect=w/6:h/6:0:5*h/6", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "swapRect6x6ver2.png", context).getAbsolutePath()};
        String[] swapRect10x6ver1Command = {"-i", inPath, "-vf", "swaprect=w/10:h/6:w/10:5*h/6,swaprect=w/10:h/6:w/10:h/3,swaprect=w/10:h/6:0:h/3,swaprect=w/10:h/6:2*w/5:5*h/6,swaprect=w/10:h/6:0:h/2,swaprect=w/10:h/6:3*w/5:2*h/3,swaprect=w/10:h/6:7*w/10:0,swaprect=w/10:h/6:3*w/10:h/6,swaprect=w/10:h/6:9*w/10:h/3,swaprect=w/10:h/6:4*w/5:2*h/3,swaprect=w/10:h/6:3*w/5:h/6,swaprect=w/10:h/6:2*w/5:2*h/3,swaprect=w/10:h/6:4*w/5:5*h/6,swaprect=w/10:h/6:2*w/5:h/6,swaprect=w/10:h/6:4*w/5:h/2,swaprect=w/10:h/6:9*w/10:h/6,swaprect=w/10:h/6:w/5:0,swaprect=w/10:h/6:w/2:h/3,swaprect=w/10:h/6:3*w/10:h/2,swaprect=w/10:h/6:7*w/10:h/6,swaprect=w/10:h/6:2*w/5:h/2,swaprect=w/10:h/6:3*w/5:h/3,swaprect=w/10:h/6:2*w/5:h/3,swaprect=w/10:h/6:4*w/5:h/6,swaprect=w/10:h/6:3*w/10:5*h/6,swaprect=w/10:h/6:7*w/10:5*h/6,swaprect=w/10:h/6:w/10:0,swaprect=w/10:h/6:w/5:h/3,swaprect=w/10:h/6:w/5:5*h/6,swaprect=w/10:h/6:4*w/5:h/3,swaprect=w/10:h/6:w/2:h/2,swaprect=w/10:h/6:3*w/5:5*h/6,swaprect=w/10:h/6:0:5*h/6,swaprect=w/10:h/6:0:2*h/3,swaprect=w/10:h/6:w/10:2*h/3,swaprect=w/10:h/6:w/2:2*h/3,swaprect=w/10:h/6:w/5:h/2,swaprect=w/10:h/6:4*w/5:0,swaprect=w/10:h/6:w/10:h/2,swaprect=w/10:h/6:7*w/10:h/2,swaprect=w/10:h/6:9*w/10:5*h/6,swaprect=w/10:h/6:9*w/10:h/2,swaprect=w/10:h/6:w/5:h/6,swaprect=w/10:h/6:3*w/10:2*h/3,swaprect=w/10:h/6:w/2:5*h/6,swaprect=w/10:h/6:2*w/5:0,swaprect=w/10:h/6:9*w/10:2*h/3,swaprect=w/10:h/6:3*w/10:h/3,swaprect=w/10:h/6:0:h/6,swaprect=w/10:h/6:9*w/10:0,swaprect=w/10:h/6:3*w/10:0,swaprect=w/10:h/6:3*w/5:h/2,swaprect=w/10:h/6:w/2:0,swaprect=w/10:h/6:7*w/10:2*h/3,swaprect=w/10:h/6:7*w/10:h/3,swaprect=w/10:h/6:w/10:h/6,swaprect=w/10:h/6:w/2:h/6,swaprect=w/10:h/6:w/5:2*h/3,swaprect=w/10:h/6:3*w/5:0,swaprect=w/10:h/6:0:0", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", "1", createFile(THUMBNAILS_DIR, "swapRect10x6ver1.png", context).getAbsolutePath()};
        String[] swapRect10x6ver2Command = {"-i", inPath, "-vf", "swaprect=w/10:h/6:0:0,swaprect=w/10:h/6:3*w/5:0,swaprect=w/10:h/6:w/5:2*h/3,swaprect=w/10:h/6:w/2:h/6,swaprect=w/10:h/6:w/10:h/6,swaprect=w/10:h/6:7*w/10:h/3,swaprect=w/10:h/6:7*w/10:2*h/3,swaprect=w/10:h/6:w/2:0,swaprect=w/10:h/6:3*w/5:h/2,swaprect=w/10:h/6:3*w/10:0,swaprect=w/10:h/6:9*w/10:0,swaprect=w/10:h/6:0:h/6,swaprect=w/10:h/6:3*w/10:h/3,swaprect=w/10:h/6:9*w/10:2*h/3,swaprect=w/10:h/6:2*w/5:0,swaprect=w/10:h/6:w/2:5*h/6,swaprect=w/10:h/6:3*w/10:2*h/3,swaprect=w/10:h/6:w/5:h/6,swaprect=w/10:h/6:9*w/10:h/2,swaprect=w/10:h/6:9*w/10:5*h/6,swaprect=w/10:h/6:7*w/10:h/2,swaprect=w/10:h/6:w/10:h/2,swaprect=w/10:h/6:4*w/5:0,swaprect=w/10:h/6:w/5:h/2,swaprect=w/10:h/6:w/2:2*h/3,swaprect=w/10:h/6:w/10:2*h/3,swaprect=w/10:h/6:0:2*h/3,swaprect=w/10:h/6:0:5*h/6,swaprect=w/10:h/6:3*w/5:5*h/6,swaprect=w/10:h/6:w/2:h/2,swaprect=w/10:h/6:4*w/5:h/3,swaprect=w/10:h/6:w/5:5*h/6,swaprect=w/10:h/6:w/5:h/3,swaprect=w/10:h/6:w/10:0,swaprect=w/10:h/6:7*w/10:5*h/6,swaprect=w/10:h/6:3*w/10:5*h/6,swaprect=w/10:h/6:4*w/5:h/6,swaprect=w/10:h/6:2*w/5:h/3,swaprect=w/10:h/6:3*w/5:h/3,swaprect=w/10:h/6:2*w/5:h/2,swaprect=w/10:h/6:7*w/10:h/6,swaprect=w/10:h/6:3*w/10:h/2,swaprect=w/10:h/6:w/2:h/3,swaprect=w/10:h/6:w/5:0,swaprect=w/10:h/6:9*w/10:h/6,swaprect=w/10:h/6:4*w/5:h/2,swaprect=w/10:h/6:2*w/5:h/6,swaprect=w/10:h/6:4*w/5:5*h/6,swaprect=w/10:h/6:2*w/5:2*h/3,swaprect=w/10:h/6:3*w/5:h/6,swaprect=w/10:h/6:4*w/5:2*h/3,swaprect=w/10:h/6:9*w/10:h/3,swaprect=w/10:h/6:3*w/10:h/6,swaprect=w/10:h/6:7*w/10:0,swaprect=w/10:h/6:3*w/5:2*h/3,swaprect=w/10:h/6:0:h/2,swaprect=w/10:h/6:2*w/5:5*h/6,swaprect=w/10:h/6:0:h/3,swaprect=w/10:h/6:w/10:h/3,swaprect=w/10:h/6:w/10:5*h/6", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", "1", createFile(THUMBNAILS_DIR, "swapRect10x6ver2.png", context).getAbsolutePath()};
        String[] swapRect16x9ver1Command = {"-i", inPath, "-vf", "swaprect=w/16:h/9:w/16:5*h/9,swaprect=w/16:h/9:7*w/16:2*h/9,swaprect=w/16:h/9:w/2:8*h/9,swaprect=w/16:h/9:7*w/8:h/3,swaprect=w/16:h/9:13*w/16:8*h/9,swaprect=w/16:h/9:3*w/4:h/9,swaprect=w/16:h/9:5*w/8:h/3,swaprect=w/16:h/9:9*w/16:5*h/9,swaprect=w/16:h/9:w/2:h/3,swaprect=w/16:h/9:0:8*h/9,swaprect=w/16:h/9:3*w/8:2*h/9,swaprect=w/16:h/9:5*w/8:5*h/9,swaprect=w/16:h/9:11*w/16:8*h/9,swaprect=w/16:h/9:0:4*h/9,swaprect=w/16:h/9:11*w/16:h/3,swaprect=w/16:h/9:7*w/8:7*h/9,swaprect=w/16:h/9:15*w/16:4*h/9,swaprect=w/16:h/9:3*w/16:7*h/9,swaprect=w/16:h/9:5*w/16:h/9,swaprect=w/16:h/9:13*w/16:h/9,swaprect=w/16:h/9:9*w/16:4*h/9,swaprect=w/16:h/9:w/4:2*h/9,swaprect=w/16:h/9:w/8:4*h/9,swaprect=w/16:h/9:9*w/16:2*h/3,swaprect=w/16:h/9:5*w/16:h/3,swaprect=w/16:h/9:w/16:8*h/9,swaprect=w/16:h/9:3*w/4:2*h/3,swaprect=w/16:h/9:3*w/4:0,swaprect=w/16:h/9:5*w/16:7*h/9,swaprect=w/16:h/9:13*w/16:2*h/9,swaprect=w/16:h/9:5*w/16:2*h/3,swaprect=w/16:h/9:5*w/8:4*h/9,swaprect=w/16:h/9:w/4:2*h/3,swaprect=w/16:h/9:3*w/8:4*h/9,swaprect=w/16:h/9:w/4:5*h/9,swaprect=w/16:h/9:7*w/8:5*h/9,swaprect=w/16:h/9:w/2:2*h/3,swaprect=w/16:h/9:13*w/16:0,swaprect=w/16:h/9:w/16:2*h/9,swaprect=w/16:h/9:11*w/16:4*h/9,swaprect=w/16:h/9:7*w/8:4*h/9,swaprect=w/16:h/9:3*w/16:4*h/9,swaprect=w/16:h/9:0:5*h/9,swaprect=w/16:h/9:3*w/8:8*h/9,swaprect=w/16:h/9:0:h/3,swaprect=w/16:h/9:w/16:7*h/9,swaprect=w/16:h/9:9*w/16:7*h/9,swaprect=w/16:h/9:3*w/8:7*h/9,swaprect=w/16:h/9:w/4:7*h/9,swaprect=w/16:h/9:w/8:2*h/3,swaprect=w/16:h/9:w/2:0,swaprect=w/16:h/9:0:2*h/9,swaprect=w/16:h/9:3*w/4:2*h/9,swaprect=w/16:h/9:5*w/8:0,swaprect=w/16:h/9:3*w/8:h/3,swaprect=w/16:h/9:7*w/16:h/3,swaprect=w/16:h/9:9*w/16:h/3,swaprect=w/16:h/9:w/8:h/3,swaprect=w/16:h/9:3*w/8:5*h/9,swaprect=w/16:h/9:3*w/16:5*h/9,swaprect=w/16:h/9:5*w/16:0,swaprect=w/16:h/9:11*w/16:2*h/9,swaprect=w/16:h/9:w/8:2*h/9,swaprect=w/16:h/9:w/16:h/3,swaprect=w/16:h/9:3*w/16:8*h/9,swaprect=w/16:h/9:w/4:h/9,swaprect=w/16:h/9:7*w/8:2*h/3,swaprect=w/16:h/9:13*w/16:5*h/9,swaprect=w/16:h/9:7*w/8:0,swaprect=w/16:h/9:3*w/16:h/9,swaprect=w/16:h/9:3*w/4:h/3,swaprect=w/16:h/9:7*w/16:h/9,swaprect=w/16:h/9:15*w/16:7*h/9,swaprect=w/16:h/9:7*w/16:5*h/9,swaprect=w/16:h/9:15*w/16:5*h/9,swaprect=w/16:h/9:15*w/16:0,swaprect=w/16:h/9:0:h/9,swaprect=w/16:h/9:11*w/16:2*h/3,swaprect=w/16:h/9:w/2:2*h/9,swaprect=w/16:h/9:7*w/8:h/9,swaprect=w/16:h/9:9*w/16:8*h/9,swaprect=w/16:h/9:3*w/4:8*h/9,swaprect=w/16:h/9:w/2:4*h/9,swaprect=w/16:h/9:0:7*h/9,swaprect=w/16:h/9:w/8:0,swaprect=w/16:h/9:13*w/16:2*h/3,swaprect=w/16:h/9:11*w/16:0,swaprect=w/16:h/9:w/2:5*h/9,swaprect=w/16:h/9:15*w/16:8*h/9,swaprect=w/16:h/9:w/4:h/3,swaprect=w/16:h/9:13*w/16:h/3,swaprect=w/16:h/9:3*w/16:2*h/9,swaprect=w/16:h/9:7*w/16:8*h/9,swaprect=w/16:h/9:3*w/8:2*h/3,swaprect=w/16:h/9:15*w/16:h/3,swaprect=w/16:h/9:w/16:2*h/3,swaprect=w/16:h/9:0:0,swaprect=w/16:h/9:w/2:7*h/9,swaprect=w/16:h/9:5*w/8:7*h/9,swaprect=w/16:h/9:5*w/8:8*h/9,swaprect=w/16:h/9:w/16:0,swaprect=w/16:h/9:w/8:h/9,swaprect=w/16:h/9:5*w/16:5*h/9,swaprect=w/16:h/9:w/8:5*h/9,swaprect=w/16:h/9:3*w/16:2*h/3,swaprect=w/16:h/9:w/4:8*h/9,swaprect=w/16:h/9:9*w/16:0,swaprect=w/16:h/9:5*w/8:2*h/3,swaprect=w/16:h/9:7*w/8:2*h/9,swaprect=w/16:h/9:3*w/4:4*h/9,swaprect=w/16:h/9:7*w/16:7*h/9,swaprect=w/16:h/9:w/16:4*h/9,swaprect=w/16:h/9:5*w/16:8*h/9,swaprect=w/16:h/9:5*w/16:4*h/9,swaprect=w/16:h/9:13*w/16:4*h/9,swaprect=w/16:h/9:7*w/16:0,swaprect=w/16:h/9:w/16:h/9,swaprect=w/16:h/9:15*w/16:h/9,swaprect=w/16:h/9:0:2*h/3,swaprect=w/16:h/9:9*w/16:h/9,swaprect=w/16:h/9:13*w/16:7*h/9,swaprect=w/16:h/9:15*w/16:2*h/3,swaprect=w/16:h/9:3*w/16:0,swaprect=w/16:h/9:3*w/8:h/9,swaprect=w/16:h/9:3*w/4:5*h/9,swaprect=w/16:h/9:3*w/4:7*h/9,swaprect=w/16:h/9:7*w/8:8*h/9,swaprect=w/16:h/9:7*w/16:2*h/3,swaprect=w/16:h/9:w/4:4*h/9,swaprect=w/16:h/9:3*w/8:0,swaprect=w/16:h/9:3*w/16:h/3,swaprect=w/16:h/9:w/8:7*h/9,swaprect=w/16:h/9:11*w/16:h/9,swaprect=w/16:h/9:5*w/8:2*h/9,swaprect=w/16:h/9:15*w/16:2*h/9,swaprect=w/16:h/9:11*w/16:5*h/9,swaprect=w/16:h/9:w/2:h/9,swaprect=w/16:h/9:5*w/16:2*h/9,swaprect=w/16:h/9:w/8:8*h/9,swaprect=w/16:h/9:7*w/16:4*h/9,swaprect=w/16:h/9:5*w/8:h/9,swaprect=w/16:h/9:9*w/16:2*h/9,swaprect=w/16:h/9:11*w/16:7*h/9,swaprect=w/16:h/9:w/4:0", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "swapRect16x9ver1.png", context).getAbsolutePath()};
        String[] swapRect16x9ver2Command = {"-i", inPath, "-vf", "swaprect=w/16:h/9:w/4:0,swaprect=w/16:h/9:11*w/16:7*h/9,swaprect=w/16:h/9:9*w/16:2*h/9,swaprect=w/16:h/9:5*w/8:h/9,swaprect=w/16:h/9:7*w/16:4*h/9,swaprect=w/16:h/9:w/8:8*h/9,swaprect=w/16:h/9:5*w/16:2*h/9,swaprect=w/16:h/9:w/2:h/9,swaprect=w/16:h/9:11*w/16:5*h/9,swaprect=w/16:h/9:15*w/16:2*h/9,swaprect=w/16:h/9:5*w/8:2*h/9,swaprect=w/16:h/9:11*w/16:h/9,swaprect=w/16:h/9:w/8:7*h/9,swaprect=w/16:h/9:3*w/16:h/3,swaprect=w/16:h/9:3*w/8:0,swaprect=w/16:h/9:w/4:4*h/9,swaprect=w/16:h/9:7*w/16:2*h/3,swaprect=w/16:h/9:7*w/8:8*h/9,swaprect=w/16:h/9:3*w/4:7*h/9,swaprect=w/16:h/9:3*w/4:5*h/9,swaprect=w/16:h/9:3*w/8:h/9,swaprect=w/16:h/9:3*w/16:0,swaprect=w/16:h/9:15*w/16:2*h/3,swaprect=w/16:h/9:13*w/16:7*h/9,swaprect=w/16:h/9:9*w/16:h/9,swaprect=w/16:h/9:0:2*h/3,swaprect=w/16:h/9:15*w/16:h/9,swaprect=w/16:h/9:w/16:h/9,swaprect=w/16:h/9:7*w/16:0,swaprect=w/16:h/9:13*w/16:4*h/9,swaprect=w/16:h/9:5*w/16:4*h/9,swaprect=w/16:h/9:5*w/16:8*h/9,swaprect=w/16:h/9:w/16:4*h/9,swaprect=w/16:h/9:7*w/16:7*h/9,swaprect=w/16:h/9:3*w/4:4*h/9,swaprect=w/16:h/9:7*w/8:2*h/9,swaprect=w/16:h/9:5*w/8:2*h/3,swaprect=w/16:h/9:9*w/16:0,swaprect=w/16:h/9:w/4:8*h/9,swaprect=w/16:h/9:3*w/16:2*h/3,swaprect=w/16:h/9:w/8:5*h/9,swaprect=w/16:h/9:5*w/16:5*h/9,swaprect=w/16:h/9:w/8:h/9,swaprect=w/16:h/9:w/16:0,swaprect=w/16:h/9:5*w/8:8*h/9,swaprect=w/16:h/9:5*w/8:7*h/9,swaprect=w/16:h/9:w/2:7*h/9,swaprect=w/16:h/9:0:0,swaprect=w/16:h/9:w/16:2*h/3,swaprect=w/16:h/9:15*w/16:h/3,swaprect=w/16:h/9:3*w/8:2*h/3,swaprect=w/16:h/9:7*w/16:8*h/9,swaprect=w/16:h/9:3*w/16:2*h/9,swaprect=w/16:h/9:13*w/16:h/3,swaprect=w/16:h/9:w/4:h/3,swaprect=w/16:h/9:15*w/16:8*h/9,swaprect=w/16:h/9:w/2:5*h/9,swaprect=w/16:h/9:11*w/16:0,swaprect=w/16:h/9:13*w/16:2*h/3,swaprect=w/16:h/9:w/8:0,swaprect=w/16:h/9:0:7*h/9,swaprect=w/16:h/9:w/2:4*h/9,swaprect=w/16:h/9:3*w/4:8*h/9,swaprect=w/16:h/9:9*w/16:8*h/9,swaprect=w/16:h/9:7*w/8:h/9,swaprect=w/16:h/9:w/2:2*h/9,swaprect=w/16:h/9:11*w/16:2*h/3,swaprect=w/16:h/9:0:h/9,swaprect=w/16:h/9:15*w/16:0,swaprect=w/16:h/9:15*w/16:5*h/9,swaprect=w/16:h/9:7*w/16:5*h/9,swaprect=w/16:h/9:15*w/16:7*h/9,swaprect=w/16:h/9:7*w/16:h/9,swaprect=w/16:h/9:3*w/4:h/3,swaprect=w/16:h/9:3*w/16:h/9,swaprect=w/16:h/9:7*w/8:0,swaprect=w/16:h/9:13*w/16:5*h/9,swaprect=w/16:h/9:7*w/8:2*h/3,swaprect=w/16:h/9:w/4:h/9,swaprect=w/16:h/9:3*w/16:8*h/9,swaprect=w/16:h/9:w/16:h/3,swaprect=w/16:h/9:w/8:2*h/9,swaprect=w/16:h/9:11*w/16:2*h/9,swaprect=w/16:h/9:5*w/16:0,swaprect=w/16:h/9:3*w/16:5*h/9,swaprect=w/16:h/9:3*w/8:5*h/9,swaprect=w/16:h/9:w/8:h/3,swaprect=w/16:h/9:9*w/16:h/3,swaprect=w/16:h/9:7*w/16:h/3,swaprect=w/16:h/9:3*w/8:h/3,swaprect=w/16:h/9:5*w/8:0,swaprect=w/16:h/9:3*w/4:2*h/9,swaprect=w/16:h/9:0:2*h/9,swaprect=w/16:h/9:w/2:0,swaprect=w/16:h/9:w/8:2*h/3,swaprect=w/16:h/9:w/4:7*h/9,swaprect=w/16:h/9:3*w/8:7*h/9,swaprect=w/16:h/9:9*w/16:7*h/9,swaprect=w/16:h/9:w/16:7*h/9,swaprect=w/16:h/9:0:h/3,swaprect=w/16:h/9:3*w/8:8*h/9,swaprect=w/16:h/9:0:5*h/9,swaprect=w/16:h/9:3*w/16:4*h/9,swaprect=w/16:h/9:7*w/8:4*h/9,swaprect=w/16:h/9:11*w/16:4*h/9,swaprect=w/16:h/9:w/16:2*h/9,swaprect=w/16:h/9:13*w/16:0,swaprect=w/16:h/9:w/2:2*h/3,swaprect=w/16:h/9:7*w/8:5*h/9,swaprect=w/16:h/9:w/4:5*h/9,swaprect=w/16:h/9:3*w/8:4*h/9,swaprect=w/16:h/9:w/4:2*h/3,swaprect=w/16:h/9:5*w/8:4*h/9,swaprect=w/16:h/9:5*w/16:2*h/3,swaprect=w/16:h/9:13*w/16:2*h/9,swaprect=w/16:h/9:5*w/16:7*h/9,swaprect=w/16:h/9:3*w/4:0,swaprect=w/16:h/9:3*w/4:2*h/3,swaprect=w/16:h/9:w/16:8*h/9,swaprect=w/16:h/9:5*w/16:h/3,swaprect=w/16:h/9:9*w/16:2*h/3,swaprect=w/16:h/9:w/8:4*h/9,swaprect=w/16:h/9:w/4:2*h/9,swaprect=w/16:h/9:9*w/16:4*h/9,swaprect=w/16:h/9:13*w/16:h/9,swaprect=w/16:h/9:5*w/16:h/9,swaprect=w/16:h/9:3*w/16:7*h/9,swaprect=w/16:h/9:15*w/16:4*h/9,swaprect=w/16:h/9:7*w/8:7*h/9,swaprect=w/16:h/9:11*w/16:h/3,swaprect=w/16:h/9:0:4*h/9,swaprect=w/16:h/9:11*w/16:8*h/9,swaprect=w/16:h/9:5*w/8:5*h/9,swaprect=w/16:h/9:3*w/8:2*h/9,swaprect=w/16:h/9:0:8*h/9,swaprect=w/16:h/9:w/2:h/3,swaprect=w/16:h/9:9*w/16:5*h/9,swaprect=w/16:h/9:5*w/8:h/3,swaprect=w/16:h/9:3*w/4:h/9,swaprect=w/16:h/9:13*w/16:8*h/9,swaprect=w/16:h/9:7*w/8:h/3,swaprect=w/16:h/9:w/2:8*h/9,swaprect=w/16:h/9:7*w/16:2*h/9,swaprect=w/16:h/9:w/16:5*h/9", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "swapRect16x9ver2.png", context).getAbsolutePath()};

        String umetak = "y=val*5";
        // only can be exported to mkv
        String[] noiseCommand = {"-i", inPath, "-codec:v", "huffyuv", "-bsf:v", "noise=100000000", "-codec:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "noise.png", context).getAbsolutePath()};
        String[] crazyCommand = {"-i", inPath, "-vf", "rotate=PI/6,stereo3d=abl:sbsr,stereo3d=sbsl:aybd,split [main][tmp]; [tmp] crop=iw:ih/2:0:0, vflip [flip]; [main][flip] overlay=0:H/2,crop=iw/2:ih:0:0,split[left][tmp];[tmp]hflip[right];[left][right] hstack", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "crazy.png", context).getAbsolutePath()};
        String[] mirorCommand = {"-i", inPath, "-vf", "split [main][tmp]; [tmp] crop=iw:ih/2:0:0, vflip [flip]; [main][flip] overlay=0:H/2", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "miror.png", context).getAbsolutePath()};
        String[] cartoonCommand = {"-i", inPath, "-vf", "split [main][tmp]; [tmp] lutyuv="+umetak+" [tmp2]; [main][tmp2] overlay", "-pix_fmt","yuv420p","-y","-c:a", "copy", "-frames:v", " 1", createFile(THUMBNAILS_DIR, "cartoon.png", context).getAbsolutePath()};

        commands = new ArrayList();
//        commands.add(new CommandModel(gamaAndSaturationCommand, "gamaAndSaturation.png", createFile(THUMBNAILS_DIR, "gamaAndSaturation.png", context).getAbsolutePath(), "Gamma and Saturation"));
        commands.add(new CommandModel(zoomConstantCommand, "zoomConstant.png", createFile(THUMBNAILS_DIR, "zoomConstant.png", context).getAbsolutePath(), "Zoomed"));
//        String[] boomerangCommand = {"-y" ,"-i",inPath,  "-filter_complex", "[0]reverse[r];[0][r][0]concat=n=3,setpts=0.5*PTS",  outPath};

        commands.add(new CommandModel(boomerangCommand, "boomerang.png",createFile(THUMBNAILS_DIR, "boomerang.png", context).getAbsolutePath(), "Boomerang"));
        commands.add(new CommandModel(blackAndWhiteCommand, "blackAndWhite.png",createFile(THUMBNAILS_DIR, "blackAndWhite.png", context).getAbsolutePath(), "Black And White"));
        commands.add(new CommandModel(sepiaCommand, "sepia.png",createFile(THUMBNAILS_DIR, "sepia.png", context).getAbsolutePath(), "Sepia"));
        commands.add(new CommandModel(gammaCorrenctionCommand, "gammaCorrection.png",createFile(THUMBNAILS_DIR, "gammaCorrection.png", context).getAbsolutePath(), "Gamma Correction"));
        commands.add(new CommandModel(stabilizationCommand, "stabilization.png",createFile(THUMBNAILS_DIR, "stabilization.png", context).getAbsolutePath(), "Stabilization"));
        commands.add(new CommandModel(sharpCommand, "sharp.png",createFile(THUMBNAILS_DIR, "sharp.png", context).getAbsolutePath(), "Sharper"));
        commands.add(new CommandModel(zoomInProgressivelyCommand, "zoomInProgressively.png",createFile(THUMBNAILS_DIR, "zoomInProgressively.png", context).getAbsolutePath(), "Zoom In Progressively"));
        commands.add(new CommandModel(flipVerticalCommand, "flipVertical.png",createFile(THUMBNAILS_DIR, "flipVertical.png", context).getAbsolutePath(), "Flip Vertical"));
        commands.add(new CommandModel(flipHorizontalCommand, "flipHorizontal.png",createFile(THUMBNAILS_DIR, "flipHorizontal.png", context).getAbsolutePath(), "Flip Horizontal"));
        commands.add(new CommandModel(rotate90DegreesClockwiseCommand, "rotate90DegreesClockwise.png",createFile(THUMBNAILS_DIR, "rotate90DegreesClockwise.png", context).getAbsolutePath(), "Rotate 90 Degr Clockwise"));
        commands.add(new CommandModel(rotate90DegreesCounterclockwiseCommand, "rotate90DegreesCounterclockwise.png",createFile(THUMBNAILS_DIR, "rotate90DegreesCounterclockwise.png", context).getAbsolutePath(), "Rotate 90 Degr Counterclockwise"));
        commands.add(new CommandModel(blurCommand, "blur.png",createFile(THUMBNAILS_DIR, "blur.png", context).getAbsolutePath(), "Blur"));
        commands.add(new CommandModel(convolutionCommand, "convolution.png",createFile(THUMBNAILS_DIR, "convolution.png", context).getAbsolutePath(), "Convolution"));
        commands.add(new CommandModel(convolution2Command, "convolution2.png",createFile(THUMBNAILS_DIR, "convolution2.png", context).getAbsolutePath(), "Convolution 2"));
        commands.add(new CommandModel(stereoscopicCommand, "stereoscopic.png",createFile(THUMBNAILS_DIR, "stereoscopic.png", context).getAbsolutePath(), "Stereoscopic"));
        commands.add(new CommandModel(pixelizeCommand, "pixelize.png",createFile(THUMBNAILS_DIR, "pixelize.png", context).getAbsolutePath(), "Pixelize"));
        commands.add(new CommandModel(brightnessPlusCommand, "brightnessPlus.png",createFile(THUMBNAILS_DIR, "brightnessPlus.png", context).getAbsolutePath(), "Brightness Plus"));
        commands.add(new CommandModel(brightnessMinusCommand, "brightnessMinus.png",createFile(THUMBNAILS_DIR, "brightnessMinus.png", context).getAbsolutePath(), "Brightness Minus"));
        commands.add(new CommandModel(saturationPlusCommand, "saturationPlus.png",createFile(THUMBNAILS_DIR, "saturationPlus.png", context).getAbsolutePath(), "Saturation Plus"));
        commands.add(new CommandModel(saturationMinusCommand, "saturationMinus.png",createFile(THUMBNAILS_DIR, "saturationMinus.png", context).getAbsolutePath(), "Saturation Minus"));
        commands.add(new CommandModel(gammaRPlusCommand, "gammaRPlus.png",createFile(THUMBNAILS_DIR, "gammaRPlus.png", context).getAbsolutePath(), "Gamma R Plus"));
        commands.add(new CommandModel(gammaRMinusCommand, "gammaRMinus.png",createFile(THUMBNAILS_DIR, "gammaRMinus.png", context).getAbsolutePath(), "Gamma R Minus"));
        commands.add(new CommandModel(gammaGPlusCommand, "gammaGPlus.png",createFile(THUMBNAILS_DIR, "gammaGPlus.png", context).getAbsolutePath(), "Gamma G Plus"));
        commands.add(new CommandModel(gammaGMinusCommand, "gammaGMinus.png",createFile(THUMBNAILS_DIR, "gammaGMinus.png", context).getAbsolutePath(), "Gamma G Minus"));
        commands.add(new CommandModel(gammaBPlusCommand, "gammaBPlus.png",createFile(THUMBNAILS_DIR, "gammaBPlus.png", context).getAbsolutePath(), "Gamma B Plus"));
        commands.add(new CommandModel(gammaBMinusCommand, "gammaBMinus.png",createFile(THUMBNAILS_DIR, "gammaBMinus.png", context).getAbsolutePath(), "Gamma B Minus"));
        commands.add(new CommandModel(colorbalanceCommand, "colorbalance.png",createFile(THUMBNAILS_DIR, "colorbalance.png", context).getAbsolutePath(), "Color Balance"));
        commands.add(new CommandModel(colorbalance2Command, "colorbalance2.png",createFile(THUMBNAILS_DIR, "colorbalance2.png", context).getAbsolutePath(), "Color Balance Two"));
        commands.add(new CommandModel(colorNegativeCommand, "colorNegative.png",createFile(THUMBNAILS_DIR, "colorNegative.png", context).getAbsolutePath(), "Color Negative"));
        commands.add(new CommandModel(vintageCommand, "vintage.png",createFile(THUMBNAILS_DIR, "vintage.png", context).getAbsolutePath(), "Vintage"));
        commands.add(new CommandModel(crossProcessCommand, "crossProcess.png",createFile(THUMBNAILS_DIR, "crossProcess.png", context).getAbsolutePath(), "Cross Process"));
        commands.add(new CommandModel(darkerCommand, "darker.png",createFile(THUMBNAILS_DIR, "darker.png", context).getAbsolutePath(), "Darker"));
        commands.add(new CommandModel(lighterCommand, "lighter.png",createFile(THUMBNAILS_DIR, "lighter.png", context).getAbsolutePath(), "Lighter"));
        commands.add(new CommandModel(increaseContrastCommand, "increaseContrast.png",createFile(THUMBNAILS_DIR, "increaseContrast.png", context).getAbsolutePath(), "Increase Contrast"));
        commands.add(new CommandModel(linearContrastCommand, "linearContrast.png",createFile(THUMBNAILS_DIR, "linearContrast.png", context).getAbsolutePath(), "Linear Contrast"));
        commands.add(new CommandModel(mediumContrastCommand, "mediumContrast.png",createFile(THUMBNAILS_DIR, "mediumContrast.png", context).getAbsolutePath(), "Medium Contrast"));
        commands.add(new CommandModel(strongContrastCommand, "strongContrast.png",createFile(THUMBNAILS_DIR, "strongContrast.png", context).getAbsolutePath(), "Strong Contrast"));
        commands.add(new CommandModel(negativeCommand, "negative.png",createFile(THUMBNAILS_DIR, "negative.png", context).getAbsolutePath(), "Negative"));
        commands.add(new CommandModel(curvesBlueCommand, "curvesBlue.png",createFile(THUMBNAILS_DIR, "curvesBlue.png", context).getAbsolutePath(), "Curves Blue"));

        commands.add(new CommandModel(elbg2Command, "elbg2.png",createFile(THUMBNAILS_DIR, "elbg2.png", context).getAbsolutePath(), "Enhanced LBG 2"));
        commands.add(new CommandModel(elbg4Command, "elbg4.png",createFile(THUMBNAILS_DIR, "elbg4.png", context).getAbsolutePath(), "Enhanced LBG 4"));
        commands.add(new CommandModel(elbg8Command, "elbg8.png",createFile(THUMBNAILS_DIR, "elbg8.png", context).getAbsolutePath(), "Enhanced LBG 8"));
        commands.add(new CommandModel(elbg16Command, "elbg16.png",createFile(THUMBNAILS_DIR, "elbg16.png", context).getAbsolutePath(), "Enhanced LBG 16"));

        commands.add(new CommandModel(debandCommand, "deband.png",createFile(THUMBNAILS_DIR, "deband.png", context).getAbsolutePath(), "Deband"));
        commands.add(new CommandModel(swapRect6x6ver1Command, "swapRect6x6ver1.png",createFile(THUMBNAILS_DIR, "swapRect6x6ver1.png", context).getAbsolutePath(), "Swap Rect One"));
        commands.add(new CommandModel(swapRect6x6ver2Command, "swapRect6x6ver2.png",createFile(THUMBNAILS_DIR, "swapRect6x6ver2.png", context).getAbsolutePath(), "Swap Rect Two"));
        commands.add(new CommandModel(swapRect10x6ver1Command, "swapRect10x6ver1.png",createFile(THUMBNAILS_DIR, "swapRect10x6ver1.png", context).getAbsolutePath(), "Swap Rect Three"));
        commands.add(new CommandModel(swapRect10x6ver2Command, "swapRect10x6ver2.png",createFile(THUMBNAILS_DIR, "swapRect10x6ver2.png", context).getAbsolutePath(), "Swap Rect Four"));
        commands.add(new CommandModel(swapRect16x9ver1Command, "swapRect16x9ver1.png",createFile(THUMBNAILS_DIR, "swapRect16x9ver1.png", context).getAbsolutePath(), "Swap Rect Five"));
        commands.add(new CommandModel(swapRect16x9ver2Command, "swapRect16x9ver2.png",createFile(THUMBNAILS_DIR, "swapRect16x9ver2.png", context).getAbsolutePath(), "Swap Rect Six"));

//        commands.add(new CommandModel(noiseCommand, "noise.png",createFile(THUMBNAILS_DIR, "noise.png", context).getAbsolutePath(), "Noise"));
        commands.add(new CommandModel(crazyCommand, "crazy.png",createFile(THUMBNAILS_DIR, "crazy.png", context).getAbsolutePath(), "Crazy"));
        commands.add(new CommandModel(mirorCommand, "miror.png",createFile(THUMBNAILS_DIR, "miror.png", context).getAbsolutePath(), "Mirror"));
        commands.add(new CommandModel(cartoonCommand, "cartoon.png",createFile(THUMBNAILS_DIR, "cartoon.png", context).getAbsolutePath(), "Cartoon"));

        CommandModel commandModel = (CommandModel) CommandsUtil.commands.get(18);
        commands.add(new CommandModel(fadeInCommand, "fadeIn.png",commandModel.getPreviewImagePath(), "Fade in"));

        CommandModel commandModel2 = (CommandModel) CommandsUtil.commands.get(19);
        commands.add(new CommandModel(fadeoutCommand, "fadeOut.png",commandModel2.getPreviewImagePath(), "Fade out"));

    }

    private static File createFile(String directoryName, String fileName, Context context) {
        ContextWrapper cw = new ContextWrapper(context.getApplicationContext());

        File directory = cw.getDir(directoryName, Context.MODE_PRIVATE);

        if (!directory.exists() && !directory.mkdirs()) {
            Log.e("ImageSaver", "Error creating directory " + directory);
        }

        return new File(directory, fileName);
    }

}
