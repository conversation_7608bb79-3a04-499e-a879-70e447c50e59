package com.videomontage.freeeditingapps.utils.videoclipper

import android.content.Context
import android.graphics.Bitmap
import android.os.Environment
import android.util.Log
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.utils.VideoLayoutSize
import com.videomontage.freeeditingapps.view.BaseImageView
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.util.ArrayList

class VideoCreator(
        private val context: Context, private val isLayoutRotated: Boolean
) : FFmpegCreator(context) {

    private var videoLayoutSize = VideoLayoutSize(isLayoutRotated)

    public fun debugMethod(
//            isLayoutRotated: Boolean,
            inputVideoPath: String,
            mViews: ArrayList<BaseImageView>,
            gifsPath: Map<Int, String>,
            videoWidth: Int,
            videoHeight: Int,
            callback: () -> Unit
    ) {


        scanGallery(true)

        videoLayoutSize = VideoLayoutSize(isLayoutRotated)

        val sbDestinationCommand = StringBuilder()
        val sbFilterComplexCommand: StringBuilder
        val sbInputCommand = StringBuilder()
        sbInputCommand.append("-y ")
        sbInputCommand.append("-i ")
        sbInputCommand.append(inputVideoPath)
        for (i in mViews.indices) {
            sbInputCommand.append(" -i ")
            var overlayPath = if (mViews[i].isGif) gifsPath.get(i) else getOverlayPath(i, mViews[i].isGif, mViews, videoWidth, videoHeight)
            sbInputCommand.append(overlayPath)
        }
        sbFilterComplexCommand = getFilterComplexCommand(mViews, videoWidth, videoHeight)

//        if (true)return;
        sbDestinationCommand.append("\" -c:v")
        sbDestinationCommand.append(" libx264")
        sbDestinationCommand.append(" -preset")
        sbDestinationCommand.append(" ultrafast ")
        val fileName = System.currentTimeMillis().toString() + ".mp4"
        val output = File(Environment.getExternalStoragePublicDirectory(
                Environment.DIRECTORY_MOVIES).toString()
                + File.separator + "Montage" + File.separator
                + fileName)
        try {
            output.createNewFile()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        sbDestinationCommand.append(output.absolutePath)
        execFFmpegBinary(sbInputCommand.toString() + sbFilterComplexCommand.toString() + sbDestinationCommand.toString(), output.absolutePath, fileName) {

            callback.invoke()
        }
    }

    private fun getFilterComplexCommand(mViews: ArrayList<BaseImageView>,
                                        videoWidth: Int,
                                        videoHeight: Int
    ): StringBuilder {
        val sb = StringBuilder()
        sb.append(" -filter_complex \"")
        for (i in mViews.indices) {
            val baseImageView = mViews[i]
            val rotated = baseImageView.rotateDegree.toDouble() != 0.0
            val inputPos = i + 1
            val filterSeparator = if (inputPos < mViews.size) "[vid" + (inputPos + 1) + "];" else ""
            println("Rotated: $rotated")

            //koristiti ovo kad je gif u pitanju sto se skejlovanja tice
            val iw = (baseImageView.viewWidth / videoLayoutSize.getWidth() * videoWidth).toInt()
            val ih = (baseImageView.viewHeight / videoLayoutSize.getHeight() * videoHeight).toInt()
            if (rotated) {
                val angle = baseImageView.angle * -1
                val main = if (i == 0) "[0:v]" else "[vid$inputPos]"
                val scale = if (baseImageView.isGif) "scale=$iw:$ih" else "scale=iw:ih"
                sb.append( //                        "[" + inputPos + ":v] scale=iw:ih [scaled" + inputPos + "];" +
                        "[" + inputPos + ":v]" + scale + "[scaled" + inputPos + "];" +
                                "[scaled" + inputPos + "] rotate=" + angle + "*PI/180:ow=rotw(" + angle + "*PI/180):oh=roth(" + angle + "*PI/180):c=0x00000000 [rotated" + inputPos + "];[rotated" + inputPos + "] format=yuva420p,colorchannelmixer=aa=1.0 [mix" + inputPos + "];" +
                                main + "[mix" + inputPos + "]"
                                + "overlay="
                                + baseImageView.leftBottomX / videoLayoutSize.getWidth() * videoWidth + "+" + baseImageView.viewHeight + "*sin(PI/180)"
                                + ":" + baseImageView.lowestY / videoLayoutSize.getHeight() * videoHeight
                                + ":enable='between(t," + baseImageView.startTime / 1000 + "," + baseImageView.endTime / 1000 + ")'" + filterSeparator)
            } else {
                Log.d("ddd", "getFilterComplexCommand: baseImageView.leftBottomX " + baseImageView.leftBottomX)
                Log.d("ddd", "getFilterComplexCommand: baseImageView.lowestY " + baseImageView.lowestY)
                Log.d("ddd", "getFilterComplexCommand: videoWidth " + videoWidth)
                Log.d("ddd", "getFilterComplexCommand: videoHeight " + videoHeight)
                if (i == 0) sb.append("[0:v]") else sb.append("[vid$inputPos]")
                sb.append(
                        ("[" + inputPos + ":v]"
                                + "overlay="
                                + ((baseImageView.leftBottomX / videoLayoutSize.getWidth() * videoWidth))
                                + ":" + (baseImageView.lowestY / videoLayoutSize.getHeight() * videoHeight)
                                + ":enable='between(t," + (baseImageView.startTime / 1000) + "," + (baseImageView.endTime / 1000) + ")'" + filterSeparator))
            }
        }
        return sb
    }

    private fun getOverlayPath(index: Int, isGif: Boolean,
                               mViews: ArrayList<BaseImageView>,
                               videoWidth: Int,
                               videoHeight: Int): String? {
        val file: File
        if (isGif) {
            val inputStream = context!!.resources.openRawResource(R.raw.dengliao)

//            String extStorageDirectory = Environment.getExternalStorageDirectory().toString();
            val cacheDir = File(context!!.cacheDir, "temp_images")
            if (!cacheDir.exists()) cacheDir.mkdir()
            file = File(cacheDir, System.currentTimeMillis().toString() + "_temp.gif")
//            createGif(file, index)

            try {
                copyInputStreamToFile(inputStream, file)
            } catch (e: IOException) {
                e.printStackTrace()
            }
        } else {
            val baseImageView = mViews[index]
            val rotated = baseImageView.angle.toDouble() != 0.0
            Log.d("ddd", "getOverlayPath: baseImageView.viewWidth ${baseImageView.viewWidth}")
            Log.d("ddd", "getOverlayPath: videoWidth $videoWidth")
            Log.d("ddd", "getOverlayPath: videoHeight $videoHeight")
            Log.d("ddd", "getOverlayPath: baseImageView.viewWidth / 630 * videoWidth ${baseImageView.viewWidth / videoLayoutSize.getWidth() * videoWidth}")
            Log.d("ddd", "getOverlayPath: baseImageView.viewHeight / 1120 * videoHeight ${baseImageView.viewHeight / videoLayoutSize.getHeight() * videoHeight}")
            val bm = Bitmap.createScaledBitmap(
                    if (rotated) baseImageView.getmBitmap() else baseImageView.bitmap,
                    (baseImageView.viewWidth / videoLayoutSize.getWidth() * videoWidth).toInt(), (baseImageView.viewHeight / videoLayoutSize.getHeight() * videoHeight).toInt(), true)

//            String extStorageDirectory = Environment.getExternalStorageDirectory().toString();
            val cacheDir = File(context!!.cacheDir, "temp_images")
            if (!cacheDir.exists()) cacheDir.mkdir()
            file = File(cacheDir, System.currentTimeMillis().toString() + "_temp.png")
            if (!file.exists()) {
                try {
                    val outStream = FileOutputStream(file)
                    bm.compress(Bitmap.CompressFormat.PNG, 100, outStream)
                    outStream.flush()
                    outStream.close()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
        //
//        return file.getAbsolutePath();
        return file.absolutePath
    }

    @Throws(IOException::class)
    private fun copyInputStreamToFile(inputStream: InputStream, file: File) {

        // append = false
        FileOutputStream(file, false).use { outputStream ->
            var read: Int
            val bytes: ByteArray = ByteArray(8192)
            while ((inputStream.read(bytes).also { read = it }) != -1) {
                outputStream.write(bytes, 0, read)
            }
        }
    }
}