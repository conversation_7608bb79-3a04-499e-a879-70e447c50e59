package com.videomontage.freeeditingapps.fragment;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.google.android.material.tabs.TabLayout;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import androidx.appcompat.widget.SearchView;

import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;

import com.videomontage.freeeditingapps.R;
import com.videomontage.freeeditingapps.adapter.StudioAdapter;
import com.videomontage.freeeditingapps.analytics.AmplitudeHelper;
import com.videomontage.freeeditingapps.analytics.FragmentAnalytics;
import com.videomontage.freeeditingapps.analytics.TimeWatch;
import com.videomontage.freeeditingapps.application.MyApplication;
import com.videomontage.freeeditingapps.statistic.Statistic;
import com.videomontage.freeeditingapps.utils.Flog;
import com.videomontage.freeeditingapps.utils.SharedPrefs;
import com.videomontage.freeeditingapps.utils.Utils;

import java.util.concurrent.TimeUnit;

import static com.videomontage.freeeditingapps.statistic.Statistic.INDEX_ADD_MUSIC;
import static com.videomontage.freeeditingapps.statistic.Statistic.INDEX_CUTTER;
import static com.videomontage.freeeditingapps.statistic.Statistic.INDEX_EFFECTS;
import static com.videomontage.freeeditingapps.statistic.Statistic.INDEX_MERGER;
import static com.videomontage.freeeditingapps.statistic.Statistic.INDEX_SPEED;
import static com.videomontage.freeeditingapps.utils.SortOrder.ID_SONG_A_Z;
import static com.videomontage.freeeditingapps.utils.SortOrder.ID_SONG_DATE_ADDED;
import static com.videomontage.freeeditingapps.utils.SortOrder.ID_SONG_DATE_ADDED_DESCENDING;
import static com.videomontage.freeeditingapps.utils.SortOrder.ID_SONG_Z_A;

public class StudioFragment extends AbsFragment {
    public static final int CUTTER = 0;
    public static final int SPEED = 1;
    public static final int MERGER = 2;
    public static final int ADD_MUSIC = 3;
    public static final int EFFECTS = 4;
    private StudioAdapter studioAdapter;
    private ViewPager viewPager;
    private SearchView searchView;
    private int CHECK_STATE_ADD = 0;
    private int OPEN_FRAGMENT = 0;
    private StudioFragmentDetail studioFragmentDetail;
    private MenuItem listMenu[];

    private TimeWatch watch;
    private FragmentAnalytics fragmentAnalytics;

    private void addTabFragment() {

        OPEN_FRAGMENT = getArguments().getInt(Statistic.OPEN_FRAGMENT, 0);

        studioAdapter = new StudioAdapter(getChildFragmentManager());

        viewPager = (ViewPager) findViewById(R.id.viewPager);
        viewPager.setAdapter(studioAdapter);

        TabLayout tabLayout = (TabLayout) findViewById(R.id.tabLayout);
        tabLayout.setupWithViewPager(viewPager);
        tabLayout.getTabAt(CUTTER).setText(getString(R.string.cutter));
        tabLayout.getTabAt(SPEED).setText(getString(R.string.speed));
        tabLayout.getTabAt(MERGER).setText(getString(R.string.merger));
        tabLayout.getTabAt(ADD_MUSIC).setText(getString(R.string.add_music));
        tabLayout.getTabAt(EFFECTS).setText(getString(R.string.add_effects));

        fragmentAnalytics.addClick("Cutter");


        tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                fragmentAnalytics.addClick(tab.getText().toString());

                getContext().sendBroadcast(new Intent(Statistic.CLEAR_ACTION_MODE));
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {

            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });
        Flog.e(" open nnnnn " + OPEN_FRAGMENT);
        if (OPEN_FRAGMENT == INDEX_CUTTER) {
            viewPager.setCurrentItem(INDEX_CUTTER);

        } else if (OPEN_FRAGMENT == INDEX_SPEED) {
            viewPager.setCurrentItem(INDEX_SPEED);

        } else if (OPEN_FRAGMENT == INDEX_ADD_MUSIC) {
            viewPager.setCurrentItem(INDEX_ADD_MUSIC);

        } else if (OPEN_FRAGMENT == INDEX_EFFECTS) {
            viewPager.setCurrentItem(INDEX_EFFECTS);

        } else {
            viewPager.setCurrentItem(INDEX_MERGER);
        }

//        viewPager.setOffscreenPageLimit(4);
        viewPager.setOffscreenPageLimit(3);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_studio, container, false);
    }

    @Override
    public void initViews() {
        watch = TimeWatch.start();
        fragmentAnalytics = new FragmentAnalytics("StudioFragment");
        AmplitudeHelper.setStudioOpened();

        addTabFragment();
    }

    @Override
    public void initToolbar() {
        super.initToolbar();
        getToolbar().getMenu().clear();
        getToolbar().inflateMenu(R.menu.menu_search1);
        getToolbar().setNavigationOnClickListener(view -> {
            getActivity().onBackPressed();
            Utils.closeKeyboard(getActivity());
        });
        searchAudio();
        setUpSortOrderMenu();
    }

    private void setUpSortOrderMenu() {
        int currentSortOrder = SharedPrefs.getInstance().get(Statistic.SORT_ORDER_CURRENT, Integer.class, ID_SONG_DATE_ADDED_DESCENDING);

        getToolbar().getMenu().setGroupCheckable(0, true, true);

        MenuItem menuItemAZ, menuItemZA, menuItemDateASC, menuItemDateDESC;
        menuItemAZ = getToolbar().getMenu().findItem(R.id.item_a_z);
        menuItemDateASC = getToolbar().getMenu().findItem(R.id.item_date_ascending);
        menuItemDateDESC = getToolbar().getMenu().findItem(R.id.item_date_descending);
        menuItemZA = getToolbar().getMenu().findItem(R.id.item_z_a);

        listMenu = new MenuItem[]{menuItemAZ, menuItemZA, menuItemDateASC, menuItemDateDESC};
        listMenu[currentSortOrder].setChecked(true);

        menuItemAZ.setOnMenuItemClickListener(menuItem -> saveIdSortOrder(ID_SONG_A_Z, menuItem));
        menuItemZA.setOnMenuItemClickListener(menuItem -> saveIdSortOrder(ID_SONG_Z_A, menuItem));
        menuItemDateASC.setOnMenuItemClickListener(menuItem -> saveIdSortOrder(ID_SONG_DATE_ADDED, menuItem));
        menuItemDateDESC.setOnMenuItemClickListener(menuItem -> saveIdSortOrder(ID_SONG_DATE_ADDED_DESCENDING, menuItem));
    }

    private boolean saveIdSortOrder(int id, @NonNull MenuItem menuItem) {
        Flog.e("xxxxx       clicccccccccccc" + id);
        menuItem.setChecked(true);
        SharedPrefs.getInstance().put(Statistic.SORT_ORDER_CURRENT, id);
        getContext().sendBroadcast(new Intent(Statistic.UPDATE_CHOOSE_SORT_ORDER).putExtra(Statistic.SORT_ORDER_CURRENT, id));
        return true;
    }

    private void searchAudio() {
        MenuItem menuItem = getToolbar().getMenu().findItem(R.id.item_search);
        searchView = (SearchView) menuItem.getActionView();
        searchView.setMaxWidth(Integer.MAX_VALUE);
        searchView.setOnQueryTextListener(new androidx.appcompat.widget.SearchView.OnQueryTextListener() {
            @Override
            public boolean onQueryTextSubmit(String s) {
                //actionSearch(s);
                return true;
            }

            @Override
            public boolean onQueryTextChange(String s) {

                PagerAdapter pagerAdapter = viewPager.getAdapter();

                for (int i = 0; i < pagerAdapter.getCount(); i++) {

                    Fragment viewPagerFragment = (Fragment) viewPager.getAdapter().instantiateItem(viewPager, i);

                    if (viewPagerFragment.isAdded()) {

                        if (viewPagerFragment instanceof StudioFragmentDetail) {

                            studioFragmentDetail = (StudioFragmentDetail) viewPagerFragment;

                            if (studioFragmentDetail != null) {
                                studioFragmentDetail.beginSearch(s);
                            }
                        }
                    }
                }

                return true;
            }
        });
    }

    public static StudioFragment newInstance(Bundle bundle) {
        StudioFragment fragment = new StudioFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        fragmentAnalytics.setDuration(watch.time(TimeUnit.SECONDS));
        MyApplication.addEvent(fragmentAnalytics);

    }
}