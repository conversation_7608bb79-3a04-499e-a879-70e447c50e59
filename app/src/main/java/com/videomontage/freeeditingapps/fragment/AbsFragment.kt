package com.videomontage.freeeditingapps.fragment

import android.content.Context
import android.os.Bundle
import android.view.View
import androidx.annotation.IdRes
import androidx.appcompat.widget.Toolbar
import androidx.fragment.app.Fragment
//import com.google.android.gms.ads.AdError
//import com.google.android.gms.ads.AdRequest
//import com.google.android.gms.ads.FullScreenContentCallback
//import com.google.android.gms.ads.MobileAds
//import com.google.android.gms.ads.interstitial.InterstitialAd
//import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback
import com.videomontage.freeeditingapps.BuildConfig
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.activity.MainActivity
import com.videomontage.freeeditingapps.utils.Utils

abstract class AbsFragment : Fragment() {
    private var mContext: Context? = null
    open var toolbar: Toolbar? = null
    var isPauseFragment = false
        private set
//    private var mInterstitialAd: InterstitialAd? = null
    override fun onAttach(context: Context) {
        super.onAttach(context)
        mContext = context
    }

    override fun onPause() {
        super.onPause()
        isPauseFragment = true
    }

    override fun onResume() {
        super.onResume()
        isPauseFragment = false
    }

    override fun getContext(): Context? {
        return if (mContext != null) mContext else super.getContext()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initToolbar()
        initViews()
//        loadAds()
    }

    open fun initToolbar() {
        toolbar = findViewById(R.id.toolbar) as Toolbar?
        if (toolbar != null) {
            toolbar!!.setNavigationIcon(R.drawable.ic_back)
            toolbar!!.setNavigationOnClickListener { view: View? -> onBack() }
            toolbar!!.inflateMenu(R.menu.menu_save)
        }
    }

    private fun onBack() {
//        InputMethodManager imm = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
//        if (imm != null && imm.is()) {
//            Utils.closeKeyboard(getActivity());
//        }else {
        requireFragmentManager().popBackStack()
        //        }
        Utils.closeKeyboard(activity)
    }

    fun findViewById(@IdRes id: Int): View? {
        return requireView().findViewById(id)
    }

//    fun showInterstitial() {
//        if (mInterstitialAd != null) {
//            mInterstitialAd?.show(requireActivity())
//        }
//    }
//
//    private fun loadAds() {
//        context?.let {ctx ->
//            MobileAds.initialize(ctx) {
//                val adRequest = AdRequest.Builder().build()
//                InterstitialAd.load(
//                    ctx,
//                    if (MainActivity.TEST) "ca-app-pub-3940256099942544/1033173712"
//                    else "ca-app-pub-2572150356682336/6250279701",
//                    adRequest,
//                    object : InterstitialAdLoadCallback() {
//                        override fun onAdLoaded(interstitialAd: InterstitialAd) {
//                            mInterstitialAd = interstitialAd
//                            mInterstitialAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
//                                override fun onAdDismissedFullScreenContent() {
//                                    mInterstitialAd = null
//                                    loadAds()
//                                    afterInterstitial()
//                                }
//
//                                override fun onAdFailedToShowFullScreenContent(adError: AdError) {
//                                    mInterstitialAd = null
//                                    loadAds()
//                                }
//
//                                override fun onAdShowedFullScreenContent() {
//                                    // Called when ad is shown.
//                                }
//                            }
//                        }
//                    }
//                )
//            }
//        }
//    }

    abstract fun initViews()
    open fun afterInterstitial() {}

    companion object {
        var IS_TEST = BuildConfig.DEBUG
    }
}