package com.videomontage.freeeditingapps.fragment;

import android.graphics.Bitmap;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

//import com.my.dropbox_lib.data.local.database.model.StickerCache;
import com.videomontage.freeeditingapps.R;
//import com.videomontage.freeeditingapps.adapter.StickersAdapter;

import java.util.List;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * Created by Olakunmi on 21/01/2017.
 */

public class CustomFragment extends Fragment {
    private String mText = "";
//    private StickersAdapter stickersAdapter = new StickersAdapter();
    private CallBackListener callBackListener;


//    public static CustomFragment createInstance(String txt, List<StickerCache> stickerList) {
//        CustomFragment fragment = new CustomFragment();
//        fragment.mText = txt;
//        fragment.stickersAdapter.getDiffer().submitList(stickerList);
//        fragment.stickersAdapter.setOnItemClickListener(fragment::handleClick);
//        return fragment;
//    }

    public  Boolean handleClick(Bitmap sticker){
        if(callBackListener != null)
            callBackListener.onCallBack(sticker);
//        Toast.makeText(getActivity(), sticker.getUrl(), Toast.LENGTH_LONG).show();
        return true;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View v = inflater.inflate(R.layout.fragment_sample, container, false);
        if (getActivity() instanceof CallBackListener)
            callBackListener = (CallBackListener) getActivity();

        setupRecyclerView(v);
        return v;
    }

    private void setupRecyclerView(View view) {
        RecyclerView stickersRv = view.findViewById(R.id.stickersRv);
        stickersRv.setLayoutManager(new GridLayoutManager(getContext(), 4));
//        stickersRv.setAdapter(stickersAdapter);
    }



    public interface CallBackListener {
        void onCallBack(Bitmap bitmap);
    }
}