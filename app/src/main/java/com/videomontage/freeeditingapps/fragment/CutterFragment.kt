package com.videomontage.freeeditingapps.fragment

import android.animation.ValueAnimator
import android.app.ProgressDialog
import android.content.DialogInterface
import android.content.Intent
import android.media.MediaPlayer
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.Toast
import com.arthenica.mobileffmpeg.Config
import com.arthenica.mobileffmpeg.FFmpeg
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.analytics.AmplitudeHelper
import com.videomontage.freeeditingapps.analytics.FragmentAnalytics
import com.videomontage.freeeditingapps.analytics.TimeWatch
import com.videomontage.freeeditingapps.application.MyApplication
import com.videomontage.freeeditingapps.listener.IInputNameFile
import com.videomontage.freeeditingapps.model.ComModel
import com.videomontage.freeeditingapps.model.VideoModel
import com.videomontage.freeeditingapps.statistic.Statistic
import com.videomontage.freeeditingapps.utils.FileUtil
import com.videomontage.freeeditingapps.utils.Flog
import com.videomontage.freeeditingapps.utils.Utils
import com.videomontage.freeeditingapps.video.MyVideoView_Old
import com.videomontage.freeeditingapps.video.MyVideoView_Old.MediaListener
import com.videomontage.freeeditingapps.video.VideoControllerView
import com.videomontage.freeeditingapps.video.VideoControllerView.ICallBackComplete
import com.videomontage.freeeditingapps.video.VideoTimelineView
import java.io.File
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.concurrent.TimeUnit


//import com.github.hiteshsondhi88.libffmpeg.ExecuteBinaryResponseHandler;
//import com.github.hiteshsondhi88.libffmpeg.FFmpeg;
//import com.github.hiteshsondhi88.libffmpeg.exceptions.FFmpegCommandAlreadyRunningException;
//import com.simplestorage.media.MediaFile;
//import com.simplestorage.media.MediaStoreCompat;
//import com.simplestorage.media.MediaType;
/**
 * Created by Hung on 11/15/2018.
 */
class CutterFragment : AbsFragment(), IInputNameFile, ICallBackComplete {
    private var videoView: MyVideoView_Old? = null
    private var videoTimelineView: VideoTimelineView? = null
    private var videoControllerView: VideoControllerView? = null
    private var pathOldFile: String? = null
    private var pathNewFile: String? = null

    //    private FFmpeg ffmpeg;
    //    com.github.kohiyadav.libffmpeg.FFmpeg ffMpegTest;
    private val simpleDateFormat = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.US)
    private var videoModel: VideoModel? = null
    private var progressDialog: ProgressDialog? = null
    private val isSuccessCut = false
    private var handler: Handler? = null
    private var animator: ValueAnimator? = null
    private var positionIcon: ImageView? = null
    private var ffmpegDone = false
    private var watch: TimeWatch? = null
    private var fragmentAnalytics: FragmentAnalytics? = null
    private var mp: MediaPlayer? = null
    override fun initViews() {
        watch = TimeWatch.start()
        fragmentAnalytics = FragmentAnalytics("CutterFragment")
        AmplitudeHelper.setCutterOpened()
        positionIcon = findViewById(R.id.positionIcon) as ImageView?
        videoModel = requireArguments().getParcelable(Statistic.VIDEO_MODEL)
        pathOldFile = requireArguments().getString(Statistic.PATH_VIDEO)
        if (pathOldFile == null) {
            requireFragmentManager().popBackStack()
            return
        }

//        ffmpeg = FFmpeg.getInstance(getContext());
        videoTimelineView = findViewById(R.id.video_timeline) as VideoTimelineView?
        videoView = findViewById(R.id.video_view) as MyVideoView_Old?
        videoView!!.setDependentView(findViewById(R.id.foreground_video))
        videoControllerView = findViewById(R.id.foreground_video) as VideoControllerView?
        videoControllerView!!.setListener(this)
        videoControllerView!!.setViewVideoView(videoView)

//        MediaFile mediaStoreCompat1 = MediaStoreCompat.INSTANCE.fromMediaId(getContext(), MediaType.VIDEO, videoModel.getId());
//        Log.d("dddd", "initViews: " + mediaStoreCompat1.getAbsolutePath());
        initVideoView()
        initVideoTimeline()
        toolbar!!.title = getString(R.string.cutter)
        toolbar!!.menu.findItem(R.id.item_save)
            .setOnMenuItemClickListener { menuItem: MenuItem? -> dialogSelectLocalSaveFile() }
    }

    private fun save(fileName: String) {
        isCancelCut = false
        var startTime = 0
        var endTime = 0
        var durationAudio = 0
        pathNewFile = (Environment.getExternalStoragePublicDirectory(
            Environment.DIRECTORY_DCIM
        ).toString()
                + File.separator + "Montage"
                + Statistic.DIR_CUTTER + "/")
        //        pathNewFile = Environment.getExternalStorageDirectory().getAbsolutePath() + Statistic.DIR_APP + Statistic.DIR_CUTTER + "/";
        if (!File(pathNewFile).exists()) {
            File(pathNewFile).mkdirs()
        }
        pathNewFile = pathNewFile + fileName + Utils.getFileExtension(
            videoModel!!.path
        )
        val f = File(pathNewFile)
        if (f.exists()) {
            Toast.makeText(context, getString(R.string.name_file_exist), Toast.LENGTH_SHORT).show()
            return
        }
        startTime =
            Math.round(videoTimelineView!!.leftProgress * videoTimelineView!!.videoLength / 1000)
        endTime =
            Math.round(videoTimelineView!!.rightProgress * videoTimelineView!!.videoLength / 1000)
        durationAudio = endTime - startTime
        if (durationAudio < 1) {
            Toast.makeText(context, getString(R.string.time_fail), Toast.LENGTH_SHORT).show()
            return
        }
        val command = arrayOf(
            "-i",
            videoModel!!.path,
            "-ss",
            startTime.toString() + "",
            "-t",
            durationAudio.toString(),
            "-c",
            "copy",
            pathNewFile!!
        )
        //        String commandNewApi = "-i " + ComModel.getCharEscaptedPath(videoModel.getPath()) + " -ss " + startTime + " -t " + String.valueOf(durationAudio) + " -c copy " + pathNewFile;
        val startTimeT = videoTimelineView!!.leftProgress * videoTimelineView!!.videoLength / 1000
        val endTimeT = videoTimelineView!!.rightProgress * videoTimelineView!!.videoLength / 1000
        val commandNewApi =
            "-i " + ComModel.getCharEscaptedPath(videoModel!!.path) + " -ss " + startTimeT + " -to " + endTimeT + " -strict -2 -async 1 -preset ultrafast " + pathNewFile
        initDialogProgress()
//        showInterstitial()
        execFFmpegBinary(commandNewApi, pathNewFile!!, fileName)


//        if (android.os.Build.VERSION.SDK_INT > Build.VERSION_CODES.P)
//            execFFmpegBinary(commandNewApi, pathNewFile, fileName);
//        else
//            execFFmpegBinary(command, pathNewFile, fileName);
    }

    private var dialogInputName: DialogInputName? = null
    private fun dialogSelectLocalSaveFile(): Boolean {
        fragmentAnalytics!!.addClick("Save")
        AmplitudeHelper.setSave()
        val defaultName = "VC_" + simpleDateFormat.format(System.currentTimeMillis())
        dialogInputName = context?.let { DialogInputName(it, this, defaultName, getString(R.string.save)) }
        dialogInputName?.initDialog()
        pauseVideo()
        return true
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_cutter, container, false)
    }

    private val isChangeVideoTimeline = false
    private fun initVideoTimeline() {
        videoTimelineView!!.clearFrames()
        videoTimelineView!!.setVideoPath(pathOldFile)
        //        videoTimelineView.setVideoPath(videoModel.getPath());
        videoTimelineView!!.setOnProgressChangeListener { leftChange: Boolean, currentMili: Long ->
            isPlayToEnd = true
            if (videoView!!.isPlaying) {
                videoView!!.pause()
                videoControllerView!!.goPauseMode()
            }
            videoView!!.seekTo(currentMili.toInt())
            if (positionIcon!!.visibility == View.VISIBLE) {
                positionIcon!!.visibility = View.GONE
            }
            positionIcon!!.clearAnimation()
            if (animator != null && animator!!.isRunning) {
                animator!!.cancel()
            }
        }
        if (true) {
            Flog.e("durrrrr        " + videoTimelineView!!.videoLength + "___" + videoView!!.duration)
        }
        if (videoTimelineView!!.videoLength <= 0) {
            Toast.makeText(context, getString(R.string.not_support_this_file), Toast.LENGTH_SHORT)
                .show()
            requireActivity().onBackPressed()
        }
    }

    private var isPlayToEnd = false
    private val isFirstTime = false
    private fun updateProgress() {
        if (handler == null) {
            handler = Handler()
        }
        handler!!.removeCallbacksAndMessages(null)
        handler!!.postDelayed(object : Runnable {
            override fun run() {
                Flog.e(" posssss     " + videoView!!.currentPosition + "___" + videoTimelineView!!.rightProgress * 100 * videoView!!.duration / 100)
                if (videoView!!.currentPosition >= videoTimelineView!!.rightProgress * 100 * videoView!!.duration / 100) {
                    Flog.e("paissssssssssssssss  ")
                    isPlayToEnd = true
                    pauseVideo()
                    handler!!.removeCallbacksAndMessages(null)
                } else {
                    handler!!.postDelayed(this, 500)
                }
            }
        }, 500)
    }

    private fun initVideoView() {
        videoView!!.setVideoPath(pathOldFile)
        videoView!!.setMediaListener(object : MediaListener {
            override fun onStart() {}
            override fun onPause() {}
            override fun onSeek(milisecond: Long) {}
        })
        videoView!!.setOnPreparedListener { mediaPlayer: MediaPlayer? ->
            mp = mediaPlayer
            videoView!!.setHandleListener(false)
            videoView!!.start()
            videoView!!.pause()
            videoView!!.setHandleListener(true)
        }
        videoView!!.setOnErrorListener { mediaPlayer: MediaPlayer?, i: Int, i1: Int -> true }
    }

    private fun initDialogProgress() {
        progressDialog = ProgressDialog(context)
        progressDialog!!.setCancelable(false)
        progressDialog!!.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL)
        progressDialog!!.setTitle(getString(R.string.progress_dialog_saving))
        progressDialog!!.progress = 0
        progressDialog!!.setButton(
            DialogInterface.BUTTON_NEGATIVE,
            getString(R.string.cancel)
        ) { dialog: DialogInterface?, which: Int -> cancelCutter() }
        progressDialog!!.show()
    }

    override fun onDestroy() {
        fragmentAnalytics!!.setDuration(watch!!.time(TimeUnit.SECONDS))
        MyApplication.addEvent(fragmentAnalytics)
        super.onDestroy()
    }

    private var isCancelCut = false
    private fun cancelCutter() {
        isCancelCut = true

//        if (ffmpeg.isFFmpegCommandRunning()) {
//            ffmpeg.killRunningProcesses();
//        }
//        if (ffMpegTest.isFFmpegCommandRunning()) {
//            ffMpegTest.killRunningProcesses();
//        }
        if (pathNewFile != null) {
            File(pathNewFile).delete()
        }
        if (progressDialog != null) {
            progressDialog!!.dismiss()
        }
    }

    //    private void execFFmpegBinary(final String[] command, String path, String title) {
    //        try {
    //            ffMpegTest.execute(command, new com.github.kohiyadav.libffmpeg.ExecuteBinaryResponseHandler() {
    //                @Override
    //                public void onFailure(String s) {
    //                    Flog.e("Successs     " + s);
    //                    progressDialog.dismiss();
    //                    Toast.makeText(getContext(), getString(R.string.can_not_create_file), Toast.LENGTH_SHORT).show();
    //                }
    //
    //                @Override
    //                public void onSuccess(String s) {
    //
    //                    if (isSuccessCut) return;
    //
    //                    progressDialog.setProgress(100);
    //                    progressDialog.dismiss();
    //
    //                    FileUtil.addFileToContentProvider(getContext(), path, title);
    //
    //                    Toast.makeText(getContext(), getString(R.string.create_file) + ": " + path, Toast.LENGTH_SHORT).show();
    //
    //                    if (isPauseFragment()) return;
    //
    //                    Utils.clearFragment(getFragmentManager());
    //
    //                    getContext().sendBroadcast(new Intent(Statistic.OPEN_CUTTER_STUDIO));
    //                }
    //
    //                @Override
    //                public void onProgress(String s) {
    //                    Flog.e(s);
    //                    int durationFile = (int) Utils.getProgress(s, Long.parseLong(videoModel.getDuration()) / 1000);
    //                    float percent = durationFile / (Float.parseFloat(videoModel.getDuration()) / 1000);
    //                    if (progressDialog != null) {
    //                        progressDialog.setProgress((int) (percent * 100));
    //                    }
    //                }
    //
    //                @Override
    //                public void onStart() {
    //
    //                }
    //
    //                @Override
    //                public void onFinish() {
    //
    //                }
    //            });
    //
    //        } catch (com.github.kohiyadav.libffmpeg.exceptions.FFmpegCommandAlreadyRunningException e) {
    //            e.printStackTrace();
    //
    //        }
    //    }
    //    private void testRun(final String[] command, String path, String title){
    //        if (FFmpeg.getInstance(getContext()).isSupported()) {
    //            FFmpeg ffmpeg = FFmpeg.getInstance(getContext());
    //            // to execute "ffmpeg -version" command you just need to pass "-version"
    //            ffmpeg.execute(command, new ExecuteBinaryResponseHandler() {
    //                @Override
    //                public void onFailure(String s) {
    //                    Flog.e("Successs     " + s);
    //                    progressDialog.dismiss();
    //                    Toast.makeText(getContext(), getString(R.string.can_not_create_file), Toast.LENGTH_SHORT).show();
    //                }
    //
    //                @Override
    //                public void onSuccess(String s) {
    //
    //                    if (isSuccessCut) return;
    //
    //                    progressDialog.setProgress(100);
    //                    progressDialog.dismiss();
    //
    //                    FileUtil.addFileToContentProvider(getContext(), path, title);
    //
    //                    Toast.makeText(getContext(), getString(R.string.create_file) + ": " + path, Toast.LENGTH_SHORT).show();
    //
    //                    if (isPauseFragment()) return;
    //
    //                    Utils.clearFragment(getFragmentManager());
    //
    //                    getContext().sendBroadcast(new Intent(Statistic.OPEN_CUTTER_STUDIO));
    //                }
    //
    //                @Override
    //                public void onProgress(String s) {
    //                    Flog.e(s);
    //                    int durationFile = (int) Utils.getProgress(s, Long.parseLong(videoModel.getDuration()) / 1000);
    //                    float percent = durationFile / (Float.parseFloat(videoModel.getDuration()) / 1000);
    //                    if (progressDialog != null) {
    //                        progressDialog.setProgress((int) (percent * 100));
    //                    }
    //                }
    //
    //                @Override
    //                public void onStart() {
    //
    //                }
    //
    //                @Override
    //                public void onFinish() {
    //
    //                }
    //
    //            });
    //        } else {
    //            // ffmpeg is not supported
    //        }
    //    }
    override fun afterInterstitial() {
        Log.d("ddd", "afterInterstetial: ")
        Handler(Looper.getMainLooper()).postDelayed({
            if (ffmpegDone) afterFFmpegOnSuccess()
            ffmpegDone = false
        }, 1000)

    }

    private fun execFFmpegBinary(command: Any, path: String, title: String) {

//        if (android.os.Build.VERSION.SDK_INT > Build.VERSION_CODES.P) {
        if (ComModel.isNewApiRequired()) {
            val myCommand = command as String
            ffmpegDone = false
            val executionId = FFmpeg.executeAsync(myCommand) { executionId, rc ->
                if (rc == Config.RETURN_CODE_SUCCESS) {
                    Log.i(Config.TAG, "Command execution completed successfully.")
                    afterFFmpegSuccess(path, title)
                } else if (rc == Config.RETURN_CODE_CANCEL) {
                    Log.i(Config.TAG, "Command execution cancelled by user.")
                    afterFFmpegFailure()
                } else {
                    Log.i(
                        Config.TAG,
                        String.format(
                            "Command execution failed with rc=%d and the output below.",
                            rc
                        )
                    )
                    Config.printLastCommandOutput(Log.INFO)
                    afterFFmpegFailure()
                }
            }
            Config.enableLogCallback { message ->
                val durationFile =
                    Utils.getProgress(message.text, videoModel!!.duration.toLong() / 1000).toInt()
                val percent = durationFile / (videoModel!!.duration.toFloat() / 1000)
                if (progressDialog != null) {
                    progressDialog!!.progress = (percent * 100).toInt()
                }
            }
        }

//        else {
//            try {
//                ffmpeg.execute((String[]) command, new ExecuteBinaryResponseHandler() {
//                    @Override
//                    public void onFailure(String s) {
//                        Flog.e("Successs     " + s);
//                        afterFFmpegFailure();
//                    }
//
//                    @Override
//                    public void onSuccess(String s) {
//
//                        afterFFmpegSuccess(path, title);
//                    }
//
//                    @Override
//                    public void onProgress(String s) {
//                        Flog.e(s);
//                        int durationFile = (int) Utils.getProgress(s, Long.parseLong(videoModel.getDuration()) / 1000);
//                        float percent = durationFile / (Float.parseFloat(videoModel.getDuration()) / 1000);
//                        if (progressDialog != null) {
//                            progressDialog.setProgress((int) (percent * 100));
//                        }
//                    }
//
//                    @Override
//                    public void onStart() {
//
//                    }
//
//                    @Override
//                    public void onFinish() {
//
//                    }
//                });
//
//            } catch (FFmpegCommandAlreadyRunningException e) {
//                e.printStackTrace();
//
//            }
//        }
    }

    private fun afterFFmpegFailure() {
        progressDialog!!.dismiss()
        Toast.makeText(context, getString(R.string.can_not_create_file), Toast.LENGTH_SHORT).show()
    }

    private fun afterFFmpegSuccess(path: String, title: String) {
        ffmpegDone = true
        if (isSuccessCut) return
        progressDialog!!.progress = 100
        progressDialog!!.dismiss()
        Log.d("ddd", "afterFFmpegSuccess: $path")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) FileUtil.scanGallery(
            context, path
        ) else FileUtil.addFileToContentProvider(
            context, path, title
        )
        Toast.makeText(context, getString(R.string.create_file) + ": " + path, Toast.LENGTH_SHORT)
            .show()
        afterFFmpegOnSuccess()
    }

    private fun afterFFmpegOnSuccess() {
        if (isPauseFragment) return
        Utils.clearFragment(fragmentManager)
        requireContext().sendBroadcast(Intent(Statistic.OPEN_CUTTER_STUDIO))
    }

    private fun pauseVideo() {
        if (videoView != null && null != videoControllerView) {
            videoView!!.pause()
            videoControllerView!!.goPauseMode()
        }
    }

    override fun onPause() {
        super.onPause()
        pauseVideo()
    }

    override fun onResume() {
        super.onResume()
    }

    override fun onApplySelect(nameFile: String) {
        save(nameFile)
    }

    override fun onCancelSelect() {
        if (dialogInputName == null) return
        dialogInputName!!.hideDialog()
    }

    override fun onFileNameEmpty() {
        Toast.makeText(context, getString(R.string.name_file_can_not_empty), Toast.LENGTH_SHORT)
            .show()
    }

    override fun onFileNameHasSpecialCharacter() {
        Toast.makeText(
            context,
            getString(R.string.name_file_can_not_contain_character),
            Toast.LENGTH_SHORT
        ).show()
    }

    override fun onCompleteVideo() {
        isPlayToEnd = false
    }

    override fun onStartVideo() {
        if (videoView != null && null != videoControllerView) {
            videoView!!.start()
            videoControllerView!!.startVideo()
            if (isPlayToEnd) {
//                Flog.e(" xxx              " + (int) ((videoTimelineView.getLeftProgress() * 100) * videoView.getDuration() / 100));
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    mp!!.seekTo(
                        (videoTimelineView!!.leftProgress * 100 * videoView!!.duration / 100).toInt()
                            .toLong(), MediaPlayer.SEEK_CLOSEST
                    )
                } else {
                    videoView!!.seekTo((videoTimelineView!!.leftProgress * 100 * videoView!!.duration / 100).toInt())
                }
                isPlayToEnd = false
            }
        }
        updateProgress()
        anim()
    }

    override fun onPauseVideo() {
        if (videoView != null && null != videoControllerView) {
            videoView!!.pause()
            videoControllerView!!.goPauseMode()
        }
        if (positionIcon!!.visibility == View.VISIBLE) {
            positionIcon!!.visibility = View.GONE
        }
        positionIcon!!.clearAnimation()
        if (animator != null && animator!!.isRunning) {
            animator!!.cancel()
        }
    }

    private fun anim() {
//        Log.d(TAG, "--anim--onProgressUpdate---->>>>>>>" + mMediaPlayer.getCurrentPosition());
        if (positionIcon!!.visibility == View.GONE) {
            positionIcon!!.visibility = View.VISIBLE
        }
        val params = positionIcon!!
            .getLayoutParams() as FrameLayout.LayoutParams
        val startTime = videoTimelineView!!.leftProgress * videoTimelineView!!.videoLength / 1000
        val endTime = videoTimelineView!!.rightProgress * videoTimelineView!!.videoLength / 1000
        val durationAudio = endTime - startTime
        Log.d("dfdfdf", "anim:  $durationAudio")
        //        int durationAudio = 10000;
//        int start = (int) (MARGIN
//                + (leftProgress/*mVideoView.getCurrentPosition()*/ - scrollPos) * averagePxMs);
//        int end = (int) (MARGIN + (rightProgress - scrollPos) * averagePxMs);
        animator = ValueAnimator
            .ofInt(
                videoTimelineView!!.positionLeftProgress,
                videoTimelineView!!.positionRightProgress
            )
            .setDuration((durationAudio * 1000).toLong())
        animator?.setInterpolator(LinearInterpolator())
        animator?.addUpdateListener(ValueAnimator.AnimatorUpdateListener { animation ->
            params.leftMargin = animation.animatedValue as Int
            positionIcon!!.layoutParams = params
        })
        animator?.start()
    }

    companion object {
        @JvmStatic
        fun newInstance(bundle: Bundle?): CutterFragment {
            val fragment = CutterFragment()
            fragment.arguments = bundle
            return fragment
        }
    }
}