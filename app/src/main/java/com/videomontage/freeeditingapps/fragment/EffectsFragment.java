//package com.videomontage.freeeditingapps.fragment;
//
//import android.app.Activity;
//import android.app.ProgressDialog;
//import android.content.Context;
//import android.content.DialogInterface;
//import android.content.Intent;
//import android.media.MediaMetadataRetriever;
//import android.os.Build;
//import android.os.Bundle;
//import android.os.Environment;
//import android.os.Handler;
//import android.os.Looper;
//import android.support.annotation.NonNull;
//import android.support.annotation.Nullable;
//import android.support.v4.view.ViewCompat;
//import android.support.v4.view.ViewPropertyAnimatorListener;
//import android.support.v7.app.AlertDialog;
//import android.text.TextUtils;
//import android.util.Log;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.view.ViewGroup;
//import android.view.Window;
//import android.view.WindowManager;
//import android.widget.AdapterView;
//import android.widget.GridView;
//import android.widget.ImageView;
//import android.widget.TextView;
//import android.widget.Toast;
//
//import com.arthenica.mobileffmpeg.Config;
//import com.bumptech.glide.Glide;
//import com.bumptech.glide.Priority;
//import com.bumptech.glide.load.engine.DiskCacheStrategy;
//import com.bumptech.glide.request.RequestOptions;
//import com.github.hiteshsondhi88.libffmpeg.ExecuteBinaryResponseHandler;
//import com.github.hiteshsondhi88.libffmpeg.FFmpeg;
//import com.github.hiteshsondhi88.libffmpeg.exceptions.FFmpegCommandAlreadyRunningException;
//import com.videomontage.freeeditingapps.R;
//import com.videomontage.freeeditingapps.adapter.FilterAdapter;
//import com.videomontage.freeeditingapps.listener.IInputNameFile;
//import com.videomontage.freeeditingapps.model.CommandModel;
//import com.videomontage.freeeditingapps.model.VideoModel;
//import com.videomontage.freeeditingapps.statistic.Statistic;
//import com.videomontage.freeeditingapps.utils.CommandsUtil;
//import com.videomontage.freeeditingapps.utils.CycleInterpolator;
//import com.videomontage.freeeditingapps.utils.FabricEvents;
//import com.videomontage.freeeditingapps.utils.FileUtil;
//import com.videomontage.freeeditingapps.utils.Flog;
//import com.videomontage.freeeditingapps.utils.Utils;
//import com.xiao.nicevideoplayer.NiceVideoPlayer;
//
//import java.io.File;
//import java.text.SimpleDateFormat;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.Locale;
//
//import static com.arthenica.mobileffmpeg.Config.RETURN_CODE_CANCEL;
//import static com.arthenica.mobileffmpeg.Config.RETURN_CODE_SUCCESS;
//
///**
// * Created by Hung on 11/15/2018.
// */
//
//public class EffectsFragment extends AbsFragment implements IInputNameFile, com.videomontage.freeeditingapps.custom.BubbleSeekBar.OnProgressChangedListener, NiceVideoPlayer.INiceVideoPlayerCallback {
//    private DialogInputName dialogInputName;
//    private FFmpeg ffmpeg;
//    private VideoModel videoModel;
//    private ProgressDialog progressDialog;
//    private SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.US);
//    private float tempoVideo = 1.0f, ptsVideo = 1.0f;
//    public static int lastPressedGridViewItem = 0;
//
//    ArrayList imageId = new ArrayList<>();
//    ArrayList web = new ArrayList<>();
//
//    private String file;
//    private boolean isSaveFile;
//    private boolean isInitThumb;
//    private ProgressDialog progressDialogInitThumbnailWithPreview;
//    private static boolean restoreWithPreview;
//    private boolean showToast = false;
//
//    @Override
//    public void initViews() {
//        ffmpeg = FFmpeg.getInstance(getContext());
//        videoModel = getArguments().getParcelable(Statistic.VIDEO_MODEL);
//
//        Flog.e("        " + videoModel.getPath());
//
//        getToolbar().setTitle("Effects");
//        getToolbar().getMenu().findItem(R.id.item_save).setOnMenuItemClickListener(menuItem -> dialogLocalSave()).setVisible(false);
//
//    }
//
//    @Override
//    public void onResume() {
//        super.onResume();
//        showToast = false;
//        if (web.size() > 0) {
//            web.clear();
//        }
//        if (imageId.size() > 0) {
//            imageId.clear();
//        }
//        if (progressDialog != null && progressDialog.isShowing()) {
//            if (restoreWithPreview) {
//                initThumbnails();
//            } else {
//                initThumbnailsImmediately(true);
//            }
//
//        } else {
//            new AlertDialog.Builder(getContext())
//                    .setTitle(getString(R.string.common_preview))
//                    .setMessage(getString(R.string.common_generate_preview))
//
//                    // Specifying a listener allows you to take an action before dismissing the dialog.
//                    // The dialog is automatically dismissed when a dialog button is clicked.
//                    .setPositiveButton(android.R.string.yes, new DialogInterface.OnClickListener() {
//                        public void onClick(DialogInterface dialog, int which) {
//                            initThumbnails();
//                            restoreWithPreview = true;
//                        }
//                    })
//
//                    // A null listener allows the button to dismiss the dialog and take no further action.
//                    .setNegativeButton(android.R.string.no, new DialogInterface.OnClickListener() {
//                        @Override
//                        public void onClick(DialogInterface dialogInterface, int i) {
//                            dialogInterface.dismiss();
//                            initThumbnailsImmediately(true);
//                            restoreWithPreview = false;
//                        }
//                    })
//                    .setCancelable(false)
//                    .show();
//        }
//    }
//
//    private void initThumbnails() {
//
//        isInitThumb = true;
//
//        new Thread(new Runnable() {
//            @Override
//            public void run() {
//                Glide.get(getContext()).clearDiskCache();
//            }
//        }).start();
//
//        if (CommandsUtil.LAST_SEEN_VID.equalsIgnoreCase(videoModel.getPath())) {
//            initThumbnailsImmediately(false);
//
//
//        } else {
//            File dir = getContext().getApplicationContext().getDir(CommandsUtil.THUMBNAILS_DIR
//                    , Context.MODE_PRIVATE);
//            if (dir.isDirectory()) {
//                String[] children = dir.list();
//                for (int i = 0; i < children.length; i++) {
//                    new File(dir, children[i]).delete();
//                }
//            }
//
//
//            CommandsUtil.initCommands(videoModel.getPath(), "", getContext());
//
//
//            CommandsUtil.LAST_SEEN_VID = videoModel.getPath();
//            CommandModel commandModel = (CommandModel) CommandsUtil.commands.get(CommandsUtil.COUNTER);
//            execFFmpegBinary(commandModel.getCommand(), commandModel.getPreviewImagePath(), commandModel.getFileNamePreview());
//            imageId.add(commandModel.getPreviewImagePath());
//            web.add(commandModel.getEffectName());
//            progressDialogInitThumbnailWithPreview = ProgressDialog.show(getContext(), "", getString(R.string.common_please_wait));
//        }
//    }
//
//    private void initThumbnailsImmediately(boolean initWithoutPreviews) {
//        progressDialogInitThumbnailWithPreview = ProgressDialog.show(getContext(), "", getString(R.string.common_please_wait));
//        if (initWithoutPreviews) {
//            CommandsUtil.initCommands(videoModel.getPath(), "", getContext());
//
//        }
//        for (int i = 0; i < CommandsUtil.commands.size(); i++) {
//            CommandModel commandModel = (CommandModel) CommandsUtil.commands.get(i);
//            imageId.add(commandModel.getPreviewImagePath());
//            web.add(commandModel.getEffectName());
//
//            if (i == CommandsUtil.commands.size() - 1) {
//                FilterAdapter adapter = new FilterAdapter(getContext(), web,
//                        imageId);
//                GridView grid = (GridView) findViewById(R.id.grid_view);
//                grid.setAdapter(adapter);
//                grid.setOnItemClickListener(new AdapterView.OnItemClickListener() {
//
//                    @Override
//                    public void onItemClick(AdapterView<?> parent, View view,
//                                            int position, long id) {
////                            grid.getChildAt(lastPressedGridViewItem).setBackgroundColor(getContext().getResources().getColor(R.color.colorPrimaryDark));
////                            grid.getChildAt(position).setBackgroundColor(getContext().getResources().getColor(R.color.high_light));
//                        lastPressedGridViewItem = position;
//                        CommandModel commandModel = (CommandModel) CommandsUtil.commands.get(position);
//
//                        AlertDialog.Builder builder =
//                                new AlertDialog.Builder(getContext(), R.style.MyCustomTheme);
//                        LayoutInflater inflater = ((Activity) getContext()).getLayoutInflater();
//                        View v = inflater.inflate(R.layout.image_preview_fragment, null);
//                        TextView effectNameTV = v.findViewById(R.id.effectNameTV);
//                        effectNameTV.setText(commandModel.getEffectName());
//                        ImageView filterPreviewImageView = v.findViewById(R.id.filterPreviewImageView);
////                            Glide.with(getContext())
////                                    .load(commandModel.getPreviewImagePath())
////                                    .into(filterPreviewImageView);
//                        builder.setView(v);
//
//                        RequestOptions options = new RequestOptions()
//                                .centerCrop()
//                                .placeholder(R.drawable.btn_fx)
//                                .error(R.drawable.btn_fx)
//                                .diskCacheStrategy(DiskCacheStrategy.NONE)
//                                .skipMemoryCache(true)
//                                .priority(Priority.HIGH);
//                        Glide.with(getContext())
//                                .load(commandModel.getPreviewImagePath())
//                                .apply(options)
//                                .into(filterPreviewImageView);
//                        AlertDialog alertDialog = builder.show();
////                            builder.show();
//                        v.findViewById(R.id.saveImagePreviewDialog).setOnClickListener(new View.OnClickListener() {
//                            @Override
//                            public void onClick(View view) {
//                                alertDialog.dismiss();
//                                dialogLocalSave();
//                            }
//                        });
//                        alertDialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND); // This flag is required to set otherwise the setDimAmount method will not show any effect
//                        alertDialog.getWindow().setDimAmount(0.9f);
//                    }
//                });
//                progressDialogInitThumbnailWithPreview.dismiss();
//            }
//        }
//    }
//
//
//    @Override
//    public void onPause() {
//        super.onPause();
//        if (ffmpeg.isFFmpegCommandRunning()) {
//            ffmpeg.killRunningProcesses();
//        }
//        if (progressDialogInitThumbnailWithPreview != null && progressDialogInitThumbnailWithPreview.isShowing()) {
//            progressDialogInitThumbnailWithPreview.dismiss();
//        }
//    }
//
//
//    private boolean dialogLocalSave() {
//        String defaultName = "VS_" + simpleDateFormat.format(System.currentTimeMillis());
//        dialogInputName = new DialogInputName(getContext(), this, defaultName, getString(R.string.save));
//        dialogInputName.initDialog();
//        return true;
//    }
//
//    public static EffectsFragment newInstance(Bundle bundle) {
//        EffectsFragment fragment = new EffectsFragment();
//        fragment.setArguments(bundle);
//        return fragment;
//    }
//
//    private String newPath = null, oldPath = null;
//
//
//    @Nullable
//    @Override
//    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
//        return inflater.inflate(R.layout.fragment_effects, container, false);
//
//    }
//
//
//    @Override
//    public void onApplySelect(String nameFile) {
//        saveFile(nameFile);
//    }
//
//    private void saveFile(String nameFile) {
//
//        isCancelSaveFile = false;
//        isSaveFile = true;
//        showToast = true;
//
//        String extensionFile = null;
//
//        if (FileUtil.isEmpty(nameFile)) {
//            Toast.makeText(getContext(), getString(R.string.name_file_can_not_empty), Toast.LENGTH_SHORT).show();
//            return;
//        }
//
//        if (Utils.isStringHasCharacterSpecial(nameFile)) {
//            Toast.makeText(getContext(), getString(R.string.name_file_can_not_contain_character), Toast.LENGTH_SHORT).show();
//            return;
//        }
//
//        newPath = Environment.getExternalStorageDirectory().getAbsolutePath() + Statistic.DIR_APP + Statistic.DIR_EFFECTS + "/";
//
//        File f = new File(newPath);
//        if (!f.exists()) f.mkdirs();
//
//        extensionFile = Utils.getFileExtension(videoModel.getPath());
//
//        newPath = newPath + nameFile + extensionFile;
////        newPath = newPath + nameFile + ".mkv";
//
//        if (new File(newPath).exists()) {
//            dialogInputName.hideDialog();
//            Toast.makeText(getContext(), getString(R.string.name_file_exist), Toast.LENGTH_SHORT).show();
//            return;
//        }
//
//        String sSpeed = "[0:v]setpts=" + ptsVideo + "*PTS[v];[0:a]atempo=" + tempoVideo + "[a]";
//        //String[] complexCommand = {"-i", videoModel.getPath(), "-filter_complex", sSpeed, "-map", "[v]", "-map", "[a]", "-b:v", "2097k", "-r", "60", "-vcodec", "mpeg4", newPath};
//        String[] complexCommand = {"-i", videoModel.getPath(), "-filter_complex", sSpeed, "-map", "[v]", "-map", "[a]", newPath};
//        String[] complexCommandNew2 = {"-i", videoModel.getPath(), "-vf", "drawtext=fontsize=60:fontfile=/system/fonts/DroidSans.ttf:fontcolor=green:text=AAAA:x=(w-max_glyph_w)/2-200:y=h/2-ascent", newPath};
//        String[] complexCommandNew = {"-y", "-i", videoModel.getPath(), "-strict",
//                "experimental", "-vf", "movie="
//                + "/storage/emulated/0/Download/logo.png [watermark]; [in][watermark] overlay=main_w/2-overlay_w/2:450 [out]",
//                "-b:v", "1540k", "-c:v", "libx264", "-threads", "30", "-preset", "ultrafast", "-r", "30", "-s", "1080x1920", "-c:a", "copy",
//                newPath};
//
//
//        String[] boomerangCommand = {"-y", "-i", videoModel.getPath(), "-filter_complex", "[0]reverse[r];[0][r][0]concat=n=3,setpts=0.5*PTS", newPath};
//        String[] boxCommand = {"-i", videoModel.getPath(), "-filter_complex", "[0:v]scale=720:720,boxblur=luma_radius=min(h\\,w)/20:luma_power=1:chroma_radius=min(cw\\,ch)/20:chroma_power=1[bg];[0:v]scale=720:720:force_original_aspect_ratio=decrease[fg];[bg][fg]overlay=(W-w)/2:(H-h)/2[outv]", "-map", "[outv]", "-map", "0:a?", newPath};
//        String[] niceFilterCommand = {"-i", videoModel.getPath(), "-filter_complex", "[0:v]eq=contrast=1:brightness=0:saturation=1:gamma=1:gamma_r=1:gamma_g=1:gamma_b=1:gamma_weight=1[outv]", "-map", "[outv]", "-map", "0:a?", newPath};
////        String[] invertColorsCommand = {"-y", "-i", videoModel.getPath(), "-vf", "negate", newPath};
//        String[] blackAndWhiteCommand = {"-y", "-i", videoModel.getPath(), "-vf", "hue=s=0", "-preset", "ultrafast", "-c:a", "copy", newPath};
//        String[] sepiaCommand = {"-i", videoModel.getPath(), "-strict", "experimental", "-preset", "ultrafast", "-filter_complex", "[0:v]colorchannelmixer=.393:.769:.189:0:.349:.686:.168:0:.272:.534:.131[colorchannelmixed];[colorchannelmixed]eq=1.0:0:1.3:2.4:1.0:1.0:1.0:1.0[color_effect]", "-map", "[color_effect]", "-map", "0:a", "-vcodec", "mpeg4", "-crf", "23", "-b:v", "1000K", "-maxrate", "1500K", "-bufsize", "500K", newPath};
//        String[] vignetteCommand = {"-i", videoModel.getPath(), "-preset", "ultrafast", "-vf", "vignette=angle=PI/4", newPath};
//        String[] gammaCorrenctionCommand = {"-i", videoModel.getPath(), "-preset", "ultrafast", "-vf", "eq=gamma=7.0:saturation=1.3", "-c:a", "copy", newPath};
//        String[] stabilizationCommand = {"-i", videoModel.getPath(), "-preset", "ultrafast", "-vf", "deshake", "-c:a", "copy", newPath};
//        String[] sharpCommand = {"-i", videoModel.getPath(), "-preset", "ultrafast", "-vf", "unsharp", "-c:a", "copy", newPath};
//        String[] zoomInCommand = {"-i", videoModel.getPath(), "-preset", "ultrafast", "-vf", "scale=2*iw:-1, crop=iw/2:ih/2", "-c:a", "copy", newPath};
//        // this command makes video longer so progress bar not working as expected
//        String[] zoomInProgressivelyCommand = {"-i", videoModel.getPath(), "-vf", "zoompan=z='1+(1.4*in/300)':x='70*in/300':y='190*in/300':d=1", newPath};
//        String[] flipVerticalCommand = {"-i", videoModel.getPath(), "-vf", "vflip", "-c:a", "copy", newPath};
//        String[] flipHorizontalCommand = {"-i", videoModel.getPath(), "-vf", "hflip", "-c:a", "copy", newPath};
//        String[] rotate90DegreesClockwiseCommand = {"-i", videoModel.getPath(), "-vf", "transpose=1", "-c:a", "copy", newPath};
//        String[] rotate90DegreesCounterclockwiseCommand = {"-i", videoModel.getPath(), "-vf", "transpose=2", "-c:a", "copy", newPath};
//        String[] blurCommand = {"-i", videoModel.getPath(), "-filter_complex", "[0:v]boxblur=10[bg];[0:v]crop=100:100:60:30[fg];[bg][fg]overlay=60:30", "-map", "0:a", "-c:v", "libx264", "-c:a", "copy", "-movflags", "+faststart", newPath};
//        String[] convolutionCommand = {"-i", videoModel.getPath(), "-vf", "convolution=\"-2 -1 0 -1 1 1 0 1 2:-2 -1 0 -1 1 1 0 1 2:-2 -1 0 -1 1 1 0 1 2:-2 -1 0 -1 1 1 0 1 2\"", "-c:a", "copy", newPath};
//        String[] convolution2Command = {"-i", videoModel.getPath(), "-vf", "convolution=1 1 1 1 -8 1 1 1 1:1 1 1 1 -8 1 1 1 1:1 1 1 1 -8 1 1 1 1:1 1 1 1 -8 1 1 1 1:5:5:5:1:0:128:128:0", "-c:a", "copy", newPath};
////        String[] verticalVideoWithBlurAsideCommand = {"-i", videoModel.getPath(), "-lavfi", "[0:v]scale=ih*16/9:-1,boxblur=luma_radius=min(h\\,w)/20:luma_power=1:chroma_radius=min(cw\\,ch)/20:chroma_power=1[bg];[bg][0:v]overlay=(W-w)/2:(H-h)/2,crop=h=iw*9/16","-vb", "800K","-pix_fmt","yuv420p","-y", newPath};
//        //startd play probably dont support this video but vlc does
//        String[] stereoscopicCommand = {"-i", videoModel.getPath(), "-vf", "stereo3d=sbs2l:arcc", "-c:a", "copy", "-pix_fmt", "yuv420p", "-y", newPath};
//        String[] pixelizeCommand = {"-i", videoModel.getPath(), "-vf", "scale=iw/10:ih/10,scale=10*iw:10*ih:flags=neighbor", "-c:a", "copy", newPath};
//        String[] brightnessPlusCommand = {"-i", videoModel.getPath(), "-vf", "eq=brightness=0.3", newPath};
//        String[] brightnessMinusCommand = {"-i", videoModel.getPath(), "-vf", "eq=brightness=-0.3", newPath};
//        String[] saturationMinusCommand = {"-i", videoModel.getPath(), "-vf", "eq=brightness=-0.3", newPath};
//        String[] saturationPlusCommand = {"-i", videoModel.getPath(), "-vf", "eq=brightness=0.3", newPath};
//
//        String[] gammaRMinusCommand = {"-i", videoModel.getPath(), "-vf", "eq=gamma_r=-0.5", newPath};
//        String[] gammaRPlusCommand = {"-i", videoModel.getPath(), "-vf", "eq=gamma_r=1.5", newPath};
//
//        String[] gammaGMinusCommand = {"-i", videoModel.getPath(), "-vf", "eq=gamma_g=-0.5", newPath};
//        String[] gammaGPlusCommand = {"-i", videoModel.getPath(), "-vf", "eq=gamma_g=1.5", newPath};
//
//        String[] gammaBMinusCommand = {"-i", videoModel.getPath(), "-vf", "eq=gamma_b=-0.5", newPath};
//        String[] gammaBPlusCommand = {"-i", videoModel.getPath(), "-vf", "eq=gamma_b=1.5", newPath};
//        String[] colorbalanceCommand = {"-i", videoModel.getPath(), "-vf", "colorbalance=rs=-0.3:bs=0.3:rh=0.1:bh=-0.1", "-pix_fmt", "yuv420p", "-y", newPath};
//        String[] colorbalance2Command = {"-i", videoModel.getPath(), "-vf", "colorbalance=gs=0.3:rh=0.1:bh=0.1", "-pix_fmt", "yuv420p", "-y", newPath};
//        String[] vintageCommand = {"-i", videoModel.getPath(), "-vf", "curves=vintage", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] fadeInCommand = {"-i", videoModel.getPath(), "-filter_complex", "fade=in:st=0:d=4; afade=in:st=0:d=4", "-c:v", "libx264", "-c:a", "aac", newPath};
//        String[] fadeOutCommand = {"-i", videoModel.getPath(), "-filter_complex", "fade=out:st=6:d=4; afade=out:st=6:d=4", "-c:v", "libx264", "-c:a", "aac", newPath};
//
//        String[] colorNegativeCommand = {"-i", videoModel.getPath(), "-vf", "curves=color_negative", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] crossProcessCommand = {"-i", videoModel.getPath(), "-vf", "curves=cross_process", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] darkerCommand = {"-i", videoModel.getPath(), "-vf", "curves=darker", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] increaseContrastCommand = {"-i", videoModel.getPath(), "-vf", "curves=increase_contrast", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] lighterCommand = {"-i", videoModel.getPath(), "-vf", "curves=lighter", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] linearContrastCommand = {"-i", videoModel.getPath(), "-vf", "curves=linear_contrast", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] mediumContrastCommand = {"-i", videoModel.getPath(), "-vf", "curves=medium_contrast", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] strongContrastCommand = {"-i", videoModel.getPath(), "-vf", "curves=strong_contrast", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] negativeCommand = {"-i", videoModel.getPath(), "-vf", "curves=negative", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] curvesBlueCommand = {"-i", videoModel.getPath(), "-vf", "curves=blue='0/0 0.5/0.58 1/1'", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//
//        String[] elbg2Command = {"-i", videoModel.getPath(), "-vf", "elbg=2:n=1", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] elbg4Command = {"-i", videoModel.getPath(), "-vf", "elbg=4:n=1", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] elbg8Command = {"-i", videoModel.getPath(), "-vf", "elbg=8:n=1", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] elbg16Command = {"-i", videoModel.getPath(), "-vf", "elbg=16:n=1", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//
//        String[] debandCommand = {"-i", videoModel.getPath(), "-vf", "deband=1thr=0.5:2thr=0.5:3thr=0.5", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] swapRect6x6ver1Command = {"-i", videoModel.getPath(), "-vf", "swaprect=w/6:h/6:0:5*h/6,swaprect=w/6:h/6:5*w/6:h/2,swaprect=w/6:h/6:w/3:5*h/6,swaprect=w/6:h/6:0:h/3,swaprect=w/6:h/6:w/6:0,swaprect=w/6:h/6:w/2:h/6,swaprect=w/6:h/6:5*w/6:5*h/6,swaprect=w/6:h/6:2*w/3:0,swaprect=w/6:h/6:w/3:h/6,swaprect=w/6:h/6:2*w/3:h/3,swaprect=w/6:h/6:0:h/2,swaprect=w/6:h/6:2*w/3:2*h/3,swaprect=w/6:h/6:w/2:5*h/6,swaprect=w/6:h/6:w/2:0,swaprect=w/6:h/6:5*w/6:0,swaprect=w/6:h/6:w/2:h/3,swaprect=w/6:h/6:2*w/3:h/2,swaprect=w/6:h/6:0:h/6,swaprect=w/6:h/6:w/6:h/3,swaprect=w/6:h/6:5*w/6:2*h/3,swaprect=w/6:h/6:0:2*h/3,swaprect=w/6:h/6:w/3:h/3,swaprect=w/6:h/6:2*w/3:5*h/6,swaprect=w/6:h/6:w/6:2*h/3,swaprect=w/6:h/6:w/6:h/6,swaprect=w/6:h/6:2*w/3:h/6,swaprect=w/6:h/6:w/3:2*h/3,swaprect=w/6:h/6:w/2:h/2,swaprect=w/6:h/6:w/2:2*h/3,swaprect=w/6:h/6:w/3:h/2,swaprect=w/6:h/6:w/6:h/2,swaprect=w/6:h/6:5*w/6:h/3,swaprect=w/6:h/6:w/6:5*h/6,swaprect=w/6:h/6:5*w/6:h/6,swaprect=w/6:h/6:0:0,swaprect=w/6:h/6:w/3:0", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] swapRect6x6ver2Command = {"-i", videoModel.getPath(), "-vf", "swaprect=w/6:h/6:w/3:0,swaprect=w/6:h/6:0:0,swaprect=w/6:h/6:5*w/6:h/6,swaprect=w/6:h/6:w/6:5*h/6,swaprect=w/6:h/6:5*w/6:h/3,swaprect=w/6:h/6:w/6:h/2,swaprect=w/6:h/6:w/3:h/2,swaprect=w/6:h/6:w/2:2*h/3,swaprect=w/6:h/6:w/2:h/2,swaprect=w/6:h/6:w/3:2*h/3,swaprect=w/6:h/6:2*w/3:h/6,swaprect=w/6:h/6:w/6:h/6,swaprect=w/6:h/6:w/6:2*h/3,swaprect=w/6:h/6:2*w/3:5*h/6,swaprect=w/6:h/6:w/3:h/3,swaprect=w/6:h/6:0:2*h/3,swaprect=w/6:h/6:5*w/6:2*h/3,swaprect=w/6:h/6:w/6:h/3,swaprect=w/6:h/6:0:h/6,swaprect=w/6:h/6:2*w/3:h/2,swaprect=w/6:h/6:w/2:h/3,swaprect=w/6:h/6:5*w/6:0,swaprect=w/6:h/6:w/2:0,swaprect=w/6:h/6:w/2:5*h/6,swaprect=w/6:h/6:2*w/3:2*h/3,swaprect=w/6:h/6:0:h/2,swaprect=w/6:h/6:2*w/3:h/3,swaprect=w/6:h/6:w/3:h/6,swaprect=w/6:h/6:2*w/3:0,swaprect=w/6:h/6:5*w/6:5*h/6,swaprect=w/6:h/6:w/2:h/6,swaprect=w/6:h/6:w/6:0,swaprect=w/6:h/6:0:h/3,swaprect=w/6:h/6:w/3:5*h/6,swaprect=w/6:h/6:5*w/6:h/2,swaprect=w/6:h/6:0:5*h/6", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] swapRect10x6ver1Command = {"-i", videoModel.getPath(), "-vf", "swaprect=w/10:h/6:w/10:5*h/6,swaprect=w/10:h/6:w/10:h/3,swaprect=w/10:h/6:0:h/3,swaprect=w/10:h/6:2*w/5:5*h/6,swaprect=w/10:h/6:0:h/2,swaprect=w/10:h/6:3*w/5:2*h/3,swaprect=w/10:h/6:7*w/10:0,swaprect=w/10:h/6:3*w/10:h/6,swaprect=w/10:h/6:9*w/10:h/3,swaprect=w/10:h/6:4*w/5:2*h/3,swaprect=w/10:h/6:3*w/5:h/6,swaprect=w/10:h/6:2*w/5:2*h/3,swaprect=w/10:h/6:4*w/5:5*h/6,swaprect=w/10:h/6:2*w/5:h/6,swaprect=w/10:h/6:4*w/5:h/2,swaprect=w/10:h/6:9*w/10:h/6,swaprect=w/10:h/6:w/5:0,swaprect=w/10:h/6:w/2:h/3,swaprect=w/10:h/6:3*w/10:h/2,swaprect=w/10:h/6:7*w/10:h/6,swaprect=w/10:h/6:2*w/5:h/2,swaprect=w/10:h/6:3*w/5:h/3,swaprect=w/10:h/6:2*w/5:h/3,swaprect=w/10:h/6:4*w/5:h/6,swaprect=w/10:h/6:3*w/10:5*h/6,swaprect=w/10:h/6:7*w/10:5*h/6,swaprect=w/10:h/6:w/10:0,swaprect=w/10:h/6:w/5:h/3,swaprect=w/10:h/6:w/5:5*h/6,swaprect=w/10:h/6:4*w/5:h/3,swaprect=w/10:h/6:w/2:h/2,swaprect=w/10:h/6:3*w/5:5*h/6,swaprect=w/10:h/6:0:5*h/6,swaprect=w/10:h/6:0:2*h/3,swaprect=w/10:h/6:w/10:2*h/3,swaprect=w/10:h/6:w/2:2*h/3,swaprect=w/10:h/6:w/5:h/2,swaprect=w/10:h/6:4*w/5:0,swaprect=w/10:h/6:w/10:h/2,swaprect=w/10:h/6:7*w/10:h/2,swaprect=w/10:h/6:9*w/10:5*h/6,swaprect=w/10:h/6:9*w/10:h/2,swaprect=w/10:h/6:w/5:h/6,swaprect=w/10:h/6:3*w/10:2*h/3,swaprect=w/10:h/6:w/2:5*h/6,swaprect=w/10:h/6:2*w/5:0,swaprect=w/10:h/6:9*w/10:2*h/3,swaprect=w/10:h/6:3*w/10:h/3,swaprect=w/10:h/6:0:h/6,swaprect=w/10:h/6:9*w/10:0,swaprect=w/10:h/6:3*w/10:0,swaprect=w/10:h/6:3*w/5:h/2,swaprect=w/10:h/6:w/2:0,swaprect=w/10:h/6:7*w/10:2*h/3,swaprect=w/10:h/6:7*w/10:h/3,swaprect=w/10:h/6:w/10:h/6,swaprect=w/10:h/6:w/2:h/6,swaprect=w/10:h/6:w/5:2*h/3,swaprect=w/10:h/6:3*w/5:0,swaprect=w/10:h/6:0:0,", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] swapRect10x6ver2Command = {"-i", videoModel.getPath(), "-vf", "swaprect=w/10:h/6:0:0,swaprect=w/10:h/6:3*w/5:0,swaprect=w/10:h/6:w/5:2*h/3,swaprect=w/10:h/6:w/2:h/6,swaprect=w/10:h/6:w/10:h/6,swaprect=w/10:h/6:7*w/10:h/3,swaprect=w/10:h/6:7*w/10:2*h/3,swaprect=w/10:h/6:w/2:0,swaprect=w/10:h/6:3*w/5:h/2,swaprect=w/10:h/6:3*w/10:0,swaprect=w/10:h/6:9*w/10:0,swaprect=w/10:h/6:0:h/6,swaprect=w/10:h/6:3*w/10:h/3,swaprect=w/10:h/6:9*w/10:2*h/3,swaprect=w/10:h/6:2*w/5:0,swaprect=w/10:h/6:w/2:5*h/6,swaprect=w/10:h/6:3*w/10:2*h/3,swaprect=w/10:h/6:w/5:h/6,swaprect=w/10:h/6:9*w/10:h/2,swaprect=w/10:h/6:9*w/10:5*h/6,swaprect=w/10:h/6:7*w/10:h/2,swaprect=w/10:h/6:w/10:h/2,swaprect=w/10:h/6:4*w/5:0,swaprect=w/10:h/6:w/5:h/2,swaprect=w/10:h/6:w/2:2*h/3,swaprect=w/10:h/6:w/10:2*h/3,swaprect=w/10:h/6:0:2*h/3,swaprect=w/10:h/6:0:5*h/6,swaprect=w/10:h/6:3*w/5:5*h/6,swaprect=w/10:h/6:w/2:h/2,swaprect=w/10:h/6:4*w/5:h/3,swaprect=w/10:h/6:w/5:5*h/6,swaprect=w/10:h/6:w/5:h/3,swaprect=w/10:h/6:w/10:0,swaprect=w/10:h/6:7*w/10:5*h/6,swaprect=w/10:h/6:3*w/10:5*h/6,swaprect=w/10:h/6:4*w/5:h/6,swaprect=w/10:h/6:2*w/5:h/3,swaprect=w/10:h/6:3*w/5:h/3,swaprect=w/10:h/6:2*w/5:h/2,swaprect=w/10:h/6:7*w/10:h/6,swaprect=w/10:h/6:3*w/10:h/2,swaprect=w/10:h/6:w/2:h/3,swaprect=w/10:h/6:w/5:0,swaprect=w/10:h/6:9*w/10:h/6,swaprect=w/10:h/6:4*w/5:h/2,swaprect=w/10:h/6:2*w/5:h/6,swaprect=w/10:h/6:4*w/5:5*h/6,swaprect=w/10:h/6:2*w/5:2*h/3,swaprect=w/10:h/6:3*w/5:h/6,swaprect=w/10:h/6:4*w/5:2*h/3,swaprect=w/10:h/6:9*w/10:h/3,swaprect=w/10:h/6:3*w/10:h/6,swaprect=w/10:h/6:7*w/10:0,swaprect=w/10:h/6:3*w/5:2*h/3,swaprect=w/10:h/6:0:h/2,swaprect=w/10:h/6:2*w/5:5*h/6,swaprect=w/10:h/6:0:h/3,swaprect=w/10:h/6:w/10:h/3,swaprect=w/10:h/6:w/10:5*h/6", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] swapRect16x9ver1Command = {"-i", videoModel.getPath(), "-vf", "swaprect=w/16:h/9:w/16:5*h/9,swaprect=w/16:h/9:7*w/16:2*h/9,swaprect=w/16:h/9:w/2:8*h/9,swaprect=w/16:h/9:7*w/8:h/3,swaprect=w/16:h/9:13*w/16:8*h/9,swaprect=w/16:h/9:3*w/4:h/9,swaprect=w/16:h/9:5*w/8:h/3,swaprect=w/16:h/9:9*w/16:5*h/9,swaprect=w/16:h/9:w/2:h/3,swaprect=w/16:h/9:0:8*h/9,swaprect=w/16:h/9:3*w/8:2*h/9,swaprect=w/16:h/9:5*w/8:5*h/9,swaprect=w/16:h/9:11*w/16:8*h/9,swaprect=w/16:h/9:0:4*h/9,swaprect=w/16:h/9:11*w/16:h/3,swaprect=w/16:h/9:7*w/8:7*h/9,swaprect=w/16:h/9:15*w/16:4*h/9,swaprect=w/16:h/9:3*w/16:7*h/9,swaprect=w/16:h/9:5*w/16:h/9,swaprect=w/16:h/9:13*w/16:h/9,swaprect=w/16:h/9:9*w/16:4*h/9,swaprect=w/16:h/9:w/4:2*h/9,swaprect=w/16:h/9:w/8:4*h/9,swaprect=w/16:h/9:9*w/16:2*h/3,swaprect=w/16:h/9:5*w/16:h/3,swaprect=w/16:h/9:w/16:8*h/9,swaprect=w/16:h/9:3*w/4:2*h/3,swaprect=w/16:h/9:3*w/4:0,swaprect=w/16:h/9:5*w/16:7*h/9,swaprect=w/16:h/9:13*w/16:2*h/9,swaprect=w/16:h/9:5*w/16:2*h/3,swaprect=w/16:h/9:5*w/8:4*h/9,swaprect=w/16:h/9:w/4:2*h/3,swaprect=w/16:h/9:3*w/8:4*h/9,swaprect=w/16:h/9:w/4:5*h/9,swaprect=w/16:h/9:7*w/8:5*h/9,swaprect=w/16:h/9:w/2:2*h/3,swaprect=w/16:h/9:13*w/16:0,swaprect=w/16:h/9:w/16:2*h/9,swaprect=w/16:h/9:11*w/16:4*h/9,swaprect=w/16:h/9:7*w/8:4*h/9,swaprect=w/16:h/9:3*w/16:4*h/9,swaprect=w/16:h/9:0:5*h/9,swaprect=w/16:h/9:3*w/8:8*h/9,swaprect=w/16:h/9:0:h/3,swaprect=w/16:h/9:w/16:7*h/9,swaprect=w/16:h/9:9*w/16:7*h/9,swaprect=w/16:h/9:3*w/8:7*h/9,swaprect=w/16:h/9:w/4:7*h/9,swaprect=w/16:h/9:w/8:2*h/3,swaprect=w/16:h/9:w/2:0,swaprect=w/16:h/9:0:2*h/9,swaprect=w/16:h/9:3*w/4:2*h/9,swaprect=w/16:h/9:5*w/8:0,swaprect=w/16:h/9:3*w/8:h/3,swaprect=w/16:h/9:7*w/16:h/3,swaprect=w/16:h/9:9*w/16:h/3,swaprect=w/16:h/9:w/8:h/3,swaprect=w/16:h/9:3*w/8:5*h/9,swaprect=w/16:h/9:3*w/16:5*h/9,swaprect=w/16:h/9:5*w/16:0,swaprect=w/16:h/9:11*w/16:2*h/9,swaprect=w/16:h/9:w/8:2*h/9,swaprect=w/16:h/9:w/16:h/3,swaprect=w/16:h/9:3*w/16:8*h/9,swaprect=w/16:h/9:w/4:h/9,swaprect=w/16:h/9:7*w/8:2*h/3,swaprect=w/16:h/9:13*w/16:5*h/9,swaprect=w/16:h/9:7*w/8:0,swaprect=w/16:h/9:3*w/16:h/9,swaprect=w/16:h/9:3*w/4:h/3,swaprect=w/16:h/9:7*w/16:h/9,swaprect=w/16:h/9:15*w/16:7*h/9,swaprect=w/16:h/9:7*w/16:5*h/9,swaprect=w/16:h/9:15*w/16:5*h/9,swaprect=w/16:h/9:15*w/16:0,swaprect=w/16:h/9:0:h/9,swaprect=w/16:h/9:11*w/16:2*h/3,swaprect=w/16:h/9:w/2:2*h/9,swaprect=w/16:h/9:7*w/8:h/9,swaprect=w/16:h/9:9*w/16:8*h/9,swaprect=w/16:h/9:3*w/4:8*h/9,swaprect=w/16:h/9:w/2:4*h/9,swaprect=w/16:h/9:0:7*h/9,swaprect=w/16:h/9:w/8:0,swaprect=w/16:h/9:13*w/16:2*h/3,swaprect=w/16:h/9:11*w/16:0,swaprect=w/16:h/9:w/2:5*h/9,swaprect=w/16:h/9:15*w/16:8*h/9,swaprect=w/16:h/9:w/4:h/3,swaprect=w/16:h/9:13*w/16:h/3,swaprect=w/16:h/9:3*w/16:2*h/9,swaprect=w/16:h/9:7*w/16:8*h/9,swaprect=w/16:h/9:3*w/8:2*h/3,swaprect=w/16:h/9:15*w/16:h/3,swaprect=w/16:h/9:w/16:2*h/3,swaprect=w/16:h/9:0:0,swaprect=w/16:h/9:w/2:7*h/9,swaprect=w/16:h/9:5*w/8:7*h/9,swaprect=w/16:h/9:5*w/8:8*h/9,swaprect=w/16:h/9:w/16:0,swaprect=w/16:h/9:w/8:h/9,swaprect=w/16:h/9:5*w/16:5*h/9,swaprect=w/16:h/9:w/8:5*h/9,swaprect=w/16:h/9:3*w/16:2*h/3,swaprect=w/16:h/9:w/4:8*h/9,swaprect=w/16:h/9:9*w/16:0,swaprect=w/16:h/9:5*w/8:2*h/3,swaprect=w/16:h/9:7*w/8:2*h/9,swaprect=w/16:h/9:3*w/4:4*h/9,swaprect=w/16:h/9:7*w/16:7*h/9,swaprect=w/16:h/9:w/16:4*h/9,swaprect=w/16:h/9:5*w/16:8*h/9,swaprect=w/16:h/9:5*w/16:4*h/9,swaprect=w/16:h/9:13*w/16:4*h/9,swaprect=w/16:h/9:7*w/16:0,swaprect=w/16:h/9:w/16:h/9,swaprect=w/16:h/9:15*w/16:h/9,swaprect=w/16:h/9:0:2*h/3,swaprect=w/16:h/9:9*w/16:h/9,swaprect=w/16:h/9:13*w/16:7*h/9,swaprect=w/16:h/9:15*w/16:2*h/3,swaprect=w/16:h/9:3*w/16:0,swaprect=w/16:h/9:3*w/8:h/9,swaprect=w/16:h/9:3*w/4:5*h/9,swaprect=w/16:h/9:3*w/4:7*h/9,swaprect=w/16:h/9:7*w/8:8*h/9,swaprect=w/16:h/9:7*w/16:2*h/3,swaprect=w/16:h/9:w/4:4*h/9,swaprect=w/16:h/9:3*w/8:0,swaprect=w/16:h/9:3*w/16:h/3,swaprect=w/16:h/9:w/8:7*h/9,swaprect=w/16:h/9:11*w/16:h/9,swaprect=w/16:h/9:5*w/8:2*h/9,swaprect=w/16:h/9:15*w/16:2*h/9,swaprect=w/16:h/9:11*w/16:5*h/9,swaprect=w/16:h/9:w/2:h/9,swaprect=w/16:h/9:5*w/16:2*h/9,swaprect=w/16:h/9:w/8:8*h/9,swaprect=w/16:h/9:7*w/16:4*h/9,swaprect=w/16:h/9:5*w/8:h/9,swaprect=w/16:h/9:9*w/16:2*h/9,swaprect=w/16:h/9:11*w/16:7*h/9,swaprect=w/16:h/9:w/4:0", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] swapRect16x9ver2Command = {"-i", videoModel.getPath(), "-vf", "swaprect=w/16:h/9:w/4:0,swaprect=w/16:h/9:11*w/16:7*h/9,swaprect=w/16:h/9:9*w/16:2*h/9,swaprect=w/16:h/9:5*w/8:h/9,swaprect=w/16:h/9:7*w/16:4*h/9,swaprect=w/16:h/9:w/8:8*h/9,swaprect=w/16:h/9:5*w/16:2*h/9,swaprect=w/16:h/9:w/2:h/9,swaprect=w/16:h/9:11*w/16:5*h/9,swaprect=w/16:h/9:15*w/16:2*h/9,swaprect=w/16:h/9:5*w/8:2*h/9,swaprect=w/16:h/9:11*w/16:h/9,swaprect=w/16:h/9:w/8:7*h/9,swaprect=w/16:h/9:3*w/16:h/3,swaprect=w/16:h/9:3*w/8:0,swaprect=w/16:h/9:w/4:4*h/9,swaprect=w/16:h/9:7*w/16:2*h/3,swaprect=w/16:h/9:7*w/8:8*h/9,swaprect=w/16:h/9:3*w/4:7*h/9,swaprect=w/16:h/9:3*w/4:5*h/9,swaprect=w/16:h/9:3*w/8:h/9,swaprect=w/16:h/9:3*w/16:0,swaprect=w/16:h/9:15*w/16:2*h/3,swaprect=w/16:h/9:13*w/16:7*h/9,swaprect=w/16:h/9:9*w/16:h/9,swaprect=w/16:h/9:0:2*h/3,swaprect=w/16:h/9:15*w/16:h/9,swaprect=w/16:h/9:w/16:h/9,swaprect=w/16:h/9:7*w/16:0,swaprect=w/16:h/9:13*w/16:4*h/9,swaprect=w/16:h/9:5*w/16:4*h/9,swaprect=w/16:h/9:5*w/16:8*h/9,swaprect=w/16:h/9:w/16:4*h/9,swaprect=w/16:h/9:7*w/16:7*h/9,swaprect=w/16:h/9:3*w/4:4*h/9,swaprect=w/16:h/9:7*w/8:2*h/9,swaprect=w/16:h/9:5*w/8:2*h/3,swaprect=w/16:h/9:9*w/16:0,swaprect=w/16:h/9:w/4:8*h/9,swaprect=w/16:h/9:3*w/16:2*h/3,swaprect=w/16:h/9:w/8:5*h/9,swaprect=w/16:h/9:5*w/16:5*h/9,swaprect=w/16:h/9:w/8:h/9,swaprect=w/16:h/9:w/16:0,swaprect=w/16:h/9:5*w/8:8*h/9,swaprect=w/16:h/9:5*w/8:7*h/9,swaprect=w/16:h/9:w/2:7*h/9,swaprect=w/16:h/9:0:0,swaprect=w/16:h/9:w/16:2*h/3,swaprect=w/16:h/9:15*w/16:h/3,swaprect=w/16:h/9:3*w/8:2*h/3,swaprect=w/16:h/9:7*w/16:8*h/9,swaprect=w/16:h/9:3*w/16:2*h/9,swaprect=w/16:h/9:13*w/16:h/3,swaprect=w/16:h/9:w/4:h/3,swaprect=w/16:h/9:15*w/16:8*h/9,swaprect=w/16:h/9:w/2:5*h/9,swaprect=w/16:h/9:11*w/16:0,swaprect=w/16:h/9:13*w/16:2*h/3,swaprect=w/16:h/9:w/8:0,swaprect=w/16:h/9:0:7*h/9,swaprect=w/16:h/9:w/2:4*h/9,swaprect=w/16:h/9:3*w/4:8*h/9,swaprect=w/16:h/9:9*w/16:8*h/9,swaprect=w/16:h/9:7*w/8:h/9,swaprect=w/16:h/9:w/2:2*h/9,swaprect=w/16:h/9:11*w/16:2*h/3,swaprect=w/16:h/9:0:h/9,swaprect=w/16:h/9:15*w/16:0,swaprect=w/16:h/9:15*w/16:5*h/9,swaprect=w/16:h/9:7*w/16:5*h/9,swaprect=w/16:h/9:15*w/16:7*h/9,swaprect=w/16:h/9:7*w/16:h/9,swaprect=w/16:h/9:3*w/4:h/3,swaprect=w/16:h/9:3*w/16:h/9,swaprect=w/16:h/9:7*w/8:0,swaprect=w/16:h/9:13*w/16:5*h/9,swaprect=w/16:h/9:7*w/8:2*h/3,swaprect=w/16:h/9:w/4:h/9,swaprect=w/16:h/9:3*w/16:8*h/9,swaprect=w/16:h/9:w/16:h/3,swaprect=w/16:h/9:w/8:2*h/9,swaprect=w/16:h/9:11*w/16:2*h/9,swaprect=w/16:h/9:5*w/16:0,swaprect=w/16:h/9:3*w/16:5*h/9,swaprect=w/16:h/9:3*w/8:5*h/9,swaprect=w/16:h/9:w/8:h/3,swaprect=w/16:h/9:9*w/16:h/3,swaprect=w/16:h/9:7*w/16:h/3,swaprect=w/16:h/9:3*w/8:h/3,swaprect=w/16:h/9:5*w/8:0,swaprect=w/16:h/9:3*w/4:2*h/9,swaprect=w/16:h/9:0:2*h/9,swaprect=w/16:h/9:w/2:0,swaprect=w/16:h/9:w/8:2*h/3,swaprect=w/16:h/9:w/4:7*h/9,swaprect=w/16:h/9:3*w/8:7*h/9,swaprect=w/16:h/9:9*w/16:7*h/9,swaprect=w/16:h/9:w/16:7*h/9,swaprect=w/16:h/9:0:h/3,swaprect=w/16:h/9:3*w/8:8*h/9,swaprect=w/16:h/9:0:5*h/9,swaprect=w/16:h/9:3*w/16:4*h/9,swaprect=w/16:h/9:7*w/8:4*h/9,swaprect=w/16:h/9:11*w/16:4*h/9,swaprect=w/16:h/9:w/16:2*h/9,swaprect=w/16:h/9:13*w/16:0,swaprect=w/16:h/9:w/2:2*h/3,swaprect=w/16:h/9:7*w/8:5*h/9,swaprect=w/16:h/9:w/4:5*h/9,swaprect=w/16:h/9:3*w/8:4*h/9,swaprect=w/16:h/9:w/4:2*h/3,swaprect=w/16:h/9:5*w/8:4*h/9,swaprect=w/16:h/9:5*w/16:2*h/3,swaprect=w/16:h/9:13*w/16:2*h/9,swaprect=w/16:h/9:5*w/16:7*h/9,swaprect=w/16:h/9:3*w/4:0,swaprect=w/16:h/9:3*w/4:2*h/3,swaprect=w/16:h/9:w/16:8*h/9,swaprect=w/16:h/9:5*w/16:h/3,swaprect=w/16:h/9:9*w/16:2*h/3,swaprect=w/16:h/9:w/8:4*h/9,swaprect=w/16:h/9:w/4:2*h/9,swaprect=w/16:h/9:9*w/16:4*h/9,swaprect=w/16:h/9:13*w/16:h/9,swaprect=w/16:h/9:5*w/16:h/9,swaprect=w/16:h/9:3*w/16:7*h/9,swaprect=w/16:h/9:15*w/16:4*h/9,swaprect=w/16:h/9:7*w/8:7*h/9,swaprect=w/16:h/9:11*w/16:h/3,swaprect=w/16:h/9:0:4*h/9,swaprect=w/16:h/9:11*w/16:8*h/9,swaprect=w/16:h/9:5*w/8:5*h/9,swaprect=w/16:h/9:3*w/8:2*h/9,swaprect=w/16:h/9:0:8*h/9,swaprect=w/16:h/9:w/2:h/3,swaprect=w/16:h/9:9*w/16:5*h/9,swaprect=w/16:h/9:5*w/8:h/3,swaprect=w/16:h/9:3*w/4:h/9,swaprect=w/16:h/9:13*w/16:8*h/9,swaprect=w/16:h/9:7*w/8:h/3,swaprect=w/16:h/9:w/2:8*h/9,swaprect=w/16:h/9:7*w/16:2*h/9,swaprect=w/16:h/9:w/16:5*h/9", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//
//        String umetak = "y=val*5";
//        // only can be exported to mkv
//        String[] noiseCommand = {"-i", videoModel.getPath(), "-codec:v", "huffyuv", "-bsf:v", "noise=100000000", "-codec:a", "copy", newPath};
//        String[] crazyCommand = {"-i", videoModel.getPath(), "-vf", "rotate=PI/6,stereo3d=abl:sbsr,stereo3d=sbsl:aybd,split [main][tmp]; [tmp] crop=iw:ih/2:0:0, vflip [flip]; [main][flip] overlay=0:H/2,crop=iw/2:ih:0:0,split[left][tmp];[tmp]hflip[right];[left][right] hstack", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] mirorCommand = {"-i", videoModel.getPath(), "-vf", "split [main][tmp]; [tmp] crop=iw:ih/2:0:0, vflip [flip]; [main][flip] overlay=0:H/2", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//        String[] cartoonCommand = {"-i", videoModel.getPath(), "-vf", "split [main][tmp]; [tmp] lutyuv=" + umetak + " [tmp2]; [main][tmp2] overlay", "-pix_fmt", "yuv420p", "-y", "-c:a", "copy", newPath};
//
//
////        execFFmpegBinary(complexCommand, newPath, nameFile);
//        CommandModel commandModel = (CommandModel) CommandsUtil.commands.get(lastPressedGridViewItem);
//        long videoDuration = Long.parseLong(videoModel.getDuration()) / 1000;
//        if (!commandModel.getEffectName().contains("Fade"))
//        initDialogProgress();
//
//        FabricEvents.logEvent(FabricEvents.EFFECT_MENU_PRESSED, FabricEvents.EFFECT_MENU_OPTION, commandModel.getEffectName());
//
//        switch (commandModel.getEffectName()) {
//
//            case "Boomerang":
////                boolean executThisCommand = Long.parseLong(videoModel.getDuration()) / 1000 > 4;
//                if (Long.parseLong(videoModel.getDuration()) / 1000 > 4) {
//
//                    new AlertDialog.Builder(getContext())
//                            .setTitle("Boomerang")
//                            .setMessage(getString(R.string.no_longer_than))
//                            .setPositiveButton("Ok", null).show();
//
//                    if (progressDialog != null) {
//                        progressDialog.dismiss();
//                    }
//                } else
//                    execFFmpegBinary(boomerangCommand, newPath, nameFile);
//                break;
//            case "Black And White":
//                execFFmpegBinary(blackAndWhiteCommand, newPath, nameFile);
//                break;
//            case "Sepia":
//                execFFmpegBinary(sepiaCommand, newPath, nameFile);
//                break;
//            case "Gamma Correction":
//                execFFmpegBinary(gammaCorrenctionCommand, newPath, nameFile);
//                break;
//            case "Stabilization":
//                execFFmpegBinary(stabilizationCommand, newPath, nameFile);
//                break;
//            case "Sharper":
//                execFFmpegBinary(sharpCommand, newPath, nameFile);
//                break;
//            case "Zoom In Progressively":
//                MediaMetadataRetriever retriever = new MediaMetadataRetriever();
//                retriever.setDataSource(videoModel.getPath());
//                int width = Integer.valueOf(retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH));
//                int height = Integer.valueOf(retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT));
//                retriever.release();
//                if (height > width) {
//                    new AlertDialog.Builder(getContext())
//                            .setTitle(getString(R.string.zoom_in_progressively))
//                            .setMessage(getString(R.string.supports_horizontal_orientation))
//                            .setPositiveButton("Ok", new DialogInterface.OnClickListener() {
//                                @Override
//                                public void onClick(DialogInterface dialogInterface, int i) {
//                                    dialogInterface.dismiss();
//                                    execFFmpegBinary(zoomInProgressivelyCommand, newPath, nameFile);
//
//                                }
//                            })
//                            .setNegativeButton(R.string.cancel, new DialogInterface.OnClickListener() {
//                                @Override
//                                public void onClick(DialogInterface dialogInterface, int i) {
//                                    dialogInterface.dismiss();
//                                    if (progressDialog != null) {
//                                        progressDialog.dismiss();
//                                    }
//                                }
//                            })
//                            .show();
//                } else
//                    execFFmpegBinary(zoomInProgressivelyCommand, newPath, nameFile);
//                break;
//            case "Flip Vertical":
//                execFFmpegBinary(flipVerticalCommand, newPath, nameFile);
//                break;
//            case "Flip Horizontal":
//                execFFmpegBinary(flipHorizontalCommand, newPath, nameFile);
//                break;
//            case "Rotate 90 Degr Clockwise":
//                execFFmpegBinary(rotate90DegreesClockwiseCommand, newPath, nameFile);
//                break;
//            case "Rotate 90 Degr Counterclockwise":
//                execFFmpegBinary(rotate90DegreesCounterclockwiseCommand, newPath, nameFile);
//                break;
//            case "Blur":
//                execFFmpegBinary(blurCommand, newPath, nameFile);
//                break;
//            case "Convolution":
//                execFFmpegBinary(convolutionCommand, newPath, nameFile);
//                break;
//            case "Convolution 2":
//                execFFmpegBinary(convolution2Command, newPath, nameFile);
//                break;
//            case "Stereoscopic":
//                execFFmpegBinary(stereoscopicCommand, newPath, nameFile);
//                break;
//            case "Pixelize":
//                execFFmpegBinary(pixelizeCommand, newPath, nameFile);
//                break;
//            case "Brightness Plus":
//                execFFmpegBinary(brightnessPlusCommand, newPath, nameFile);
//                break;
//            case "Brightness Minus":
//                execFFmpegBinary(brightnessMinusCommand, newPath, nameFile);
//                break;
//            case "Saturation Plus":
//                execFFmpegBinary(saturationPlusCommand, newPath, nameFile);
//                break;
//            case "Saturation Minus":
//                execFFmpegBinary(saturationMinusCommand, newPath, nameFile);
//                break;
//            case "Gamma R Plus":
//                execFFmpegBinary(gammaRPlusCommand, newPath, nameFile);
//                break;
//            case "Gamma R Minus":
//                execFFmpegBinary(gammaRMinusCommand, newPath, nameFile);
//                break;
//            case "Gamma G Plus":
//                execFFmpegBinary(gammaGPlusCommand, newPath, nameFile);
//                break;
//            case "Gamma G Minus":
//                execFFmpegBinary(gammaGMinusCommand, newPath, nameFile);
//                break;
//            case "Gamma B Plus":
//                execFFmpegBinary(gammaBPlusCommand, newPath, nameFile);
//                break;
//            case "Gamma B Minus":
//                execFFmpegBinary(gammaBMinusCommand, newPath, nameFile);
//                break;
//            case "Color Balance":
//                execFFmpegBinary(colorbalanceCommand, newPath, nameFile);
//                break;
//            case "Color Balance Two":
//                execFFmpegBinary(colorbalance2Command, newPath, nameFile);
//                break;
//            case "Color Negative":
//                execFFmpegBinary(colorNegativeCommand, newPath, nameFile);
//                break;
//            case "Cross Process":
//                execFFmpegBinary(crossProcessCommand, newPath, nameFile);
//                break;
//            case "Darker":
//                execFFmpegBinary(darkerCommand, newPath, nameFile);
//                break;
//            case "Lighter":
//                execFFmpegBinary(lighterCommand, newPath, nameFile);
//                break;
//            case "Increase Contrast":
//                execFFmpegBinary(increaseContrastCommand, newPath, nameFile);
//                break;
//            case "Linear Contrast":
//                execFFmpegBinary(linearContrastCommand, newPath, nameFile);
//                break;
//            case "Medium Contrast":
//                execFFmpegBinary(mediumContrastCommand, newPath, nameFile);
//                break;
//            case "Strong Contrast":
//                execFFmpegBinary(strongContrastCommand, newPath, nameFile);
//                break;
//            case "Negative":
//                execFFmpegBinary(negativeCommand, newPath, nameFile);
//                break;
//            case "Curves Blue":
//                execFFmpegBinary(curvesBlueCommand, newPath, nameFile);
//                break;
//            case "Enhanced LBG 2":
//                execFFmpegBinary(elbg2Command, newPath, nameFile);
//                break;
//            case "Enhanced LBG 4":
//                execFFmpegBinary(elbg4Command, newPath, nameFile);
//                break;
//            case "Enhanced LBG 8":
//                execFFmpegBinary(elbg8Command, newPath, nameFile);
//                break;
//            case "Enhanced LBG 16":
//                execFFmpegBinary(elbg16Command, newPath, nameFile);
//                break;
//            case "Deband":
//                execFFmpegBinary(debandCommand, newPath, nameFile);
//                break;
//            case "Swap Rect One":
//                execFFmpegBinary(swapRect6x6ver1Command, newPath, nameFile);
//                break;
//            case "Swap Rect Two":
//                execFFmpegBinary(swapRect6x6ver2Command, newPath, nameFile);
//                break;
//            case "Swap Rect Three":
//                execFFmpegBinary(swapRect10x6ver1Command, newPath, nameFile);
//                break;
//            case "Swap Rect Four":
//                execFFmpegBinary(swapRect10x6ver2Command, newPath, nameFile);
//                break;
//            case "Swap Rect Five":
//                execFFmpegBinary(swapRect16x9ver1Command, newPath, nameFile);
//                break;
//            case "Swap Rect Six":
//                execFFmpegBinary(swapRect16x9ver2Command, newPath, nameFile);
//                break;
//            case "Noise":
//                execFFmpegBinary(noiseCommand, newPath, nameFile);
//                break;
//            case "Crazy":
//                execFFmpegBinary(crazyCommand, newPath, nameFile);
//                break;
//            case "Mirror":
//                execFFmpegBinary(mirorCommand, newPath, nameFile);
//                break;
//            case "Cartoon":
//                execFFmpegBinary(cartoonCommand, newPath, nameFile);
//                break;
//            case "Zoomed":
//                execFFmpegBinary(zoomInCommand, newPath, nameFile);
//                break;
//            case "Vintage":
//                execFFmpegBinary(vintageCommand, newPath, nameFile);
//                break;
//            case "Fade in":
//                executFadeCommand(nameFile, commandModel, videoDuration);
//                break;
//            case "Fade out":
//                executFadeCommand(nameFile, commandModel, videoDuration);
//                break;
//
//        }
//
//
//    }
//
//    private void executFadeCommand(String nameFile, CommandModel commandModel, long videoDuration) {
//
//        AlertDialog.Builder builder =
//                new AlertDialog.Builder(getContext(), R.style.MyCustomTheme);
//        LayoutInflater inflater = ((Activity) getContext()).getLayoutInflater();
//        View v = inflater.inflate(R.layout.number_picker, null);
//        TextView effectNameTV = v.findViewById(R.id.numberPickerTitleTV);
//        effectNameTV.setText(commandModel.getEffectName());
//
//        //---------------------- option one ---------------------------------
//        TextView FirstOptionTitleTV = v.findViewById(R.id.FirstOptionTitleTV);
//        FirstOptionTitleTV.setText(commandModel.getEffectName().equalsIgnoreCase("Fade in") ? getString(R.string.fade_in_duration) : getString(R.string.fade_out_duration));
//        TextView optionOneTV = v.findViewById(R.id.optionOneTV);
//        ImageView optionOneAddIV = v.findViewById(R.id.optionOneAddIV);
//        ViewPropertyAnimatorListener viewPropertyAnimatorListenerOptionOneAddIV = new ViewPropertyAnimatorListener() {
//            @Override
//            public void onAnimationStart(View view) {
//
//            }
//
//            @Override
//            public void onAnimationEnd(View view) {
//                if (Integer.parseInt(optionOneTV.getText().toString()) + 1 > videoDuration) {
//                    Toast.makeText(getContext(), getString(R.string.cant_be_gratter_than) + videoDuration, Toast.LENGTH_SHORT).show();
//                    return;
//                }
//                optionOneTV.setText(String.valueOf(Integer.parseInt(optionOneTV.getText().toString()) + 1));
//            }
//
//            @Override
//            public void onAnimationCancel(View view) {
//
//            }
//        };
//        optionOneAddIV.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View view) {
//
//                animateView(view, viewPropertyAnimatorListenerOptionOneAddIV);
//
//            }
//        });
//        ImageView optionOneRemoveIV = v.findViewById(R.id.optionOneRemoveIV);
//        optionOneRemoveIV.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View view) {
//
//                ViewPropertyAnimatorListener viewPropertyAnimatorListenerOptionOneRemoveIV = new ViewPropertyAnimatorListener() {
//                    @Override
//                    public void onAnimationStart(final View view) {
//
//                    }
//
//                    @Override
//                    public void onAnimationEnd(final View view) {
//                        if ((Integer.parseInt(optionOneTV.getText().toString()) - 1) < 1) {
//                            Toast.makeText(getContext(),getString(R.string.must_be_grater_than_null), Toast.LENGTH_SHORT).show();
//                            return;
//                        }
//                        optionOneTV.setText(String.valueOf(Integer.parseInt(optionOneTV.getText().toString()) - 1));
//                    }
//
//                    @Override
//                    public void onAnimationCancel(final View view) {
//
//                    }
//                };
//
//                animateView(optionOneRemoveIV, viewPropertyAnimatorListenerOptionOneRemoveIV);
//
//
//            }
//        });
//
//        //---------------------- option one ---------------------------------
//
//
//        //---------------------- option two ---------------------------------
//        TextView SecondOptionTitleTV = v.findViewById(R.id.SecondOptionTitleTV);
////        SecondOptionTitleTV.setText("Audio fade in duration");
//        SecondOptionTitleTV.setText(commandModel.getEffectName().equalsIgnoreCase("Fade in") ? getString(R.string.audio_fade_in_duration) : getString(R.string.audio_fade_out_duration));
//        TextView optionTwoTV = v.findViewById(R.id.optionTwoTV);
//        ImageView SecondOptionAddIV = v.findViewById(R.id.SecondOptionAddIV);
//        SecondOptionAddIV.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View view) {
//
//                ViewPropertyAnimatorListener viewPropertyAnimatorListenerSecondOptionAddIV = new ViewPropertyAnimatorListener() {
//                    @Override
//                    public void onAnimationStart(final View view) {
//
//                    }
//
//                    @Override
//                    public void onAnimationEnd(final View view) {
//                        if (Integer.parseInt(optionTwoTV.getText().toString()) + 1 > videoDuration) {
//                            Toast.makeText(getContext(), getString(R.string.cant_be_gratter_than)+ videoDuration, Toast.LENGTH_SHORT).show();
//                            return;
//                        }
//                        optionTwoTV.setText(String.valueOf(Integer.parseInt(optionTwoTV.getText().toString()) + 1));
//                    }
//
//                    @Override
//                    public void onAnimationCancel(final View view) {
//
//                    }
//                };
//
//                animateView(SecondOptionAddIV, viewPropertyAnimatorListenerSecondOptionAddIV);
//
//            }
//        });
//        ImageView optionTwoRemoveIV = v.findViewById(R.id.optionTwoRemoveIV);
//        optionTwoRemoveIV.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View view) {
//
//                ViewPropertyAnimatorListener viewPropertyAnimatorListenerOptionTwoRemoveIV = new ViewPropertyAnimatorListener() {
//                    @Override
//                    public void onAnimationStart(final View view) {
//
//                    }
//
//                    @Override
//                    public void onAnimationEnd(final View view) {
//                        if ((Integer.parseInt(optionTwoTV.getText().toString()) - 1) < 1) {
//                            Toast.makeText(getContext(), getString(R.string.must_be_grater_than_null), Toast.LENGTH_SHORT).show();
//                            return;
//                        }
//                        optionTwoTV.setText(String.valueOf(Integer.parseInt(optionTwoTV.getText().toString()) - 1));
//                    }
//
//                    @Override
//                    public void onAnimationCancel(final View view) {
//
//                    }
//                };
//
//                animateView(optionTwoRemoveIV, viewPropertyAnimatorListenerOptionTwoRemoveIV);
//
//            }
//        });
//        //---------------------- option two ---------------------------------
//
//        builder.setView(v);
//
//        AlertDialog alertDialog = builder.show();
////                Window window = alertDialog.getWindow();
////                window.setLayout(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT);
////                            builder.show();
//
//        ImageView doneNumberPickerIV = v.findViewById(R.id.doneNumberPickerIV);
//        ViewPropertyAnimatorListener viewPropertyAnimatorListenerDoneNumberPickerIV = new ViewPropertyAnimatorListener() {
//            @Override
//            public void onAnimationStart(final View view) {
//
//            }
//
//            @Override
//            public void onAnimationEnd(final View view) {
//                alertDialog.dismiss();
//                initDialogProgress();
//
//                int audioFadeInDuration = Integer.parseInt(optionTwoTV.getText().toString());
//                int videoFadeInDuration = Integer.parseInt(optionOneTV.getText().toString());
//                String[] fadeInCommand = {"-i", videoModel.getPath(), "-filter_complex", "fade=in:st=0:d=" + videoFadeInDuration + "; afade=in:st=0:d=" + audioFadeInDuration, "-c:v", "libx264", "-c:a", "aac", newPath};
//                String[] fadeOutCommand = {"-i", videoModel.getPath(), "-filter_complex", "fade=out:st=" + (videoDuration - videoFadeInDuration)+ ":d=" + videoFadeInDuration + "; afade=out:st=" + (videoDuration -audioFadeInDuration) + ":d=" + audioFadeInDuration, "-c:v", "libx264", "-c:a", "aac", newPath};
//
//                execFFmpegBinary(commandModel.getEffectName().equalsIgnoreCase("Fade in") ? fadeInCommand : fadeOutCommand, newPath, nameFile);
//            }
//
//            @Override
//            public void onAnimationCancel(final View view) {
//
//            }
//        };
//        doneNumberPickerIV.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View view) {
//
//
//                animateView(view, viewPropertyAnimatorListenerDoneNumberPickerIV);
//
//
//            }
//        });
//        alertDialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND); // This flag is required to set otherwise the setDimAmount method will not show any effect
//        alertDialog.getWindow().setDimAmount(0.9f);
//    }
//
//    private void animateView(View view, ViewPropertyAnimatorListener viewPropertyAnimatorListener) {
//        ViewCompat.animate(view)
//                .setDuration(200)
//                .scaleX(0.9f)
//                .scaleY(0.9f)
//                .setInterpolator(new CycleInterpolator())
//                .setListener(viewPropertyAnimatorListener)
//                .withLayer()
//                .start();
//    }
//
//
//    private void initDialogProgress() {
//        progressDialog = new ProgressDialog(getContext());
//        progressDialog.setCancelable(false);
//        progressDialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
//        progressDialog.setTitle(getString(R.string.progress_dialog_saving));
//        progressDialog.setProgress(0);
//        progressDialog.setButton(DialogInterface.BUTTON_NEGATIVE, getString(R.string.cancel), (dialog, which) -> cancelCreateFile());
//        progressDialog.show();
//
//    }
//
//    private boolean isCancelSaveFile = false;
//
//    private void cancelCreateFile() {
//        isCancelSaveFile = true;
//
//        if (ffmpeg.isFFmpegCommandRunning()) {
//            ffmpeg.killRunningProcesses();
//        }
//
//        if (newPath != null) {
//            new File(newPath).delete();
//        }
//
//        if (progressDialog != null) {
//            progressDialog.dismiss();
//        }
//    }
//
//
//    private void execFFmpegBinary(final String[] command, String path, String title) {
//
//
//        try {
//            ffmpeg.execute(command, new ExecuteBinaryResponseHandler() {
//                @Override
//                public void onFailure(String s) {
//                    Flog.e("Successs     " + s);
//                    if (showToast)
//                        Toast.makeText(getContext(), getString(R.string.can_not_create_file), Toast.LENGTH_SHORT).show();
//                    if (progressDialog != null) {
//                        progressDialog.dismiss();
//                    }
//                    generateNextThumbnail();
//                }
//
//                @Override
//                public void onSuccess(String s) {
//                    Flog.e("Failllllllll   " + s);
//                    if (isCancelSaveFile) return;
//
//                    if (progressDialog != null) {
//                        progressDialog.setProgress(100);
//                        progressDialog.dismiss();
//                    }
//
////                    if (isSaveFile){
////                        isSaveFile = false;
//
//                    if (generateNextThumbnail()) return;
//
//                    FileUtil.addFileToContentProvider(getContext(), path, title);
//
//                    Toast.makeText(getContext(), getString(R.string.create_file) + ": " + path, Toast.LENGTH_SHORT).show();
//
//                    if (isPauseFragment()) {
//                        return;
//                    }
//
//                    Utils.clearFragment(getFragmentManager());
//
//                    getContext().sendBroadcast(new Intent(Statistic.OPEN_EFFECSTS_STUDIO));
//
//                }
//
//                @Override
//                public void onProgress(String s) {
//                    Flog.e(s);
//                    double durationFile = (int) Utils.getProgress(s, Long.parseLong(videoModel.getDuration()) / 1000) * tempoVideo;
//                    double percent = durationFile / (Double.parseDouble(videoModel.getDuration()) / 1000);
//                    Log.e("xxx", " durrrrrr  " + durationFile + "___" + percent * 100);
//                    if (progressDialog != null) {
//                        if ((int) (percent * 100) > 0) {
//                            progressDialog.setProgress((int) (percent * 100));
//                        }
//                    }
//                }
//
//                @Override
//                public void onStart() {
//
//                }
//
//                @Override
//                public void onFinish() {
//
//                }
//            });
//
//        } catch (FFmpegCommandAlreadyRunningException e) {
//            e.printStackTrace();
//        }
//    }
//
//    private boolean generateNextThumbnail() {
//        if (isInitThumb) {
//
//            if (CommandsUtil.COUNTER < CommandsUtil.commands.size() - 1) {
//                CommandsUtil.COUNTER++;
//                CommandModel commandModel = (CommandModel) CommandsUtil.commands.get(CommandsUtil.COUNTER);
//                execFFmpegBinary(commandModel.getCommand(), commandModel.getPreviewImagePath(), commandModel.getFileNamePreview());
//                imageId.add(commandModel.getPreviewImagePath());
//                web.add(commandModel.getEffectName());
//
//            } else {
//                isInitThumb = false;
//                new Handler(Looper.getMainLooper()).post(new Runnable() {
//                    @Override
//                    public void run() {
//                        FilterAdapter adapter = new FilterAdapter(getContext(), web,
//                                imageId);
//                        GridView grid = (GridView) findViewById(R.id.grid_view);
//                        grid.setAdapter(adapter);
//                        grid.setOnItemClickListener(new AdapterView.OnItemClickListener() {
//
//                            @Override
//                            public void onItemClick(AdapterView<?> parent, View view,
//                                                    int position, long id) {
////                                            if (grid.getChildAt(position) != null){
////
////                                                grid.getChildAt(lastPressedGridViewItem).setBackgroundColor(getContext().getResources().getColor(R.color.colorPrimaryDark));
////                                                grid.getChildAt(position).setBackgroundColor(getContext().getResources().getColor(R.color.high_light));
////                                            }
//                                lastPressedGridViewItem = position;
//                                CommandModel commandModel = (CommandModel) CommandsUtil.commands.get(position);
//
//                                AlertDialog.Builder builder =
//                                        new AlertDialog.Builder(getContext(), R.style.MyCustomTheme);
//                                LayoutInflater inflater = ((Activity) getContext()).getLayoutInflater();
//                                View v = inflater.inflate(R.layout.image_preview_fragment, null);
//                                TextView effectNameTV = v.findViewById(R.id.effectNameTV);
//                                effectNameTV.setText(commandModel.getEffectName());
//                                ImageView filterPreviewImageView = v.findViewById(R.id.filterPreviewImageView);
//                                Glide.with(getContext())
//                                        .load(commandModel.getPreviewImagePath())
//                                        .into(filterPreviewImageView);
//                                builder.setView(v);
//                                AlertDialog alertDialog = builder.show();
////                            builder.show();
//                                v.findViewById(R.id.saveImagePreviewDialog).setOnClickListener(new View.OnClickListener() {
//                                    @Override
//                                    public void onClick(View view) {
//                                        alertDialog.dismiss();
//                                        dialogLocalSave();
//                                    }
//                                });
//                                alertDialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND); // This flag is required to set otherwise the setDimAmount method will not show any effect
//                                alertDialog.getWindow().setDimAmount(0.9f);
//                            }
//                        });
//                    }
//                });
//                progressDialogInitThumbnailWithPreview.dismiss();
//            }
//
//            return true;
//        }
//        return false;
//    }
//
//    @Override
//    public void onDestroy() {
////        NiceVideoPlayerManager.instance().releaseNiceVideoPlayer();
//        super.onDestroy();
//    }
//
//    @Override
//    public void onCancelSelect() {
//        if (dialogInputName != null) {
//            dialogInputName.hideDialog();
//        }
//    }
//
//    @Override
//    public void onFileNameEmpty() {
//        Toast.makeText(getContext(), getString(R.string.error), Toast.LENGTH_SHORT).show();
//    }
//
//    @Override
//    public void onFileNameHasSpecialCharacter() {
//        Toast.makeText(getContext(), getString(R.string.name_file_can_not_contain_character), Toast.LENGTH_SHORT).show();
//    }
//
//
//    @Override
//    public void onProgressChanged(com.videomontage.freeeditingapps.custom.BubbleSeekBar bubbleSeekBar, int progress, float progressFloat, boolean fromUser) {
//
//    }
//
//    @Override
//    public void getProgressOnActionUp(com.videomontage.freeeditingapps.custom.BubbleSeekBar bubbleSeekBar, int progress, float progressFloat) {
//
//    }
//
//    @Override
//    public void getProgressOnFinally(com.videomontage.freeeditingapps.custom.BubbleSeekBar bubbleSeekBar, int progress, float progressFloat, boolean fromUser) {
//
//    }
//
//    @Override
//    public void onRestart() {
//    }
//}
