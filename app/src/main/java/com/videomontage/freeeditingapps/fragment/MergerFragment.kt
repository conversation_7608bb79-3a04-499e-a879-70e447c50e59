package com.videomontage.freeeditingapps.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.videomontage.freeeditingapps.R

/**
 * Created by <PERSON> on 11/15/2018.
 */
class MergerFragment : AbsFragment() {
    override fun initViews() {}
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_merger, container, false)
    }

    companion object {
        fun newInstance(bundle: Bundle?): MergerFragment {
            val fragment = MergerFragment()
            fragment.setArguments(bundle)
            return fragment
        }
    }
}
