package com.videomontage.freeeditingapps.fragment

import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.Outline
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.Message
import android.util.Log
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.ViewOutlineProvider
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.core.content.FileProvider
import androidx.core.view.ViewCompat
import androidx.core.view.ViewPropertyAnimatorListener
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import cn.Ragnarok.BitmapFilter
import com.arthenica.mobileffmpeg.Config
import com.arthenica.mobileffmpeg.ExecuteCallback
import com.arthenica.mobileffmpeg.FFmpeg
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.source.LoopingMediaSource
import com.google.android.exoplayer2.source.MediaSource
import com.google.android.exoplayer2.source.ProgressiveMediaSource
import com.google.android.exoplayer2.ui.PlayerView
import com.google.android.exoplayer2.upstream.DefaultDataSourceFactory
import com.videomontage.freeeditingapps.BuildConfig
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.activity.MainActivity
import com.videomontage.freeeditingapps.adapter.OptionsAdapter
import com.videomontage.freeeditingapps.analytics.AmplitudeHelper
import com.videomontage.freeeditingapps.analytics.FragmentAnalytics
import com.videomontage.freeeditingapps.analytics.TimeWatch
import com.videomontage.freeeditingapps.application.MyApplication
import com.videomontage.freeeditingapps.listener.IInputNameFile
import com.videomontage.freeeditingapps.model.ComModel
import com.videomontage.freeeditingapps.model.VideoModel
import com.videomontage.freeeditingapps.sorting.PixelSortingContext
import com.videomontage.freeeditingapps.sorting.PixelSortingContext.OnImageSavedListener
import com.videomontage.freeeditingapps.sorting.filter.Filter
import com.videomontage.freeeditingapps.sorting.filter.FilterDB
import com.videomontage.freeeditingapps.statistic.Statistic
import com.videomontage.freeeditingapps.utils.ColorFilters
import com.videomontage.freeeditingapps.utils.CycleInterpolator
import com.videomontage.freeeditingapps.utils.EffectsConstants
import com.videomontage.freeeditingapps.utils.FabricEvents
import com.videomontage.freeeditingapps.utils.FileUtil
import com.videomontage.freeeditingapps.utils.FilterManager
import com.videomontage.freeeditingapps.utils.Flog
import com.videomontage.freeeditingapps.utils.GlitchEffect
import com.videomontage.freeeditingapps.utils.ImageUtil
import com.videomontage.freeeditingapps.utils.StorageUtils
import com.videomontage.freeeditingapps.utils.Utils
import com.videomontage.freeeditingapps.utils.VideoUtil
import com.videomontage.freeeditingapps.view.CustomDialog
import com.videomontage.freeeditingapps.view.FinalDialog
import com.videomontage.freeeditingapps.view.FinalDialog.FinalDialogListener
import com.videomontage.freeeditingapps.view.ThresholdBottomSheet
import com.videomontage.freeeditingapps.view.ThresholdBottomSheet.BottomSheetClickeListener
import org.apache.commons.io.comparator.LastModifiedFileComparator
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.text.SimpleDateFormat
import java.util.Arrays
import java.util.Locale
import java.util.Random
import java.util.concurrent.TimeUnit

class MoreOptionsFragment() : AbsFragment(), IInputNameFile {
    private var dialogInputName: DialogInputName? = null

    //    private FFmpeg ffmpeg;
    private var videoModel: VideoModel? = null

    //    private ProgressDialog progressDialog;
    private var customProgressDialog: CustomDialog? = null
    private val simpleDateFormat = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.US)
    private val tempoVideo = 1.0f
    private val ptsVideo = 1.0f
    private var showToast = false
    private var view: View? = null
    private var videoView: PlayerView? = null
    private var player: ExoPlayer? = null
    private var adapter: OptionsAdapter? = null
    private var lastPressedGridViewItem = 0
    private var isPreview = false
    private var ffmpegDone = false
    protected var choice = 0
    private var i = 0
    private var filesNumb = 0
    private var lastReverseCommand: Any? = null

    //    private String[] lastReverseCommand;
    private var handlerRevere: Handler? = null
    private var lastPlayedPath: String? = null
    private var newPath: String? = null
    private val oldPath: String? = null
    private var pencilSketchIndex = 0
    private var lastPickedEffectName: String? = null
    private val preparedEffectsMap: HashMap<String, String> = HashMap()
    private var fileNameForSaving: String? = null
    private var recyclerView: RecyclerView? = null

    //    private ComModel commandModel;
    private var filters: List<Filter>? = null
    private var filterManager: FilterManager? = null
    // private var filterPreviewManager: FilterPreviewManager? = null
    private var gFilterIndex = -1
    private var nonGlitchFrames = 10
    private var glitchFrames = 5
    private var watch: TimeWatch? = null
    private var fragmentAnalytics: FragmentAnalytics? = null
    override fun initViews() {
        watch = TimeWatch.start()
        fragmentAnalytics = FragmentAnalytics("MoreOptionsFragment")
        AmplitudeHelper.setEffectsOpened()
        clearSketchCacheDir()
        clearGlitchDir()
        //        ffmpeg = FFmpeg.getInstance(getContext());
        videoModel = requireArguments().getParcelable(Statistic.VIDEO_MODEL)
        
        // Initialize FilterManager
        filterManager = context?.let { FilterManager(it) }

//        Flog.e("        " + videoModel.getPath());
        toolbar?.setTitle(getString(R.string.effects))
        //        getToolbar().getMenu().findItem(R.id.item_save).setOnMenuItemClickListener(menuItem -> dialogLocalSave());
        toolbar?.let {
            it.getMenu().findItem(R.id.item_save).setOnMenuItemClickListener(
                MenuItem.OnMenuItemClickListener {
                    fragmentAnalytics?.addClick("Save")
                    AmplitudeHelper.setSave()
                    dialogLocalSave()
                    true
                })
        }

//        initVideoView(videoModel.getPath());

        // set up the RecyclerView
        recyclerView = requireView().findViewById(R.id.optionsRv)
        val horizontalLayoutManager =
            LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        recyclerView?.setLayoutManager(horizontalLayoutManager)
        adapter = OptionsAdapter(context, ComModel.getListOfCommands())
        adapter?.setClickListener(object : OptionsAdapter.ItemClickListener {
            override fun onItemClick(view: View?, position: Int) {
                lastPressedGridViewItem = position
                if (lastPressedGridViewItem == 0) {
                    initVideoView(videoModel?.path)
                    return
                }
                
                val effect = ComModel.getListOfCommands()[position]
                
                // Original implementation - directly apply effect
                isPreview = true
                val defaultName = "VS_" + simpleDateFormat.format(System.currentTimeMillis())
                onApplySelect(defaultName)
            }
        })
        
        
        recyclerView?.adapter = adapter
        try {
            filters = filtersFromDB
            //            Log.d("ddd", "Filters size: " + filters.size());
        } catch (e: IOException) {
            e.printStackTrace()
        }
        
        // Start generating previews for effects in background
        generatePreviewsInBackground()
    }
    
    private fun generatePreviewsInBackground() {
        val videoPath = videoModel?.path ?: return
        val effects = ComModel.getListOfCommands()
        
        // Generate previews for all effects that support preview
        val previewableEffects = effects.filter { effect ->
            // Filter out effects that don't need preview
            effect.effectName != ComModel.DEFAULT && 
            !effect.effectName.contains("Input", ignoreCase = true)
        }
        
    }

    private fun initBottomSheet(
        effectName: String,
        inputPath: String,
        outputPath: String?,
        fileName: String
    ) {
        closeProgressDialog()
        val detailsBottomSheet = ThresholdBottomSheet()
        detailsBottomSheet.setBottomSheetClickeListener(object : BottomSheetClickeListener {
            override fun onDismissSubscription(
                thresholdColor: String,
                minThresholdColor: String,
                maxThresholdColor: String
            ) {
                initDialogProgress(true)
                execFFmpegBinary(
                    ComModel.getThresholdCommand(
                        inputPath,
                        outputPath,
                        thresholdColor,
                        minThresholdColor,
                        maxThresholdColor
                    ), outputPath, fileName
                )
            }
        })
        detailsBottomSheet.show(activity, effectName)
    }

    private fun testFinalDialog() {
        val finalDialog = FinalDialog(activity, videoModel?.path, object : FinalDialogListener {
            override fun onDismiss() {}
        })
        Handler().postDelayed(object : Runnable {
            override fun run() {
                finalDialog.show()
            }
        }, 5000)
    }

    private fun initVideoView(path: String?) {
        videoView = requireView().findViewById(R.id.videoView)
        lastPlayedPath = path
        try {
//            new Handler().postDelayed(new Runnable() {
//                @Override
//                public void run() {
            (context as MainActivity?)?.runOnUiThread(object : Runnable {
                override fun run() {
                    if (player == null) {
                        val context = context ?: return@run // or requireContext(), or use your Activity
                        player = ExoPlayer.Builder(context).build()
                        videoView?.setShutterBackgroundColor(Color.TRANSPARENT)
                    }
                    val mediaItem = MediaItem.fromUri(Uri.parse(path))
                    val assetVideo: MediaSource = ProgressiveMediaSource.Factory(
                        DefaultDataSourceFactory(MyApplication.self(), "MyExoplayer")
                    ).createMediaSource(mediaItem)
                    player?.prepare(LoopingMediaSource(assetVideo))
                    videoView?.setPlayer(player)
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                        videoView?.setOutlineProvider(object : ViewOutlineProvider() {
                            override fun getOutline(view: View, outline: Outline) {
                                outline.setRoundRect(0, 0, view.width, view.height, 15f)
                            }
                        })
                        videoView?.setClipToOutline(true)
                    }
                    player?.playWhenReady = true
                }
            })

//                }
//            }, 300);
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onResume() {
        super.onResume()
        if (lastPlayedPath != null) initVideoView(lastPlayedPath) else initVideoView(videoModel?.path)

//        testFinalDialog();
    }

    override fun onPause() {
        super.onPause()
        closeProgressDialog()

//        if (ffmpeg.isFFmpegCommandRunning()) {
//            ffmpeg.killRunningProcesses();
//        }
        if (player != null) {
            player?.playWhenReady = false
            releasePlayer()
        }
        
        // Clean up FilterManager
        filterManager?.release()
    }

    private fun releasePlayer() {
        if (player != null) {
            player?.stop()
            player?.release()
            player = null
        }
    }

    private fun dialogLocalSave(): Boolean {
        context?.let {
            isPreview = false
            val defaultName = "VS_" + simpleDateFormat.format(System.currentTimeMillis())
            dialogInputName = DialogInputName(it, this, defaultName, getString(R.string.save))
            dialogInputName?.initDialog()
        }
        return true
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        view = inflater.inflate(R.layout.fragment_more_options, container, false)
        return view
    }

    override fun onApplySelect(nameFile: String) {
        if (player != null) {
            player?.playWhenReady = false
            releasePlayer()
        }
        saveFile(nameFile)
    }

    private fun splitVideoCommand(path: String) {
        choice = 8
        if (context == null) return
        val moviesDir = StorageUtils.getIndividualCacheDirectory(
            context
        )
        val filePrefix = "split_video"
        val fileExtn = ".mp4"
        val yourRealPath = path
        val dir = File(moviesDir, "VideoSplit")
        if (dir.exists()) Utils.deleteDir(dir)
        dir.mkdir()
        val fileName = "$filePrefix%03d$fileExtn"
        val dest = File(dir, fileName)

//        String[] complexCommand = {"-i", yourRealPath, "-preset", "ultrafast", "-c:v", "libx264", "-crf", "22", "-map", "0", "-segment_time", "6", "-g", "9", "-sc_threshold", "0", "-force_key_frames", "expr:gte(t,n_forced*6)", "-f", "segment", "-an", dest.getAbsolutePath()};
        execFFmpegBinary(
            ComModel.getCommand(ComModel.SPLIT_VIDEO, yourRealPath, dest.absolutePath),
            yourRealPath,
            fileName
        )
    }

    private fun saveFile(nameFile: String) {
        fileNameForSaving = nameFile
        isCancelSaveFile = false
        showToast = true
        val extensionFile: String? = null
        if (FileUtil.isEmpty(nameFile)) {
            Toast.makeText(context, getString(R.string.name_file_can_not_empty), Toast.LENGTH_SHORT)
                .show()
            return
        }
        if (Utils.isStringHasCharacterSpecial(nameFile)) {
            Toast.makeText(
                context,
                getString(R.string.name_file_can_not_contain_character),
                Toast.LENGTH_SHORT
            ).show()
            return
        }
        getPathForSave(nameFile)
        if (File(newPath).exists()) {
            dialogInputName?.hideDialog()
            Toast.makeText(context, getString(R.string.name_file_exist), Toast.LENGTH_SHORT).show()
            return
        }
        val commandModel = ComModel.getListOfCommands()[lastPressedGridViewItem] as ComModel
        val videoDuration = (videoModel?.duration?.toLong() ?: 0) / 1000
        if (!commandModel.effectName.contains("Fade")) if ((commandModel.effectName.equals(
                ComModel.REVERSE_VIDEO,
                ignoreCase = true
            )
                    || commandModel.effectName.contains("Sketch")
                    || commandModel.effectName.contains("Glitch")
                    || commandModel.effectName.contains("Oil")
                    || commandModel.effectName.contains(ComModel.THRESHOLD))
        ) initDialogProgress(true) else initDialogProgress(false)
        FabricEvents.logEvent(
            FabricEvents.EFFECT_MENU_PRESSED,
            FabricEvents.EFFECT_MENU_OPTION,
            commandModel.effectName
        )
        fragmentAnalytics?.addClick(commandModel.effectName)
        AmplitudeHelper.setEffectName(commandModel.effectName)
        lastPickedEffectName = commandModel.effectName
        if (preparedEffectsMap[lastPickedEffectName] != null) {
            if (isPreview) {
                initVideoView(preparedEffectsMap[lastPickedEffectName])
                //                if (progressDialog != null) {
//                    progressDialog.dismiss();
//                }
                closeProgressDialog()
            } else {
                showInterstitialAd()
            }
            return
        }
        when (lastPickedEffectName) {
            ComModel.THRESHOLD -> videoModel?.path?.let {
                initBottomSheet(
                    commandModel.effectName,
                    it,
                    newPath,
                    nameFile
                )
            }

            ComModel.CROSS_FADE -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.CROSS_FADE,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.GLITCH_FILTER_16 -> {
                choice = 12
                gFilterIndex = 16
                processGlitch(nameFile, 24)
            }

            ComModel.GLITCH_FILTER_15 -> {
                choice = 12
                gFilterIndex = 15
                processGlitch(nameFile, 24)
            }

            ComModel.GLITCH_FILTER_14 -> {
                choice = 12
                gFilterIndex = 14
                processGlitch(nameFile, 24)
            }

            ComModel.GLITCH_FILTER_13 -> {
                choice = 12
                gFilterIndex = 13
                processGlitch(nameFile, 24)
            }

            ComModel.GLITCH_FILTER_12 -> {
                choice = 12
                gFilterIndex = 12
                processGlitch(nameFile, 24)
            }

            ComModel.GLITCH_FILTER_11 -> {
                choice = 12
                gFilterIndex = 11
                processGlitch(nameFile, 24)
            }

            ComModel.GLITCH_FILTER_10 -> {
                choice = 12
                gFilterIndex = 10
                processGlitch(nameFile, 24)
            }

            ComModel.GLITCH_FILTER_9 -> {
                choice = 12
                gFilterIndex = 9
                processGlitch(nameFile, 24)
            }

            ComModel.GLITCH_FILTER_8 -> {
                choice = 12
                gFilterIndex = 8
                processGlitch(nameFile, 24)
            }

            ComModel.GLITCH_FILTER_7 -> {
                choice = 12
                gFilterIndex = 7
                processGlitch(nameFile, 24)
            }

            ComModel.GLITCH_FILTER_6 -> {
                choice = 12
                gFilterIndex = 6
                processGlitch(nameFile, 24)
            }

            ComModel.GLITCH_FILTER_5 -> {
                choice = 12
                gFilterIndex = 5
                processGlitch(nameFile, 24)
            }

            ComModel.GLITCH_FILTER_4 -> {
                choice = 12
                gFilterIndex = 4
                processGlitch(nameFile, 24)
            }

            ComModel.GLITCH_FILTER_3 -> {
                choice = 12
                gFilterIndex = 3
                processGlitch(nameFile, 24)
            }

            ComModel.GLITCH_FILTER_2 -> {
                choice = 12
                gFilterIndex = 2
                processGlitch(nameFile, 24)
            }

            ComModel.GLITCH_FILTER_1 -> {
                choice = 12
                gFilterIndex = 1
                processGlitch(nameFile, 24)
            }

            ComModel.GLITCH_FILTER_0 -> {
                choice = 12
                gFilterIndex = 0
                processGlitch(nameFile, 24)
            }

            ComModel.GLITCH -> {
                choice = 11
                processGlitch(nameFile, 24)
            }

            ComModel.OIL_PAINT -> {
                choice = 10
                pencilSketchIndex = 6
                generateSketchPreviewFile()
                if (isFramesExtracted) {
                    onFfmpegSuccessFinished(
                        ComModel.getCommand(
                            ComModel.PENCIL_SKETCH_4,
                            videoModel?.path,
                            newPath
                        ), newPath, nameFile
                    )
                } else {
                    execFFmpegBinary(
                        ComModel.getCommand(
                            ComModel.PENCIL_SKETCH_4,
                            videoModel?.path,
                            newPath
                        ), newPath, nameFile
                    )
                }
            }

            ComModel.PENCIL_SKETCH_5 -> {
                choice = 10
                pencilSketchIndex = 5
                generateSketchPreviewFile()
                if (isFramesExtracted) {
                    onFfmpegSuccessFinished(
                        ComModel.getCommand(
                            ComModel.PENCIL_SKETCH_4,
                            videoModel?.path,
                            newPath
                        ), newPath, nameFile
                    )
                } else {
                    execFFmpegBinary(
                        ComModel.getCommand(
                            ComModel.PENCIL_SKETCH_4,
                            videoModel?.path,
                            newPath
                        ), newPath, nameFile
                    )
                }
            }

            ComModel.PENCIL_SKETCH_3 -> {
                choice = 10
                pencilSketchIndex = 3
                generateSketchPreviewFile()
                if (isFramesExtracted) {
                    onFfmpegSuccessFinished(
                        ComModel.getCommand(
                            ComModel.PENCIL_SKETCH_4,
                            videoModel?.path,
                            newPath
                        ), newPath, nameFile
                    )
                } else {
                    execFFmpegBinary(
                        ComModel.getCommand(
                            ComModel.PENCIL_SKETCH_4,
                            videoModel?.path,
                            newPath
                        ), newPath, nameFile
                    )
                }
            }

            ComModel.PENCIL_SKETCH_2 -> {
                choice = 10
                pencilSketchIndex = 2
                generateSketchPreviewFile()
                if (isFramesExtracted) {
                    onFfmpegSuccessFinished(
                        ComModel.getCommand(
                            ComModel.PENCIL_SKETCH_4,
                            videoModel?.path,
                            newPath
                        ), newPath, nameFile
                    )
                } else {
                    execFFmpegBinary(
                        ComModel.getCommand(
                            ComModel.PENCIL_SKETCH_4,
                            videoModel?.path,
                            newPath
                        ), newPath, nameFile
                    )
                }
            }

            ComModel.PENCIL_SKETCH_1 -> {
                choice = 10
                pencilSketchIndex = 1
                generateSketchPreviewFile()
                if (isFramesExtracted) {
                    onFfmpegSuccessFinished(
                        ComModel.getCommand(
                            ComModel.PENCIL_SKETCH_4,
                            videoModel?.path,
                            newPath
                        ), newPath, nameFile
                    )
                } else {
                    execFFmpegBinary(
                        ComModel.getCommand(
                            ComModel.PENCIL_SKETCH_4,
                            videoModel?.path,
                            newPath
                        ), newPath, nameFile
                    )
                }
            }

            ComModel.PENCIL_SKETCH_4 -> {
                choice = 10
                pencilSketchIndex = 4
                generateSketchPreviewFile()
                if (isFramesExtracted) {
                    onFfmpegSuccessFinished(
                        ComModel.getCommand(
                            ComModel.PENCIL_SKETCH_4,
                            videoModel?.path,
                            newPath
                        ), newPath, nameFile
                    )
                } else {
                    execFFmpegBinary(
                        ComModel.getCommand(
                            ComModel.PENCIL_SKETCH_4,
                            videoModel?.path,
                            newPath
                        ), newPath, nameFile
                    )
                }
            }

            ComModel.REVERSE_VIDEO -> if (Utils.getMediaDuration(
                    videoModel?.path
                ) > 9000
            ) {
                //reverse longer video

//                    if (android.os.Build.VERSION.SDK_INT > Build.VERSION_CODES.P) {
//                        if (customProgressDialog != null && customProgressDialog.isShowing())
//                            customProgressDialog.dismiss();
//                        Toast.makeText(getContext(), "Reversing video longer than 9 seconds are not supported currently. We will fix it ASAP. Meanwhile you can reverse shorter video than 9 seconds.", Toast.LENGTH_LONG).show();
//                        initVideoView(videoModel.getPath());
//                    } else
                videoModel?.path?.let { splitVideoCommand(it) }
            } else {
//                    choice = 10;
                //reverse shorter than 9 sec
                execFFmpegBinary(
                    ComModel.getCommand(
                        ComModel.REVERSE_VIDEO,
                        videoModel?.path,
                        newPath
                    ), newPath, nameFile
                )
            }

            ComModel.DEFAULT -> {
                //                if (progressDialog != null) {
//                    progressDialog.dismiss();
//                }
                closeProgressDialog()
                Toast.makeText(
                    context,
                    getString(R.string.must_apply_some_changes),
                    Toast.LENGTH_SHORT
                ).show()
            }

            ComModel.BOOMERANG -> //                boolean executThisCommand = Long.parseLong(videoModel.getDuration()) / 1000 > 4;
                if ((videoModel?.duration?.toLong() ?: 0) / 1000 > 4) {
                    context?.let {
                        AlertDialog.Builder(it)
                            .setTitle("Boomerang")
                            .setMessage(getString(R.string.no_longer_than))
                            .setPositiveButton("Ok", null).show()

//                    if (progressDialog != null) {
//                        progressDialog.dismiss();
//                    }
                        closeProgressDialog()
                    }
                } else execFFmpegBinary(
                    ComModel.getCommand(
                        ComModel.BOOMERANG,
                        videoModel?.path,
                        newPath
                    ), newPath, nameFile
                )

            ComModel.BLACK_AND_WHITE -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.BLACK_AND_WHITE,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.SEPIA -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.SEPIA,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.GAMMA_CORRECTION -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.GAMMA_CORRECTION,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.STABILIZATION -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.STABILIZATION,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.SHARPER -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.SHARPER,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.ZOOM_IN_PROGRESSIVELY -> {
                val retriever = MediaMetadataRetriever()
                retriever.setDataSource(videoModel?.path)
                val width =
                    retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)
                        ?.toInt() ?: 0
                val height =
                    retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)
                        ?.toInt() ?: 0
                try {
                    retriever.release()
                } catch (e: IOException) {
                    throw RuntimeException(e)
                }
                if (height > width) {
                    context?.let {
                        AlertDialog.Builder(it)
                            .setTitle(getString(R.string.zoom_in_progressively))
                            .setMessage(getString(R.string.supports_horizontal_orientation))
                            .setPositiveButton("Ok", object : DialogInterface.OnClickListener {
                                override fun onClick(dialogInterface: DialogInterface, i: Int) {
                                    dialogInterface.dismiss()
                                    execFFmpegBinary(
                                        ComModel.getCommand(
                                            ComModel.ZOOM_IN_PROGRESSIVELY,
                                            videoModel?.path,
                                            newPath
                                        ), newPath, nameFile
                                    )
                                }
                            })
                            .setNegativeButton("Cancel", object : DialogInterface.OnClickListener {
                                override fun onClick(dialogInterface: DialogInterface, i: Int) {
                                    dialogInterface.dismiss()
                                    //                                    if (progressDialog != null) {
//                                        progressDialog.dismiss();
//                                    }
                                    closeProgressDialog()
                                }
                            })
                            .show()
                    }
                } else execFFmpegBinary(
                    ComModel.getCommand(
                        ComModel.ZOOM_IN_PROGRESSIVELY,
                        videoModel?.path,
                        newPath
                    ), newPath, nameFile
                )
            }

            ComModel.FLIP_VERTICAL -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.FLIP_VERTICAL,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.FLIP_HORIZONTAL -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.FLIP_HORIZONTAL,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.ROTATE_90_DEGREE_CLOCKWISE -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.ROTATE_90_DEGREE_CLOCKWISE,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.ROTATE_90_DEGERE_COUNTERCLOCKWISE -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.ROTATE_90_DEGERE_COUNTERCLOCKWISE,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.BLUR -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.BLUR,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.CONVOLUTION -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.CONVOLUTION,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.CONVOLUTION_2 -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.CONVOLUTION_2,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.STEREOSCOPIC -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.STEREOSCOPIC,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.PIXELIZE -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.PIXELIZE,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.BRIGHTNESS_PLUS -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.BRIGHTNESS_PLUS,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.BRIGHTNESS_MINUS -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.BRIGHTNESS_MINUS,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.SATURATION_PLUS -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.SATURATION_PLUS,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.SATURATION_MINUS -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.SATURATION_MINUS,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.GAMMA_R_PLUS -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.GAMMA_R_PLUS,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.GAMMA_R_MINUS -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.GAMMA_R_MINUS,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.GAMMA_G_PLUS -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.GAMMA_G_PLUS,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.GAMMA_G_MINUS -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.GAMMA_G_MINUS,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.GAMMA_B_PLUS -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.GAMMA_B_PLUS,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.GAMMA_B_MINUS -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.GAMMA_B_MINUS,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.COLOR_BALANCE -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.COLOR_BALANCE,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.COLOR_BALANCE_TWO -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.COLOR_BALANCE_TWO,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.COLOR_NEGATIVE -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.COLOR_NEGATIVE,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.CROSS_PROCESS -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.CROSS_PROCESS,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.DARKER -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.DARKER,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.LIGHTER -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.LIGHTER,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.INCREASE_CONTRAST -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.INCREASE_CONTRAST,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.LINEAR_CONTRAST -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.LINEAR_CONTRAST,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.MEDIUM_CONTRAST -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.MEDIUM_CONTRAST,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.STRONG_CONTRAST -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.STRONG_CONTRAST,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.NEGATIVE -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.NEGATIVE,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.CURVES_BLUE -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.CURVES_BLUE,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.ENHANCED_LBG_2 -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.ENHANCED_LBG_2,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.ENHANCED_LBG_4 -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.ENHANCED_LBG_4,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.ENHANCED_LBG_8 -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.ENHANCED_LBG_8,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.ENHANCED_LBG_16 -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.ENHANCED_LBG_16,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.DEBAND -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.DEBAND,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.SWAP_RECT_ONE -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.SWAP_RECT_ONE,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.SWAP_RECT_TWO -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.SWAP_RECT_TWO,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.SWAP_RECT_THREE -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.SWAP_RECT_THREE,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.SWAP_RECT_FOUR -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.SWAP_RECT_FOUR,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.SWAP_RECT_FIVE -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.SWAP_RECT_FIVE,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.SWAP_RECT_SIX -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.SWAP_RECT_SIX,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.NOISE -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.NOISE,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.CRAZY -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.CRAZY,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.MIRROR_VERTICAL -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.MIRROR_VERTICAL,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.MIRROR -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.MIRROR,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.CARTOON -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.CARTOON,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.ZOOMED -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.ZOOMED,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.VINTAGE -> execFFmpegBinary(
                ComModel.getCommand(
                    ComModel.VINTAGE,
                    videoModel?.path,
                    newPath
                ), newPath, nameFile
            )

            ComModel.FADE_IN -> executFadeCommand(nameFile, commandModel, videoDuration)
            ComModel.FADE_OUT -> executFadeCommand(nameFile, commandModel, videoDuration)
        }
    }

    private fun processGlitch(nameFile: String, fps: Int) {
        generateGlitchPreviewFile()
        if (isFramesGlitchExtracted) {
            // old FPS = 24
            onFfmpegSuccessFinished(
                ComModel.getExtractFramesCommand(
                    videoModel?.path,
                    newPath,
                    fps
                ), newPath, nameFile
            )
        } else {
            execFFmpegBinary(
                ComModel.getExtractFramesCommand(videoModel?.path, newPath, fps),
                newPath,
                nameFile
            )
        }
    }

    private val isFramesGlitchExtracted: Boolean
        private get() {
            val pencilSketc4Dir = getGlitchDir(cacheDir)
            val pencilSketchDirFiles = pencilSketc4Dir.listFiles()
            return if (pencilSketchDirFiles.size > 0) true else false
        }
    private val isFramesExtracted: Boolean
        private get() {
            val pencilSketc4Dir = getPencilSketchDir(cacheDir)
            val pencilSketchDirFiles = pencilSketc4Dir.listFiles()
            return if (pencilSketchDirFiles.size > 0) true else false
        }

    private fun clearGlitchDir() {
        val cacheDir = cacheDir
        val pencilSketc4Dir = getGlitchDir(cacheDir)
        Utils.clearDir(pencilSketc4Dir)
    }

    private fun clearSketchCacheDir() {
        val cacheDir = cacheDir
        val pencilSketc4Dir = getPencilSketchDir(cacheDir)
        Utils.clearDir(pencilSketc4Dir)
    }

    private fun generateGlitchPreviewFile() {
        if (isPreview) {
            val cacheDir = cacheDir

//            File pencilSketc4Dir = getPencilSketchDir(cacheDir);

//            Utils.clearDir(pencilSketc4Dir);

//                    Log.d("ddd", "pencilSketch: " + Utils.deleteDir(pencilSketc4Dir));
            newPath = "$cacheDir/glitch-frames/img%04d.jpg"
        }
    }

    private fun generateSketchPreviewFile() {
        if (isPreview) {
            val cacheDir = cacheDir

//            File pencilSketc4Dir = getPencilSketchDir(cacheDir);

//            Utils.clearDir(pencilSketc4Dir);

//                    Log.d("ddd", "pencilSketch: " + Utils.deleteDir(pencilSketc4Dir));
            newPath = "$cacheDir/pencil-sketch-4/img%04d.jpg"
        }
    }

    private fun getGlitchDir(cacheDir: File): File {
        val f = File(cacheDir.path)
        if (!f.exists()) f.mkdirs()
        val pencilSketc4Dir = File("$cacheDir/glitch-frames/")
        if (!pencilSketc4Dir.exists()) pencilSketc4Dir.mkdirs()
        return pencilSketc4Dir
    }

    private fun getPencilSketchDir(cacheDir: File): File {
        val f = File(cacheDir.path)
        if (!f.exists()) f.mkdirs()
        val pencilSketc4Dir = File("$cacheDir/pencil-sketch-4/")
        if (!pencilSketc4Dir.exists()) pencilSketc4Dir.mkdirs()
        return pencilSketc4Dir
    }

    private val cacheDir: File
        private get() {
            val cacheDir = StorageUtils.getIndividualCacheDirectory(
                context
            )
            if (!cacheDir.exists()) cacheDir.mkdirs()
            return cacheDir
        }

    private fun getPathForSave(nameFile: String) {
        val extensionFile: String
        //        newPath = Environment.getExternalStorageDirectory().getAbsolutePath() + Statistic.DIR_APP + Statistic.DIR_EFFECTS + "/";
        newPath = (Environment.getExternalStoragePublicDirectory(
            Environment.DIRECTORY_DCIM
        ).toString()
                + File.separator + "Montage"
                + Statistic.DIR_EFFECTS + "/")
        if (!File(newPath).exists()) {
            File(newPath).mkdirs()
        }
        extensionFile = Utils.getFileExtension(videoModel?.path)
        newPath = newPath + nameFile + extensionFile
        //        newPath = newPath + nameFile + ".mkv";
        if (isPreview) {
            val cacheDir = StorageUtils.getIndividualCacheDirectory(
                context
            )
            if (!cacheDir.exists()) cacheDir.mkdirs()
            val f = File(cacheDir.path)
            if (!f.exists()) f.mkdirs()
            newPath = "$cacheDir/$nameFile$extensionFile"
        }
    }

    private fun showInterstitialAd() {
        if (isPreview) return
        afterInterstitial()
//        showInterstitial()
    }

    private fun executFadeCommand(nameFile: String, commandModel: ComModel, videoDuration: Long) {
        val context = context ?: return
        val builder = AlertDialog.Builder(
            context, R.style.MyCustomTheme
        )
        val inflater = (context as Activity?)?.layoutInflater ?: return
        val v = inflater.inflate(R.layout.number_picker, null)
        val effectNameTV = v.findViewById<TextView>(R.id.numberPickerTitleTV)
        effectNameTV.text = commandModel.effectName

        //---------------------- option one ---------------------------------
        val FirstOptionTitleTV = v.findViewById<TextView>(R.id.FirstOptionTitleTV)
        FirstOptionTitleTV.text = if (commandModel.effectName.equals(
                "Fade in",
                ignoreCase = true
            )
        ) "Video fade in duration" else "Video fade out duration"
        val optionOneTV = v.findViewById<TextView>(R.id.optionOneTV)
        val optionOneAddIV = v.findViewById<ImageView>(R.id.optionOneAddIV)
        val viewPropertyAnimatorListenerOptionOneAddIV: ViewPropertyAnimatorListener =
            object : ViewPropertyAnimatorListener {
                override fun onAnimationStart(view: View) {}
                override fun onAnimationEnd(view: View) {
                    if (optionOneTV.getText().toString().toInt() + 1 > videoDuration) {
                        Toast.makeText(
                            context,
                            getString(R.string.cant_be_gratter_than) + videoDuration,
                            Toast.LENGTH_SHORT
                        ).show()
                        return
                    }
                    optionOneTV.text = (optionOneTV.getText().toString().toInt() + 1).toString()
                }

                override fun onAnimationCancel(view: View) {}
            }
        optionOneAddIV.setOnClickListener(object : View.OnClickListener {
            override fun onClick(view: View) {
                animateView(view, viewPropertyAnimatorListenerOptionOneAddIV)
            }
        })
        val optionOneRemoveIV = v.findViewById<ImageView>(R.id.optionOneRemoveIV)
        optionOneRemoveIV.setOnClickListener(object : View.OnClickListener {
            override fun onClick(view: View) {
                val viewPropertyAnimatorListenerOptionOneRemoveIV: ViewPropertyAnimatorListener =
                    object : ViewPropertyAnimatorListener {
                        override fun onAnimationStart(view: View) {}
                        override fun onAnimationEnd(view: View) {
                            if ((optionOneTV.getText().toString().toInt() - 1) < 1) {
                                Toast.makeText(
                                    context,
                                    getString(R.string.must_be_grater_than_null),
                                    Toast.LENGTH_SHORT
                                ).show()
                                return
                            }
                            optionOneTV.text =
                                (optionOneTV.getText().toString().toInt() - 1).toString()
                        }

                        override fun onAnimationCancel(view: View) {}
                    }
                animateView(optionOneRemoveIV, viewPropertyAnimatorListenerOptionOneRemoveIV)
            }
        })

        //---------------------- option one ---------------------------------


        //---------------------- option two ---------------------------------
        val SecondOptionTitleTV = v.findViewById<TextView>(R.id.SecondOptionTitleTV)
        //        SecondOptionTitleTV.setText("Audio fade in duration");
        SecondOptionTitleTV.text =
            if (commandModel.effectName.equals("Fade in", ignoreCase = true)) getString(
                R.string.audio_fade_in_duration
            ) else getString(R.string.audio_fade_out_duration)
        val optionTwoTV = v.findViewById<TextView>(R.id.optionTwoTV)
        val SecondOptionAddIV = v.findViewById<ImageView>(R.id.SecondOptionAddIV)
        SecondOptionAddIV.setOnClickListener(object : View.OnClickListener {
            override fun onClick(view: View) {
                val viewPropertyAnimatorListenerSecondOptionAddIV: ViewPropertyAnimatorListener =
                    object : ViewPropertyAnimatorListener {
                        override fun onAnimationStart(view: View) {}
                        override fun onAnimationEnd(view: View) {
                            if (optionTwoTV.getText().toString().toInt() + 1 > videoDuration) {
                                Toast.makeText(
                                    context,
                                    getString(R.string.cant_be_gratter_than) + videoDuration,
                                    Toast.LENGTH_SHORT
                                ).show()
                                return
                            }
                            optionTwoTV.text =
                                (optionTwoTV.getText().toString().toInt() + 1).toString()
                        }

                        override fun onAnimationCancel(view: View) {}
                    }
                animateView(SecondOptionAddIV, viewPropertyAnimatorListenerSecondOptionAddIV)
            }
        })
        val optionTwoRemoveIV = v.findViewById<ImageView>(R.id.optionTwoRemoveIV)
        optionTwoRemoveIV.setOnClickListener(object : View.OnClickListener {
            override fun onClick(view: View) {
                val viewPropertyAnimatorListenerOptionTwoRemoveIV: ViewPropertyAnimatorListener =
                    object : ViewPropertyAnimatorListener {
                        override fun onAnimationStart(view: View) {}
                        override fun onAnimationEnd(view: View) {
                            if ((optionTwoTV.getText().toString().toInt() - 1) < 1) {
                                Toast.makeText(
                                    context,
                                    getString(R.string.must_be_grater_than_null),
                                    Toast.LENGTH_SHORT
                                ).show()
                                return
                            }
                            optionTwoTV.text =
                                (optionTwoTV.getText().toString().toInt() - 1).toString()
                        }

                        override fun onAnimationCancel(view: View) {}
                    }
                animateView(optionTwoRemoveIV, viewPropertyAnimatorListenerOptionTwoRemoveIV)
            }
        })
        //---------------------- option two ---------------------------------
        builder.setView(v)
        val alertDialog = builder.show()
        //                Window window = alertDialog.getWindow();
//                window.setLayout(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT);
//                            builder.show();
        val doneNumberPickerIV = v.findViewById<ImageView>(R.id.doneNumberPickerIV)
        val viewPropertyAnimatorListenerDoneNumberPickerIV: ViewPropertyAnimatorListener =
            object : ViewPropertyAnimatorListener {
                override fun onAnimationStart(view: View) {}
                override fun onAnimationEnd(view: View) {
                    Log.d("ddd", "onAnimationEnd: $newPath")
                    alertDialog.dismiss()
                    initDialogProgress(false)
                    val audioFadeInDuration = optionTwoTV.getText().toString().toInt()
                    val videoFadeInDuration = optionOneTV.getText().toString().toInt()
                    //                String[] fadeInCommand = {"-i", videoModel.getPath(), "-preset", "ultrafast", "-filter_complex", "fade=in:st=0:d=" + videoFadeInDuration + "; afade=in:st=0:d=" + audioFadeInDuration, "-c:v", "libx264", "-c:a", "aac", newPath};
                    val fadeInCommand = ComModel.getFadeInCommand<Any>(
                        videoModel?.path,
                        newPath,
                        videoFadeInDuration,
                        audioFadeInDuration
                    )
                    val fadeOutCommand = ComModel.getFadeOutCommand<Any>(
                        videoModel?.path,
                        newPath,
                        videoDuration,
                        videoFadeInDuration,
                        audioFadeInDuration
                    )
                    //                String[] fadeOutCommand = {"-i", videoModel.getPath(), "-preset", "ultrafast", "-filter_complex", "fade=out:st=" + (videoDuration - videoFadeInDuration) + ":d=" + videoFadeInDuration + "; afade=out:st=" + (videoDuration - audioFadeInDuration) + ":d=" + audioFadeInDuration, "-c:v", "libx264", "-c:a", "aac", newPath};
                    execFFmpegBinary(
                        if (commandModel.effectName.equals(
                                "Fade in",
                                ignoreCase = true
                            )
                        ) fadeInCommand else fadeOutCommand, newPath, nameFile
                    )
                }

                override fun onAnimationCancel(view: View) {}
            }
        doneNumberPickerIV.setOnClickListener(object : View.OnClickListener {
            override fun onClick(view: View) {
                animateView(view, viewPropertyAnimatorListenerDoneNumberPickerIV)
            }
        })
        alertDialog.window?.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND) // This flag is required to set otherwise the setDimAmount method will not show any effect
        alertDialog.window?.setDimAmount(0.9f)
    }

    private fun animateView(
        view: View,
        viewPropertyAnimatorListener: ViewPropertyAnimatorListener
    ) {
        ViewCompat.animate(view)
            .setDuration(200)
            .scaleX(0.9f)
            .scaleY(0.9f)
            .setInterpolator(CycleInterpolator())
            .setListener(viewPropertyAnimatorListener)
            .withLayer()
            .start()
    }

    private fun initDialogProgress(showSpinner: Boolean) {
//        progressDialog = new ProgressDialog(getContext());
//        progressDialog.setCancelable(false);
//        progressDialog.setProgressStyle(showSpinner ? ProgressDialog.STYLE_SPINNER : ProgressDialog.STYLE_HORIZONTAL);
//        progressDialog.setTitle(isPreview ? "Preparing preview..." : getString(R.string.progress_dialog_saving));
//        progressDialog.setProgress(0);
//        progressDialog.setButton(DialogInterface.BUTTON_NEGATIVE, getString(R.string.cancel), (dialog, which) -> {
//            cancelCreateFile();
//            initVideoView(videoModel.getPath());
//        });
//        progressDialog.show();
        val activity: Activity = requireView().context as Activity
        customProgressDialog = CustomDialog(
            requireView().context as Activity,
            if (isPreview) getString(R.string.common_preparing_preview) else getString(R.string.progress_dialog_saving),
            showSpinner,
            object : CustomDialog.CustomDialogListener {
                override fun onDismiss() {
//                        Log.d("DIALOG", "onDismiss: ");
                    cancelCreateFile()
                    initVideoView(videoModel?.path)
                }
            }
        )
        customProgressDialog?.show()
    }

    private var isCancelSaveFile = false
    private fun cancelCreateFile() {
        isCancelSaveFile = true
        closeProgressDialog()
        //        if (ffmpeg.isFFmpegCommandRunning()) {
//            ffmpeg.killRunningProcesses();
//        }
        if (newPath != null) {
            File(newPath).delete()
        }

//        if (progressDialog != null) {
//            progressDialog.dismiss();
//        }
    }

    private fun closeProgressDialog() {
        if (customProgressDialog != null) {
            customProgressDialog?.dismiss()
        }
    }

    private fun reverseVideoCommand() {
        val moviesDir = StorageUtils.getIndividualCacheDirectory(
            context
        )
        val srcDir = File(moviesDir, "VideoSplit")
        val files = srcDir.listFiles()
        val filePrefix = "reverse_video"
        val fileExtn = ".mp4"
        val destDir = File(moviesDir, "VideoPartsReverse")
        if (destDir.exists()) Utils.deleteDir(destDir)
        destDir.mkdir()
        i = 0
        filesNumb = files.size
        val fileName = filePrefix + i + fileExtn
        val dest = File(destDir, fileName)
        //        String command[] = {"-i", files[i].getAbsolutePath(), "-preset", "ultrafast", "-vf", "reverse", dest.getAbsolutePath()};
        val command = ComModel.getCommand<Any>(
            ComModel.REVERSE_VIDEO,
            files[i].absolutePath,
            dest.absolutePath
        )
        if (i == files.size - 1) lastReverseCommand = command

//        Log.d(Const.TAG, "reverseVideoCommand: " + Arrays.toString(command));
        execFFmpegBinary(command, files[i].absolutePath, fileName)
        handlerRevere = object : Handler() {
            override fun handleMessage(msg: Message) {
                i++
                val fileName = filePrefix + i + fileExtn
                val dest = File(destDir, fileName)
                //                String command[] = {"-i", files[i].getAbsolutePath(), "-preset", "ultrafast", "-vf", "reverse", dest.getAbsolutePath()};
                var command: Any? = null
                try {
                    command = ComModel.getCommand(
                        ComModel.REVERSE_VIDEO,
                        files[i].absolutePath,
                        dest.absolutePath
                    )
                } catch (e: IndexOutOfBoundsException) {
                    e.printStackTrace()
                }
                if (i == files.size - 1) {
                    lastReverseCommand = command
                    i = 0

                    //ovo sam zadnje dodao, treba ga proveriti
                    filesNumb = 0
                }

//                Log.d(Const.TAG, "reverseVideoCommand: " + Arrays.toString(command));
                execFFmpegBinary(command, files[i].absolutePath, fileName)
            }
        }
    }

    private fun concatVideoCommand() {
        val moviesDir = StorageUtils.getIndividualCacheDirectory(
            context
        )
        val srcDir = File(moviesDir, "VideoPartsReverse")
        val files = srcDir.listFiles() ?: return
        if (files.size > 1) {
            Arrays.sort(files, LastModifiedFileComparator.LASTMODIFIED_REVERSE)
        }
        val stringBuilder = StringBuilder()
        val filterComplex = StringBuilder()
        filterComplex.append(if (ComModel.isNewApiRequired()) "-filter_complex " else "-filter_complex,")
        for (i in files.indices) {
            stringBuilder.append(if (ComModel.isNewApiRequired()) "-i" + " \'" + files[i].absolutePath + "\' " else "-i" + "," + files[i].absolutePath + ",")
            if (ComModel.isNewApiRequired()) filterComplex.append("[").append(i).append(":v")
                .append("]") else filterComplex.append("[").append(i).append(":v").append(i)
                .append("]")
            if (VideoUtil.isVideoHaveAudioTrack(files[i].absolutePath)) if (ComModel.isNewApiRequired()) filterComplex.append(
                "["
            ).append(i).append(":a").append("]") else filterComplex.append("[").append(i)
                .append(":a").append(i).append("] ")
        }
        if (VideoUtil.isVideoHaveAudioTrack(files[i].absolutePath)) if (ComModel.isNewApiRequired()) filterComplex.append(
            "concat=n="
        ).append(
            files.size
        ).append(":v=1:a=1[v][a]") else filterComplex.append("concat=n=").append(
            files.size
        ).append(":v=1:a=1 [v][a]") else {
            if (ComModel.isNewApiRequired()) filterComplex.append("concat=n=").append(files.size)
                .append(":v=1[v]") else filterComplex.append("concat=n=").append(
                files.size
            ).append(":v=1 [v]")
        }
        val inputCommand: Array<String?> =
            stringBuilder.toString().split(",".toRegex()).dropLastWhile { it.isEmpty() }
                .toTypedArray()
        val filterCommand: Array<String?> =
            filterComplex.toString().split(",".toRegex()).dropLastWhile { it.isEmpty() }
                .toTypedArray()

//        final File boomerangFile = new File(StorageUtils.getIndividualCacheDirectory(getContext()), "Boomerang");
        val filePrefix = "reverse_video"
        val fileExtn = ".mp4"
        var dest = File(
            StorageUtils.getIndividualCacheDirectory(
                context
            ), filePrefix + fileExtn
        )
        var fileName = filePrefix + fileExtn
        var fileNo = 0
        while (dest.exists()) {
            fileNo++
            dest = File(moviesDir, filePrefix + fileNo + fileExtn)
            fileName = filePrefix + fileNo + fileExtn
        }

//        if (isPreview) {
//            String defaultName = "VS_" + simpleDateFormat.format(System.currentTimeMillis());
//
//            getPathForSave(defaultName);
//            filePath = newPath;
//
//        } else {
//
//            filePath = dest.getAbsolutePath();
//        }
        val defaultName = "VS_" + simpleDateFormat.format(System.currentTimeMillis())
        getPathForSave(defaultName)
        val destinationCommand: Array<String?>
        if (VideoUtil.isVideoHaveAudioTrack(files[i].absolutePath)) destinationCommand = arrayOf(
            "-map",
            "[v]",
            "-map",
            "[a]",
            "-preset",
            "ultrafast",
            newPath
        ) else destinationCommand = arrayOf("-map", "[v]", "-preset", "ultrafast", newPath)
        val destCommand =
            if (VideoUtil.isVideoHaveAudioTrack(files[i].absolutePath)) " -map [v] -map [a] -preset ultrafast $newPath" else " -map [v] -preset ultrafast $newPath"

//        Log.d("ddd", "concatVideoCommand: " + stringBuilder.toString() + filterComplex.toString() + destCommand);
        if (ComModel.isNewApiRequired()) //            execFFmpegBinary(stringBuilder.toString() + "-filter_complex [0:v][1:v][2:v]concat=n=3:v=1[v]" + destCommand, newPath, fileName);
            execFFmpegBinary(
                stringBuilder.toString() + filterComplex.toString() + destCommand,
                newPath,
                fileName
            ) else execFFmpegBinary(
            combine(inputCommand, filterCommand, destinationCommand), newPath, fileName
        )
    }

    private fun execFFmpegBinary(command: Any?, path: String?, title: String) {
        if (ComModel.isNewApiRequired()) {
            val myCommand = command as String?
            ffmpegDone = false
            val executionId = FFmpeg.executeAsync(myCommand, object : ExecuteCallback {
                override fun apply(executionId: Long, rc: Int) {
                    if (rc == Config.RETURN_CODE_SUCCESS) {
                        Log.i(Config.TAG, "Command execution completed successfully.")
                        onFfmpegSuccessFinished(myCommand, path, title)
                    } else if (rc == Config.RETURN_CODE_CANCEL) {
                        Log.i(Config.TAG, "Command execution cancelled by user.")
                    } else {
                        Log.i(
                            Config.TAG,
                            String.format(
                                "Command execution failed with rc=%d and the output below.",
                                rc
                            )
                        )
                        Config.printLastCommandOutput(Log.INFO)
                        if (showToast) Toast.makeText(
                            context,
                            getString(R.string.can_not_create_file),
                            Toast.LENGTH_SHORT
                        ).show()
                        closeProgressDialog()
                    }
                }
            })
            Config.enableLogCallback { message -> //                    Flog.e(s);
                val durationFile =
                    (Utils.getProgress(message.text, (videoModel?.duration?.toLong() ?:0) / 1000)
                        .toInt() * tempoVideo).toDouble()
                val percent = durationFile / ((videoModel?.duration?.toDouble() ?: 0.0) / 1000)
                if (customProgressDialog != null) {
                    if ((percent * 100).toInt() > 0) {
                        customProgressDialog?.setProgress((percent * 100).toInt())
                    }
                }
            }
        }

//        else {
////            Log.d("ddd", "execFFmpegBinary: " + Arrays.toString((String[]) command));
//            ffmpegDone = false;
//            try {
//                ffmpeg.execute((String[]) command, new ExecuteBinaryResponseHandler() {
//                    @Override
//                    public void onFailure(String s) {
//                        Flog.e("Failllllllll   " + s);
//
//                        if (showToast)
//                            Toast.makeText(getContext(), getString(R.string.can_not_create_file), Toast.LENGTH_SHORT).show();
////                    if (progressDialog != null) {
////                        progressDialog.dismiss();
////                    }
//
//                        closeProgressDialog();
//                    }
//
//                    @Override
//                    public void onSuccess(String s) {
////                    Flog.e("Successs     " + s);
//
//                        onFfmpegSuccessFinished((String[]) command, path, title);
//
//
//                    }
//
//                    @Override
//                    public void onProgress(String s) {
////                    Flog.e(s);
//                        double durationFile = (int) Utils.getProgress(s, Long.parseLong(videoModel.getDuration()) / 1000) * tempoVideo;
//                        double percent = durationFile / (Double.parseDouble(videoModel.getDuration()) / 1000);
////                    Log.e("xxx", " durrrrrr  " + durationFile + "___" + percent * 100);
////                    if (progressDialog != null) {
////                        if ((int) (percent * 100) > 0) {
////                            progressDialog.setProgress((int) (percent * 100));
////                        }
////                    }
//
//                        if (customProgressDialog != null) {
//                            if ((int) (percent * 100) > 0) {
//                                customProgressDialog.setProgress((int) (percent * 100));
//                            }
//                        }
//                    }
//
//                    @Override
//                    public void onStart() {
//
//                    }
//
//                    @Override
//                    public void onFinish() {
//
//                    }
//                });
//
//            } catch (FFmpegCommandAlreadyRunningException e) {
//                e.printStackTrace();
//            }
//        }
    }

    private fun checkLastReverseVideoCommand(command: Any?): Boolean {
        if (ComModel.isNewApiRequired()) {
            val nextCommand = command as String?
            return nextCommand.equals(lastReverseCommand as String?, ignoreCase = true)
            //            return false;
        } else {
            return (command as Array<String?>?).contentEquals(lastReverseCommand as Array<String?>?)
        }
    }

    private fun onFfmpegSuccessFinished(command: Any?, path: String?, title: String) {
        if (i < filesNumb) {
            handlerRevere?.obtainMessage()?.let { handlerRevere?.sendMessage(it) }
            return
        }
        Log.d("sss", "onFfmpegSuccessFinished: $lastReverseCommand")
        if (choice == 8) {
            choice = 9
            reverseVideoCommand()
            return
        } else if (checkLastReverseVideoCommand(if (ComModel.isNewApiRequired()) command as String? else command as Array<String?>?)) {
            choice = 0
            concatVideoCommand()
            return
        } else if (choice == 10) {
            context?.let {
                Thread { generateSketch(it) }.start()
                choice = 0
            }
            return
        } else if (choice == 11) {
            // Old glitch processing (GLITCH without index) - keep for compatibility
            context?.let {
                generateGlitch(it)
                choice = 0
            }
            return
        } else if (choice == 12) {
            context?.let {
                generateGlitchTwo(it)
                choice = 0
            }
            return
        }
        ffmpegDone = true
        showInterstitialAd()
        if (isCancelSaveFile) return

//        if (progressDialog != null) {
//            progressDialog.setProgress(100);
//            progressDialog.dismiss();
//        }
        if (customProgressDialog != null) {
            customProgressDialog?.setProgress(100)
            customProgressDialog?.dismiss()
        }
        if (isPreview) {

//            if (commandModel != null)
//            ComModel.getListOfCommands().get(lastPressedGridViewItem).setPreviewAvailable(true);

//            new Handler(Looper.getMainLooper()).post(new Runnable() {
//                @Override
//                public void run() {
//
//                }
//            });
            (context as MainActivity?)?.runOnUiThread(object : Runnable {
                override fun run() {
                    adapter?.setPreviewAvailable(lastPressedGridViewItem)
                }
            })

//            recyclerView.setAdapter(new OptionsAdapter(getContext(), ComModel.getListOfCommands()));
//            recyclerView.invalidate();

//            Log.d("ddd", "onFfmpegSuccessFinished: ");
            lastPickedEffectName?.let {lastPickedEffectName ->
                path?.let {
                    preparedEffectsMap[lastPickedEffectName] = path
                }
            }
            initVideoView(path)
            isPreview = false
            return
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) FileUtil.scanGallery(
            context, path
        ) else FileUtil.addFileToContentProvider(
            context, path, title
        )
        Toast.makeText(context, getString(R.string.create_file) + ": " + path, Toast.LENGTH_SHORT)
            .show()
        afterFFmpegOnSuccess()
    }

    private fun generateGlitch(context: Context) {
        val glitchFramesDir = File(
            StorageUtils.getIndividualCacheDirectory(
                context
            ).toString() + "/glitch-frames/"
        )
        val directory = File(glitchFramesDir.path)
        val files = directory.listFiles()?: return
        //        Log.d("Files", "Size: " + files.length);
        var glichedFileNumberCounter = 0
        var originalBitmapCounter = 0
        for (i in files.indices) {

//            Log.d("Files", "FileName:" + files[i].getName());
            var imageStream: InputStream?
            try {
                val imageUri =
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) FileProvider.getUriForFile(
                        context, BuildConfig.APPLICATION_ID+".provider", File(files[i].path)) else Uri.fromFile(File(files[i].path))
                imageStream = requireContext().contentResolver.openInputStream(imageUri)
                val bitmapOriginal = BitmapFactory.decodeStream(imageStream)
                val f3 = File(
                    StorageUtils.getIndividualCacheDirectory(
                        context
                    ).toString() + "/glitch-filtered/"
                )
                if (!f3.exists()) f3.mkdirs()
                if (i == 0) Utils.clearDir(f3)
                val fmt = "img%04d.jpg"
                val bitmaps = GlitchEffect.bmpToGlith(bitmapOriginal)
                for (j in bitmaps.indices) {
                    try {
//                            String fmt = "img%04d.jpg";
                        var fOut: OutputStream? = null
                        val file = File(
                            f3.path,
                            String.format(fmt, glichedFileNumberCounter)
                        ) // the File to save , append increasing numeric counter to prevent files from getting overwritten.
                        fOut = FileOutputStream(file)
                        bitmaps[j].compress(
                            Bitmap.CompressFormat.JPEG,
                            100,
                            fOut
                        ) // saving the Bitmap to a file compressed as a JPEG with 85% compression rate
                        fOut.flush() // Not really required
                        fOut.close()
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                    ++glichedFileNumberCounter
                }

//                if (i % 5 == 0) {
//
//                    Bitmap[] bitmaps = GlitchEffect.bmpToGlith(bitmap_original);
//                    for (int j = 0; j < bitmaps.length; j++) {
//
//                        try {
////                            String fmt = "img%04d.jpg";
//                            OutputStream fOut = null;
//                            File file = new File(f3.getPath(), String.format(fmt, glichedFileNumberCounter)); // the File to save , append increasing numeric counter to prevent files from getting overwritten.
//                            fOut = new FileOutputStream(file);
//
//                            bitmaps[j].compress(Bitmap.CompressFormat.JPEG, 100, fOut); // saving the Bitmap to a file compressed as a JPEG with 85% compression rate
//                            fOut.flush(); // Not really required
//                            fOut.close();
//                        } catch (Exception e) {
//                            e.printStackTrace();
//                        }
//                        ++glichedFileNumberCounter;
//                    }
//                } else {
//                    try {
//                        OutputStream fOut = null;
//                        File file = new File(f3.getPath(), String.format(fmt, glichedFileNumberCounter)); // the File to save , append increasing numeric counter to prevent files from getting overwritten.
//                        fOut = new FileOutputStream(file);
//                        bitmap_original.compress(Bitmap.CompressFormat.JPEG, 100, fOut); // saving the Bitmap to a file compressed as a JPEG with 85% compression rate
//                        fOut.flush(); // Not really required
//                        fOut.close();
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    }
//                    ++glichedFileNumberCounter;
//
//                }
                if (i == files.size - 1) {
                    val defaultName = "VS_" + simpleDateFormat.format(System.currentTimeMillis())
                    getPathForSave(defaultName)
                    //old FPS = 24
                    execFFmpegBinary(
                        ComModel.getImagesToVideoCommand(
                            f3.path + "/img%04d.jpg",
                            newPath,
                            24,
                            videoModel?.path
                        ), newPath, ""
                    )
                    //                    File[] filteredFiles = f3.listFiles();
//                    for (int j = 0; j < filteredFiles.length; j++) {
//                        Log.d("Files", "filteredFiles:" + filteredFiles[j].getPath());
//
//                    }
                }
            } catch (e: FileNotFoundException) {
                e.printStackTrace()
            }
            ++originalBitmapCounter
        }
    }

    private fun generateGlitchTwo(context: Context) {
        val ran = Random()
        val pencilSketc4Dir = File(
            StorageUtils.getIndividualCacheDirectory(
                context
            ).toString() + "/glitch-frames/"
        )
        val directory = File(pencilSketc4Dir.path)
        val files = directory.listFiles()
        //        Log.d("Files", "Size: " + files.length);
        val f3 = File(
            StorageUtils.getIndividualCacheDirectory(
                context
            ).toString() + "/glitch-filtered/"
        )
        if (!f3.exists()) f3.mkdirs()
        if (i == 0) Utils.clearDir(f3)
        for (i in files.indices) {
            val finalI = i


//            Log.d("Files", "FileName:" + files[i].getName());
            try {
                var imageStream: InputStream?
                val imageUri =
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) FileProvider.getUriForFile(context, BuildConfig.APPLICATION_ID+".provider", File(
                files[i].path
                )
                ) else Uri.fromFile(File(files[i].path))
                try {
                    if (nonGlitchFrames > 0) {
//                        Log.d("Files", "FileName: IF " + files[i].getName());
                        imageStream = requireContext().contentResolver.openInputStream(imageUri)
                        val bitmapOriginal = BitmapFactory.decodeStream(imageStream)
                        var outStream: OutputStream? = null
                        try {
                            outStream = FileOutputStream(File(f3, files[i].getName()))
                            bitmapOriginal.compress(Bitmap.CompressFormat.JPEG, 100, outStream)
                            outStream.close()
                            if (finalI == files.size - 1) {
                                val defaultName =
                                    "VS_" + simpleDateFormat.format(System.currentTimeMillis())
                                getPathForSave(defaultName)
                                execFFmpegBinary(
                                    ComModel.getImagesToVideoCommand(
                                        f3.path + "/img%04d.jpg",
                                        newPath,
                                        24
                                    ), newPath, ""
                                )

//                                File[] filteredFiles = f3.listFiles();
//                                for (int j = 0; j < filteredFiles.length; j++) {
//                                    Log.d("Files", "onImageReady Files:" + filteredFiles[j].getPath());
//                                }
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    } else {
//                        Log.d("Files", "FileName: ELSE " + files[i].getName());
                        PixelSortingContext(context, imageUri).savePixelSortedImage(
                            File(
                                f3,
                                files[finalI].getName()
                            ).path,
                            context,
                            filters?.get(gFilterIndex),
                        OnImageSavedListener {
                            //                                Log.d("ddd", "onImageReady index: " + finalI);
                            if (finalI == files.size - 1) {
                                val defaultName =
                                    "VS_" + simpleDateFormat.format(System.currentTimeMillis())
                                getPathForSave(defaultName)
                                execFFmpegBinary(
                                    ComModel.getImagesToVideoCommand(
                                        f3.path + "/img%04d.jpg",
                                        newPath,
                                        24
                                    ), newPath, ""
                                )

                                //                                File[] filteredFiles = f3.listFiles();
                                //                                for (int j = 0; j < filteredFiles.length; j++) {
                                //                                    Log.d("Files", "onImageReady Files:" + filteredFiles[j].getPath());
                                //                                }
                            }
                        })
                        if (nonGlitchFrames == -glitchFrames) {
                            nonGlitchFrames = ran.nextInt(11) + 10
                            glitchFrames = ran.nextInt(4) + 5
                        }
                    }
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            } catch (e: Error) {
                e.printStackTrace()
            }
            nonGlitchFrames--
        }
    }

    private fun generateSketch(context: Context) {
        val pencilSketc4Dir = File(
            StorageUtils.getIndividualCacheDirectory(
                context
            ).toString() + "/pencil-sketch-4/"
        )
        val directory = File(pencilSketc4Dir.path)
        val files = directory.listFiles()
        //        Log.d("Files", "Size: " + files.length);
        for (i in files.indices) {

//            Log.d("Files", "FileName:" + files[i].getName());
            var imageStream: InputStream?
            try {
                val imageUri =
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) FileProvider.getUriForFile(
                        context, BuildConfig.APPLICATION_ID+".provider", File(
                files[i].path
                )
                ) else Uri.fromFile(File(files[i].path))
                imageStream = requireContext().contentResolver.openInputStream(imageUri)
                val bitmap_original = BitmapFactory.decodeStream(imageStream)
                //                                bitmap_original = ExifUtils.rotateBitmap(getRealPathFromURI(imgUri), bitmap_original);
                var bitmap_print: Bitmap? = null
                if (pencilSketchIndex == 4) bitmap_print = ImageUtil.getSketchFromBH(
                    ImageUtil.getResizedBitmap(
                        BitmapFactory.decodeResource(
                            resources, R.drawable.sketch_1
                        ), ImageUtil.BITMAP_LARGE_SIZE
                    ), bitmap_original, context
                ) else if (pencilSketchIndex == 1) bitmap_print = ColorFilters.Changetosketch(
                    bitmap_original,
                    context,
                    5f
                ) else if (pencilSketchIndex == 2) bitmap_print = ColorFilters.Changetosketch(
                    bitmap_original,
                    context,
                    15f
                ) else if (pencilSketchIndex == 3) bitmap_print = ColorFilters.Changetosketch(
                    bitmap_original,
                    context,
                    25f
                ) else if (pencilSketchIndex == 5) bitmap_print = BitmapFilter.changeStyle(
                    bitmap_original,
                    BitmapFilter.SKETCH_STYLE
                ) else if (pencilSketchIndex == 6) bitmap_print =
                    BitmapFilter.changeStyle(bitmap_original, BitmapFilter.OIL_STYLE, 5)
                val f3 = File(
                    StorageUtils.getIndividualCacheDirectory(
                        context
                    ).toString() + "/pencil-sketch-4-filtered/"
                )
                if (!f3.exists()) f3.mkdirs()
                if (i == 0) Utils.clearDir(f3)
                var outStream: OutputStream? = null
                try {
                    outStream = FileOutputStream(File(f3, files[i].getName()))
                    bitmap_print?.compress(Bitmap.CompressFormat.JPEG, 100, outStream)
                    outStream.close()
                    //                                    utils.refreshMediaScanner(this, file_to_save_image);
//                                    Toast.makeText(this, "Photo saved to sdcard/PhotoEffects/", Toast.LENGTH_SHORT).show();
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                if (i == files.size - 1) {
                    val defaultName = "VS_" + simpleDateFormat.format(System.currentTimeMillis())
                    getPathForSave(defaultName)
                    execFFmpegBinary(
                        ComModel.getImagesToVideoCommand(
                            f3.path + "/img%04d.jpg",
                            newPath,
                            4
                        ), newPath, ""
                    )
                    //                    File[] filteredFiles = f3.listFiles();
//                    for (int j = 0; j < filteredFiles.length; j++) {
//                        Log.d("Files", "filteredFiles:" + filteredFiles[j].getPath());
//
//                    }
                }
            } catch (e: FileNotFoundException) {
                e.printStackTrace()
            }
        }
    }

    override fun onDestroy() {
        watch?.time(TimeUnit.SECONDS)?.let { fragmentAnalytics?.setDuration(it) }
        MyApplication.addEvent(fragmentAnalytics)
        EffectsConstants.APPLIED_EFFECTS_VIDEO_ITEM_ARRAYLIST.clear()
        // filterPreviewManager?.destroy()
        super.onDestroy()
    }

    override fun afterInterstitial() {
//        Log.d("ddd", "afterInterstetial: ");
        Handler().postDelayed(object : Runnable {
            override fun run() {
                try {
                    Flog.d("jedan")
                    if (isCancelSaveFile) return
                    Flog.d("dva")

//                    if (progressDialog != null) {
//                        progressDialog.setProgress(100);
//                        progressDialog.dismiss();
//                    }
                    if (customProgressDialog != null) {
                        customProgressDialog?.setProgress(100)
                        customProgressDialog?.dismiss()
                    }
                    Flog.d("new path: $newPath")
                    val afile = preparedEffectsMap[lastPickedEffectName]?.let { File(it) }?:return
                    Flog.d("afile: $afile")
                    if (afile.renameTo(File(newPath))) {
                        println("File is moved successful!")
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) FileUtil.scanGallery(
                            context, newPath
                        ) else FileUtil.addFileToContentProvider(
                            context, newPath, fileNameForSaving
                        )
                        Toast.makeText(
                            context,
                            getString(R.string.create_file) + ": " + newPath,
                            Toast.LENGTH_SHORT
                        ).show()
                        afterFFmpegOnSuccess()
                    } else {
                        Flog.d("File is failed to move!")
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }, 1000)


//        new Handler().postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                if (ffmpegDone)
//                    afterFFmpegOnSuccess();
//                ffmpegDone = false;
//
//            }
//        }, 1000);
    }

    private fun afterFFmpegOnSuccess() {
        if (isPauseFragment) {
            return
        }
        Utils.clearFragment(fragmentManager)
        requireContext().sendBroadcast(Intent(Statistic.OPEN_EFFECSTS_STUDIO))
    }

    override fun onCancelSelect() {
        if (dialogInputName != null) {
            dialogInputName?.hideDialog()
        }
    }

    override fun onFileNameEmpty() {
        Toast.makeText(context, getString(R.string.error), Toast.LENGTH_SHORT).show()
    }

    override fun onFileNameHasSpecialCharacter() {
        Toast.makeText(
            context,
            getString(R.string.name_file_can_not_contain_character),
            Toast.LENGTH_SHORT
        ).show()
    }

    @get:Throws(IOException::class)
    private val filtersFromDB: List<Filter>
        private get() {
            val filterDB: FilterDB
            filterDB = try {
                FilterDB(context)
            } catch (e: IOException) {
                Log.e(
                    "getFiltersFromDB",
                    "A fatal error occurred while accessing the Filter database.",
                    e
                )
                throw e
            }
            filterDB.open()
            val filters = filterDB.getFilters()
            filterDB.close()
            return filters
        }

    companion object {
        fun newInstance(bundle: Bundle?): MoreOptionsFragment {
            val fragment = MoreOptionsFragment()
            fragment.setArguments(bundle)
            return fragment
        }

        fun combine(
            arg1: Array<String?>,
            arg2: Array<String?>,
            arg3: Array<String?>
        ): Array<String?> {
            val result = arrayOfNulls<String>(arg1.size + arg2.size + arg3.size)
            System.arraycopy(arg1, 0, result, 0, arg1.size)
            System.arraycopy(arg2, 0, result, arg1.size, arg2.size)
            System.arraycopy(arg3, 0, result, arg1.size + arg2.size, arg3.size)
            return result
        }
    }
}
