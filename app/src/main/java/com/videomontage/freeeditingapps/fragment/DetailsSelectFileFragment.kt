package com.videomontage.freeeditingapps.fragment

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.widget.Toolbar
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.adapter.LeftListAdapter
import com.videomontage.freeeditingapps.adapter.RightListAdapter
import com.videomontage.freeeditingapps.model.VideoModel
import com.videomontage.freeeditingapps.statistic.Statistic
import com.videomontage.freeeditingapps.utils.Flog
import com.videomontage.freeeditingapps.utils.Utils
import timber.log.Timber
import com.yalantis.multiselection.lib.MultiSelect
import com.yalantis.multiselection.lib.MultiSelectBuilder

class DetailsSelectFileFragment : AbsFragment() {
    private var mMultiSelect: MultiSelect<VideoModel>? = null
    private var leftAdapter: LeftListAdapter? = null
    private var rightAdapter: RightListAdapter? = null
    override var toolbar: Toolbar? = null
    private var viewChooseFile: View? = null
    private var tvNoAudioFile: TextView? = null
    private var videoModelListChecked: List<VideoModel?>? = ArrayList()
    private var isReceiverRegistered = false
    private val receiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (intent.action != null) {
                when (intent.action) {
                    Statistic.UPDATE_CHOOSE_VIDEO -> {
                        val path = intent.extras!!.getString(Statistic.MODEL)
                        Flog.e("x   items " + mMultiSelect!!.selectedItems!!.size)
                        var i = 0
                        val items = leftAdapter?.items
                        if (items != null) {
                            for (i in 0 until items.size()) {
                                val videoModel = items[i]
                                if (videoModel?.path == path) {
                                    leftAdapter?.removeItemAt(i)
                                    rightAdapter?.add(videoModel, false)
                                    break // Exit the loop after processing the found item
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_details_select_folder, container, false)
    }

    override fun initToolbar() {
        super.initToolbar()
        toolbar!!.title = getString(R.string.select_file)
        toolbar!!.menu.clear()
        toolbar!!.inflateMenu(R.menu.menu_details_gallery_folder)
        toolbar!!.setOnMenuItemClickListener { item: MenuItem ->
            when (item.itemId) {
                R.id.action_select_file -> {
                    checkSelectFile()
                    if (videoModelListChecked!!.size < 2) {
                        Toast.makeText(
                            context,
                            getString(R.string.you_need_to_have),
                            Toast.LENGTH_SHORT
                        ).show()
                    } else {
                        var duration: Long = 0
                        for (videoModel in videoModelListChecked!!) {
                            duration += videoModel!!.duration.toLong()
                        }
                        val bundle = Bundle()
                        bundle.putParcelableArrayList(
                            Statistic.LIST_VIDEO,
                            videoModelListChecked as ArrayList<out Parcelable?>?
                        )
                        bundle.putLong(Statistic.DURATION, duration)
                        requireFragmentManager().beginTransaction()
                            .add(R.id.view_container, SortMergerFragment.newInstance(bundle))
                            .addToBackStack(null)
                            .commit()
                    }
                }

                R.id.item_search -> {
                    val videoModels = Utils.getVideos(context, 0, null, true)
                    Flog.e("x   items " + mMultiSelect!!.selectedItems!!.size)
                    if (mMultiSelect!!.selectedItems!!.size > 0) {
                        videoModelListChecked = mMultiSelect!!.selectedItems
                        for (videoModel in videoModelListChecked!!) {
                            for (videoModel1 in videoModels) {
                                if (videoModel!!.path == videoModel1!!.path) {
                                    videoModels.remove(videoModel1)
                                    break
                                }
                            }
                        }
                    }
                    val bundle = Bundle()
                    bundle.putParcelableArrayList(
                        Statistic.LIST_VIDEO,
                        videoModels as ArrayList<out Parcelable?>
                    )
                    requireActivity().supportFragmentManager.beginTransaction()
                        .setCustomAnimations(
                            R.anim.animation_left_to_right,
                            R.anim.animation_right_to_left,
                            R.anim.animation_left_to_right,
                            R.anim.animation_right_to_left
                        )
                        .add(R.id.view_container, SearchFragment.newInstance(bundle))
                        .addToBackStack(null)
                        .commit()
                }
            }
            true
        }
    }

    fun checkSelectFile(): Boolean {
        videoModelListChecked = mMultiSelect!!.selectedItems
        return false
    }

    private var leftListAudio: List<VideoModel> = ArrayList()
    
    private fun navigateToMergerWithVideos(videos: List<VideoModel>) {
        Timber.tag("DEBUG_FLOW").d("DetailsSelectFileFragment: Navigating to merger with ${videos.size} pre-selected videos")
        
        if (videos.size < 2) {
            Toast.makeText(
                context,
                getString(R.string.you_need_to_have),
                Toast.LENGTH_SHORT
            ).show()
            // Go back to main activity
            requireActivity().onBackPressed()
            return
        }
        
        var duration: Long = 0
        for (videoModel in videos) {
            val videoDuration = videoModel.duration.toLongOrNull() ?: 0L
            Timber.tag("DEBUG_FLOW").d("DetailsSelectFileFragment: Video ${videoModel.nameAudio} duration: ${videoDuration}ms")
            duration += videoDuration
        }
        
        Timber.tag("DEBUG_FLOW").d("DetailsSelectFileFragment: Total duration: ${duration}ms")
        
        val bundle = Bundle()
        bundle.putParcelableArrayList(
            Statistic.LIST_VIDEO,
            videos as ArrayList<out Parcelable?>
        )
        bundle.putLong(Statistic.DURATION, duration)
        
        requireFragmentManager().beginTransaction()
            .replace(R.id.view_container, SortMergerFragment.newInstance(bundle))
            .addToBackStack(null)
            .commit()
    }
    
    private fun setUpAdapters(builder: MultiSelectBuilder<VideoModel>) {
        leftListAudio = Utils.getVideos(context, 0, null, true)
        leftAdapter =
            LeftListAdapter(requireContext()) { position: Int -> mMultiSelect!!.select(position) }
        rightAdapter =
            RightListAdapter(requireContext()) { position: Int -> mMultiSelect!!.deselect(position) }
        leftAdapter!!.addAll(leftListAudio)
        builder.withLeftAdapter(leftAdapter!!)
            .withRightAdapter(rightAdapter!!)
        if (leftListAudio.size == 0) {
            tvNoAudioFile!!.visibility = View.VISIBLE
            findViewById(R.id.viewDetailsSelectFolder)?.visibility = View.GONE
            toolbar?.visibility = View.GONE
        } else {
            tvNoAudioFile!!.visibility = View.INVISIBLE
            viewChooseFile!!.visibility = View.VISIBLE
        }
    }

    override fun initViews() {
        tvNoAudioFile = findViewById(R.id.tv_no_audio) as TextView?
        viewChooseFile = findViewById(R.id.view_choose_file)
        
        // Check if we have pre-selected videos from MainActivity
        preSelectedVideos?.let { selectedVideos ->
            if (selectedVideos.isNotEmpty()) {
                // Skip the selection UI and go directly to merger
                navigateToMergerWithVideos(selectedVideos)
                preSelectedVideos = null // Clear after use
                return
            }
        }
        
        val builder = MultiSelectBuilder(VideoModel::class.java)
            .withContext(requireActivity())
            .mountOn((findViewById(R.id.viewDetailsSelectFolder) as ViewGroup?)!!)
            .withSidebarWidth((46 + 8 * 2).toFloat()) // ImageView width with paddings
        setUpAdapters(builder)
        mMultiSelect = builder.build()
        val it = IntentFilter()
        it.addAction(Statistic.UPDATE_CHOOSE_VIDEO)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            requireContext().registerReceiver(receiver, it, Context.RECEIVER_NOT_EXPORTED)
        } else {
            requireContext().registerReceiver(receiver, it)
        }
        isReceiverRegistered = true
    }

    override fun onDestroy() {
        if (isReceiverRegistered) {
            try {
                requireContext().unregisterReceiver(receiver)
                isReceiverRegistered = false
            } catch (e: IllegalArgumentException) {
                Timber.tag("DEBUG_FLOW").d("DetailsSelectFileFragment: Receiver already unregistered")
            }
        }
        super.onDestroy()
    }

    companion object {
        var preSelectedVideos: List<VideoModel>? = null
        
        fun newInstance(): DetailsSelectFileFragment {
            val args = Bundle()
            val fragment = DetailsSelectFileFragment()
            fragment.arguments = args
            return fragment
        }
    }
}