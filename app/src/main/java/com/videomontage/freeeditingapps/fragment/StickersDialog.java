package com.videomontage.freeeditingapps.fragment;

import android.os.Bundle;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.Toast;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.google.android.material.tabs.TabLayout;
import com.videomontage.freeeditingapps.R;
import com.videomontage.freeeditingapps.adapter.CustomAdapter;
import com.videomontage.freeeditingapps.fragment.viewmodel.StickerDialogViewModel;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager.widget.ViewPager;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 21/01/2017.
 */

public class StickersDialog extends DialogFragment {
    private static final String TAG = StickersDialog.class.getSimpleName();
    TabLayout tabLayout;
    ViewPager viewPager;
    CustomAdapter adapter;
    private StickerDialogViewModel stickerDialogViewModel;
    private ArrayList listGroupId = new ArrayList();

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        stickerDialogViewModel = new ViewModelProvider(requireActivity()).get(StickerDialogViewModel.class);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
//        getDialog().getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        getDialog().getWindow().getAttributes().windowAnimations = R.style.DialogAnimation;
        getDialog().getWindow().setGravity(Gravity.BOTTOM);
        getDialog().getWindow().clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);


        View rootview = inflater.inflate(R.layout.dialog_sample, container, false);

        tabLayout = (TabLayout) rootview.findViewById(R.id.tabLayout);
        viewPager = (ViewPager) rootview.findViewById(R.id.masterViewPager);

        adapter = new CustomAdapter(getChildFragmentManager());

//        stickerDialogViewModel.getStateListOfStickers().observe(this, lists -> {
//            switch (lists.getStatus()){
//                case SUCCESS:
//                    List<List<StickerCache>> list = lists.getData();
//                    for (int i = 0; i < list.size(); i++) {
//                        listGroupId.add(list.get(i).get(0).getGroupId());
//                        adapter.addFragment("Robot", CustomFragment.createInstance("Aeon", list.get(i)));
//                        adapter.notifyDataSetChanged();
//                    }
//                    break;
//                case ERROR:
//                    break;
//                case LOADING:
//                    break;
//            }
//
//
//        });



//        for (int i = 0; i < stickerGroups.size(); i++) {
//            adapter.addFragment("Robot",CustomFragment.createInstance("Aeon", StickersHelper.INSTANCE.getGroupedStickersList().get(i)));
//            adapter.notifyDataSetChanged();
//        }

        viewPager.setAdapter(adapter);
        tabLayout.setupWithViewPager(viewPager);



        return rootview;
    }

    @Override
    public void onStart() {
        super.onStart();
        getDialog().getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        String image_url = "https://firebasestorage.googleapis.com/v0/b/home-network-monitor-9ade8.appspot.com/o/video_montage%2Fstickers%2Fthankful%20stickers%2Fthankful%20stickers%201.png?alt=media&token=e596b1bc-b8f5-4163-9cbc-9ce141aa0dda";

//        stickerDialogViewModel.getStateListOfStickerGroupCache().observe(this, listStateData -> {
//            switch (listStateData.getStatus()) {
//                case SUCCESS:
////                    for (int i = 0; i < listGroupId.size(); i++) {
////                        tabLayout.getTabAt(i).setCustomView(createTabItemView(listStateData.getData()));
////
////                    }
//                    for (int i = 0; i < listStateData.getData().size(); i++) {
//                        tabLayout.getTabAt(i).setCustomView(createTabItemView(listStateData.getData().get(i).getGroupIconPath()));
//
//                        Log.d(TAG, "onChanged: " + listStateData.getData().get(i));
//                    }
//                    break;
//                case LOADING:
//                    Log.d(TAG, "onChanged: LOADING");
//                    Toast.makeText(getContext(), "Loading Stickers", Toast.LENGTH_SHORT).show();
//                    break;
//                case ERROR:
//                    Log.d(TAG, "onChanged: ERROR");
//                    Toast.makeText(getContext(), "Error occurred while loading stickers", Toast.LENGTH_SHORT).show();
//                    break;
//            }
//        });

//        for (int i = 0; i < stickerGroups.size(); i++) {
//            Log.d("ddd", "onStart: " + i);
//            tabLayout.getTabAt(i).setCustomView(createTabItemView(stickerGroups.get(i).getGroupIconPath()));
//        }
    }

    private View createTabItemView(String imgUri) {
        ImageView imageView = new ImageView(getContext());
        TabLayout.LayoutParams params = new TabLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        imageView.setLayoutParams(params);
        RequestOptions options = new RequestOptions()
                .centerCrop()
                .placeholder(R.mipmap.ic_launcher)
                .error(R.mipmap.ic_launcher);


        Glide.with(this).load(imgUri).apply(options).into(imageView);
        return imageView;
    }
}

