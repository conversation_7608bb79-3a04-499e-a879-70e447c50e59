package com.videomontage.freeeditingapps.fragment

import android.content.Context
import android.view.View
import android.widget.EditText
import android.widget.TextView
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.listener.IInputNameFile
import com.videomontage.freeeditingapps.utils.Utils

/**
 * Created by <PERSON> on 11/16/2018.
 */
class DialogInputName(
    context: Context,
    private val mCallback: IInputNameFile,
    nameDefault: String?,
    title: String?
) : AbsDialog(
    context
) {
    var edtNameFile: EditText? = null
    var nameDefault: String? = null
    var title: String? = null
    var tvTitle: TextView? = null

    init {
        this.nameDefault = nameDefault
        this.title = title
    }

    override fun initLayout(): Int {
        return R.layout.dialog_save_file
    }

    override fun initDialog() {
        super.initDialog()
        tvTitle = view?.findViewById(R.id.ss)
        tvTitle?.setText(title)
        edtNameFile = view?.findViewById(R.id.edt_name_file)
        edtNameFile?.setText(nameDefault)
        edtNameFile?.text?.let { edtNameFile?.setSelection(it.length) }
        view?.findViewById<View>(R.id.btn_local_ok)?.setOnClickListener { v: View? -> applyInput() }
        view?.findViewById<View>(R.id.btn_local_cancel)
            ?.setOnClickListener { v: View? -> cancelInput() }
    }

    private fun applyInput() {
        hideDialog()
        val nameFile = edtNameFile!!.text.toString().trim { it <= ' ' }
        if (nameFile.isEmpty()) {
            mCallback.onFileNameEmpty()
            return
        }
        if (Utils.isStringHasCharacterSpecial(nameFile)) {
            mCallback.onFileNameHasSpecialCharacter()
            return
        }
        mCallback.onApplySelect(edtNameFile!!.text.toString().trim { it <= ' ' })
    }

    private fun cancelInput() {
        mCallback.onCancelSelect()
    }
}