package com.videomontage.freeeditingapps.fragment

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.widget.Toolbar
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.activity.MainActivity
import com.videomontage.freeeditingapps.analytics.AmplitudeHelper
import com.videomontage.freeeditingapps.model.VideoModel
import com.videomontage.freeeditingapps.statistic.Statistic
import com.videomontage.freeeditingapps.utils.FabricEvents
import com.videomontage.freeeditingapps.utils.Flog
import com.videomontage.freeeditingapps.utils.SelectedFragmentHelper
import com.videomontage.freeeditingapps.utils.SharedPrefs
import com.videomontage.freeeditingapps.utils.StaticFinalValues
import com.videomontage.freeeditingapps.utils.UriToPathUtil
import timber.log.Timber

class ListVideoFragment : AbsFragment() {
    private var mCheckAction = 0 // action is check this fragment choose video is fragment cutter == 0,addmusic == 2,speed ==1
    private var btnSelectVideo: Button? = null
    private var tvInstructions: TextView? = null
    
    companion object {
        private const val REQUEST_VIDEO_PICK = 1001
        private const val REQUEST_MULTIPLE_VIDEO_PICK = 1002
        
        fun newInstance(bundle: Bundle): ListVideoFragment {
            val fragment = ListVideoFragment()
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_list_video_intent, container, false)
    }

    override fun initViews() {
        AmplitudeHelper.setFragmentListVideo()
        Timber.tag("DEBUG_FLOW").d("ListVideoFragment: Initializing Intent-based video selection")
        
        btnSelectVideo = findViewById(R.id.btn_select_video) as Button?
        tvInstructions = findViewById(R.id.tv_instructions) as TextView?
        
        setupUI()
    }
    
    private fun setupUI() {
        mCheckAction = requireArguments().getInt(Statistic.ACTION, 0)
        
        // Configure UI based on action type
        when (mCheckAction) {
            2 -> { // Merger - multiple video selection
                btnSelectVideo?.text = "Select Videos"
                tvInstructions?.text = "Tap to select multiple videos to merge together"
            }
            else -> { // Single video selection (Cutter, Speed, AddMusic, Effects)
                btnSelectVideo?.text = "Select Video"
                tvInstructions?.text = "Tap to select a video from your device"
            }
        }
        
        btnSelectVideo?.setOnClickListener {
            when (mCheckAction) {
                2 -> selectMultipleVideos() // Merger
                else -> selectSingleVideo() // Other modes
            }
        }
    }

    private fun selectSingleVideo() {
        Timber.tag("DEBUG_FLOW").d("ListVideoFragment: Starting single video selection")
        
        val intent = Intent(Intent.ACTION_GET_CONTENT).apply {
            type = "video/*"
            putExtra(Intent.EXTRA_LOCAL_ONLY, true)
            addCategory(Intent.CATEGORY_OPENABLE)
        }
        
        try {
            startActivityForResult(
                Intent.createChooser(intent, "Select Video"),
                REQUEST_VIDEO_PICK
            )
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e(e, "ListVideoFragment: Error starting video picker")
            Toast.makeText(context, "No video app found", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun selectMultipleVideos() {
        Timber.tag("DEBUG_FLOW").d("ListVideoFragment: Starting multiple video selection")
        
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            type = "video/*"
            putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
            addCategory(Intent.CATEGORY_OPENABLE)
        }
        
        try {
            startActivityForResult(intent, REQUEST_MULTIPLE_VIDEO_PICK)
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e(e, "ListVideoFragment: Error starting multiple video picker")
            Toast.makeText(context, "No video app found", Toast.LENGTH_SHORT).show()
        }
    }

    override fun initToolbar() {
        super.initToolbar()
        mCheckAction = requireArguments().getInt(Statistic.ACTION, 0)
        
        val listTitle = arrayOf(
            getString(R.string.cutter),
            getString(R.string.speed),
            getString(R.string.merger),
            getString(R.string.add_music),
            getString(R.string.common_effects),
            getString(R.string.common_edit_activity)
        )
        
        toolbar?.title = listTitle[mCheckAction]
        FabricEvents.logEvent(
            FabricEvents.MAIN_MANU_PRESSED,
            "toolbar_title",
            toolbar?.title?.toString() ?: ""
        )
        
        toolbar?.setNavigationOnClickListener {
            activity?.onBackPressed()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        if (resultCode != Activity.RESULT_OK || data == null) {
            Timber.tag("DEBUG_FLOW").d("ListVideoFragment: Video selection cancelled or failed")
            return
        }
        
        when (requestCode) {
            REQUEST_VIDEO_PICK -> {
                handleSingleVideoSelection(data)
            }
            REQUEST_MULTIPLE_VIDEO_PICK -> {
                handleMultipleVideoSelection(data)
            }
        }
    }
    
    private fun handleSingleVideoSelection(data: Intent) {
        val uri = data.data
        if (uri != null) {
            Timber.tag("DEBUG_FLOW").d("ListVideoFragment: Single video selected: $uri")
            
            // Create VideoModel from URI
            val videoModel = createVideoModelFromUri(uri)
            if (videoModel != null) {
                navigateToTargetFragment(videoModel)
            } else {
                Toast.makeText(context, "Unable to process selected video", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun handleMultipleVideoSelection(data: Intent) {
        val videoModels = mutableListOf<VideoModel>()
        
        // Handle multiple selection
        data.clipData?.let { clipData ->
            for (i in 0 until clipData.itemCount) {
                val uri = clipData.getItemAt(i).uri
                val videoModel = createVideoModelFromUri(uri)
                if (videoModel != null) {
                    videoModels.add(videoModel)
                }
            }
        } ?: data.data?.let { uri ->
            // Single selection through multiple picker
            val videoModel = createVideoModelFromUri(uri)
            if (videoModel != null) {
                videoModels.add(videoModel)
            }
        }
        
        Timber.tag("DEBUG_FLOW").d("ListVideoFragment: Multiple videos selected: ${videoModels.size}")
        
        if (videoModels.isNotEmpty()) {
            navigateToMergerFragment(videoModels)
        } else {
            Toast.makeText(context, "No valid videos selected", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun createVideoModelFromUri(uri: Uri): VideoModel? {
        return try {
            val context = requireContext()
            val contentResolver = context.contentResolver
            
            // Get FFmpeg-compatible file path
            val filePath = UriToPathUtil.getFFmpegCompatiblePath(context, uri)
            if (filePath == null) {
                Timber.tag("DEBUG_FLOW").e("ListVideoFragment: Unable to get file path for URI: $uri")
                return null
            }
            
            // Get basic info from ContentResolver
            val cursor = contentResolver.query(uri, null, null, null, null)
            var displayName = "Selected Video"
            var size = 0L
            
            cursor?.use {
                if (it.moveToFirst()) {
                    val nameIndex = it.getColumnIndex("_display_name")
                    val sizeIndex = it.getColumnIndex("_size")
                    
                    if (nameIndex != -1) {
                        displayName = it.getString(nameIndex) ?: "Selected Video"
                    }
                    if (sizeIndex != -1) {
                        size = it.getLong(sizeIndex)
                    }
                }
            }
            
            VideoModel(
                uri.toString(), // id
                displayName, // nameAudio (title)
                "", // nameArtist
                "", // nameAlbum
                "0", // duration (will be determined during processing)
                filePath, // path (FFmpeg-compatible file path)
                "", // resolution
                size, // size
                System.currentTimeMillis().toString() // dateModifier
            )
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e(e, "ListVideoFragment: Error creating VideoModel from URI")
            null
        }
    }
    
    private fun navigateToTargetFragment(videoModel: VideoModel) {
        Timber.tag("DEBUG_FLOW").d("ListVideoFragment: Navigating to fragment for action: $mCheckAction")
        
        // Set the selected video model globally for fragments to use
        Statistic.SELECTED_VIDEO_MODEL = videoModel
        
        // Use SelectedFragmentHelper to navigate to the appropriate fragment
        SelectedFragmentHelper.addFragment(mCheckAction, parentFragmentManager, requireActivity())
    }
    
    private fun navigateToMergerFragment(videoModels: List<VideoModel>) {
        Timber.tag("DEBUG_FLOW").d("ListVideoFragment: Navigating to MergerFragment with ${videoModels.size} videos")
        
        if (videoModels.isNotEmpty()) {
            // For merger, set the first video as the selected model and pass the list separately
            Statistic.SELECTED_VIDEO_MODEL = videoModels[0]
            // TODO: Need to implement proper multiple video passing to MergerFragment
            // For now, just navigate to merger with the first video
            SelectedFragmentHelper.addFragment(MainActivity.INDEX_MERGER, parentFragmentManager, requireActivity())
        }
    }

}