package com.videomontage.freeeditingapps.fragment

import android.app.ProgressDialog
import android.content.Intent
import android.media.MediaPlayer
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.util.Log
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.widget.SearchView
import androidx.appcompat.widget.Toolbar
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.arthenica.mobileffmpeg.Config
import com.arthenica.mobileffmpeg.FFmpeg
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.adapter.MusicAdapter
import com.videomontage.freeeditingapps.model.ComModel
import com.videomontage.freeeditingapps.model.MusicModel
import com.videomontage.freeeditingapps.statistic.Statistic
import com.videomontage.freeeditingapps.utils.Flog
import com.videomontage.freeeditingapps.utils.SharedPrefs
import com.videomontage.freeeditingapps.utils.Utils
import com.videomontage.freeeditingapps.view.RangeSeekBar
import com.videomontage.freeeditingapps.view.RangeSeekBar.OnRangeSeekBarChangeListener
import java.io.File
import java.io.IOException

//import com.github.hiteshsondhi88.libffmpeg.ExecuteBinaryResponseHandler;
//import com.github.hiteshsondhi88.libffmpeg.FFmpeg;
//import com.github.hiteshsondhi88.libffmpeg.exceptions.FFmpegCommandAlreadyRunningException;
/**
 * Created by Hung on 11/19/2018.
 */
class ChooseMusicFragment : AbsFragment(), MusicAdapter.ItemSelected, View.OnClickListener {
    private var musicModelList: MutableList<MusicModel>? = ArrayList()
    private var musicAdapter: MusicAdapter? = null
    private var rvAudio: RecyclerView? = null
    var durationAudio = 0
    private val adapter: MusicAdapter? = null
    private val mFile: File? = null
    private var mFilename = "record"
    private val mHandler: Handler? = null
    private val mLoadingKeepGoing = false
    private val mLoadingLastUpdateTime: Long = 0
    private val musicList: RecyclerView? = null
    private val mPlayer: MediaPlayer? = null
    private var mProgressDialog: ProgressDialog? = null
    private val mRecordingFilename: String? = null
    private val mRecordingUri: Uri? = null
    private var tvNameSong: TextView? = null
    private var rangeSeekbar: RangeSeekBar<Int>? = null
    private var searchView: SearchView? = null
    private var selectedSong: MusicModel? = null
    override var toolbar: Toolbar? = null
    private var ivPrevious: ImageView? = null
    private var ivNext: ImageView? = null
    private var ivPlay: ImageView? = null
    private var mediaPlayer: MediaPlayer? = null
    private var position = 0

    //    private FFmpeg ffmpeg;
    //    com.github.kohiyadav.libffmpeg.FFmpeg ffMpegTest;
    private var outPath: String? = null
    private var tvAddMusic: TextView? = null
    private var isSuccess = false
//    private val mAdView: AdView? = null
    private var isOnPause = false
    private var handler: Handler? = null
    private var tvNoAudio: TextView? = null
    private val listAllSong: MutableList<MusicModel> = ArrayList()
    private var viewPlay: View? = null
    private var isMaxRangerSeekbar = false
    override fun initViews() {

//        ffmpeg = FFmpeg.getInstance(getContext());
//        ffMpegTest =com.github.kohiyadav.libffmpeg.FFmpeg.getInstance(getContext());
        listAllSong.clear()
        listAllSong.addAll(Utils.getMusicFiles(context))
        musicModelList!!.clear()
        musicModelList!!.addAll(listAllSong)
        musicAdapter = MusicAdapter(musicModelList!!, this)
        rvAudio = findViewById(R.id.rvMusicList) as RecyclerView?
        rvAudio!!.setHasFixedSize(true)
        rvAudio!!.layoutManager = LinearLayoutManager(context)
        rvAudio!!.adapter = musicAdapter
        tvNoAudio = findViewById(R.id.tv_no_audio) as TextView?
        ivNext = findViewById(R.id.iv_next) as ImageView?
        ivPrevious = findViewById(R.id.iv_previous) as ImageView?
        ivPlay = findViewById(R.id.iv_play) as ImageView?
        tvAddMusic = findViewById(R.id.tv_add_music) as TextView?
        tvNameSong = findViewById(R.id.tv_name_song) as TextView?
        rangeSeekbar = findViewById(R.id.rangeSeekbar) as RangeSeekBar<Int>?
        viewPlay = findViewById(R.id.lnView)
        ivNext!!.setOnClickListener(this)
        ivPlay!!.setOnClickListener(this)
        ivPrevious!!.setOnClickListener(this)
        hasAudio()
    }

    private fun hasAudio() {
        if (musicModelList == null) {
            return
        }
        if (musicModelList!!.size > 0) {
            tvNoAudio!!.visibility = View.GONE
        } else {
            tvNoAudio!!.visibility = View.VISIBLE
        }
    }

    override fun initToolbar() {
        super.initToolbar()
        toolbar!!.menu.clear()
        toolbar!!.inflateMenu(R.menu.menu_search_song)
        toolbar!!.menu.findItem(R.id.item_done)
            .setOnMenuItemClickListener { menuItem: MenuItem? -> addMusic() }
        searchAudio(toolbar)
    }

    private fun searchAudio(toolbar: Toolbar?) {
        val menuItem = toolbar!!.menu.findItem(R.id.item_search)
        searchView = menuItem.actionView as SearchView?
        searchView!!.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(s: String): Boolean {
                // actionSearch(s);
                return true
            }

            override fun onQueryTextChange(s: String): Boolean {
                actionSearch(s)
                return true
            }
        })
    }

    private fun actionSearch(s: String) {
        val currentMusicModelList = Utils.filterSong(listAllSong, s)
        musicModelList = currentMusicModelList // Update the global list if necessary
        musicAdapter?.setFilter(currentMusicModelList) // Use the local variable
    }

    private fun addMusic(): Boolean {
        if (isExistSong) {
            onSave()
        } else {
            requireFragmentManager().popBackStack()
        }
        return true
    }

    override fun onResume() {
        super.onResume()
        isOnPause = false
    }

    override fun onPause() {
        super.onPause()
        pauseMediaPlayer()
    }

    private val STATE_STOP = 0
    private val STATE_PLAYING = 1
    private val STATE_PAUSE = 2
    private var STATE_PLAY_MUSIC = STATE_STOP
    fun playMusic(index: Int) {
        if (STATE_PLAY_MUSIC == STATE_PLAYING) {
            pauseMediaPlayer()
        } else if (STATE_PLAY_MUSIC == STATE_PAUSE) {
            startMediaPlayer()
        } else {
            position = index
            if (position > musicModelList!!.size - 1) {
                position = musicModelList!!.size - 1
            }
            selectedSong = musicModelList!![position]
            mFilename = selectedSong!!.filePath
            tvNameSong!!.text = selectedSong!!.title
            rangeSeekbar!!.setRangeValues(0, selectedSong?.duration.toString().toInt())
            rangeSeekbar!!.setSelectedMinValue(0)
            rangeSeekbar!!.setSelectedMaxValue(selectedSong!!.duration.toString().toInt())
            rangeSeekbar!!.setOnRangeSeekBarChangeListener(OnRangeSeekBarChangeListener { bar: RangeSeekBar<Int>?, minValue: Number?, maxValue: Number? ->
                isChangeSeekbar = true
                pauseMediaPlayer()
            })
            stopMedia()
            playAudio()
        }
    }

    private var isChangeSeekbar = false
    private fun pauseMediaPlayer() {
        if (mediaPlayer == null) {
            return
        }
        mediaPlayer!!.pause()
        ivPlay!!.setImageResource(R.drawable.ic_play_arrow_black_24dp)
        STATE_PLAY_MUSIC = STATE_PAUSE
    }

    private fun startMediaPlayer() {
        if (mediaPlayer == null) return
        mediaPlayer!!.start()
        if (isChangeSeekbar) {
            mediaPlayer!!.seekTo((rangeSeekbar!!.selectedMinValue as Int))
            isChangeSeekbar = false
        } else {
            mediaPlayer!!.start()
        }
        if (isMaxRangerSeekbar) {
            isMaxRangerSeekbar = false
            mediaPlayer!!.seekTo((rangeSeekbar!!.selectedMinValue as Int))
        }
        ivPlay!!.setImageResource(R.drawable.ic_pause_black_24dp)
        STATE_PLAY_MUSIC = STATE_PLAYING
    }

    private fun playAudio() {
        Flog.e(" xxx  play")
        STATE_PLAY_MUSIC = STATE_PLAYING
        val timeStart = (rangeSeekbar!!.selectedMinValue.toString() + "").toInt() / 1000
        val timeEnd = (rangeSeekbar!!.selectedMaxValue.toString() + "").toInt() / 1000
        val timePlay = timeEnd - timeStart
        if (timePlay > 0) {
            try {
                mediaPlayer = MediaPlayer()
                mediaPlayer!!.setDataSource(selectedSong!!.filePath)
                mediaPlayer!!.prepare()
                mediaPlayer!!.setOnPreparedListener { mp: MediaPlayer? ->
                    mediaPlayer!!.start()
                    mediaPlayer!!.seekTo((rangeSeekbar!!.selectedMinValue as Int))
                }
                mediaPlayer!!.setOnCompletionListener { mp: MediaPlayer? ->
                    //mediaPlayer.stop();
                    Flog.e(" onComplelteeeeeeeeee")
                    STATE_PLAY_MUSIC = STATE_STOP
                }
                ivPlay!!.setImageResource(R.drawable.ic_pause_black_24dp)
            } catch (e: IOException) {
                if (position < musicModelList!!.size - 1) {
                    position++
                }
                playMusic(position)
            }
        } else {
            Toast.makeText(context, getString(R.string.time_fail), Toast.LENGTH_SHORT).show()
        }
        if (handler == null) {
            handler = Handler()
        }
        handler!!.postDelayed(object : Runnable {
            override fun run() {
                if (mediaPlayer != null) {
                    if (mediaPlayer!!.currentPosition >= rangeSeekbar!!.selectedMaxValue as Int) {
                        ivPlay!!.setImageResource(R.drawable.ic_play_arrow_black_24dp)
                        //                        mediaPlayer.stop();
//                        mediaPlayer.reset();
//                        mediaPlayer.release();
//                        mediaPlayer = null;
                        isMaxRangerSeekbar = true
                        mediaPlayer!!.pause()
                        STATE_PLAY_MUSIC = STATE_PAUSE
                    }
                }
                handler!!.postDelayed(this, 1000)
            }
        }, 1000)
    }

    private fun stopMedia() {
        STATE_PLAY_MUSIC = STATE_STOP
        ivPlay!!.setImageResource(R.drawable.ic_play_arrow_black_24dp)
        if (mediaPlayer == null) return
        mediaPlayer!!.stop()
        mediaPlayer!!.reset()
        mediaPlayer!!.release()
        mediaPlayer = null
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_choose_music, container, false)
    }

    private var pathOldFile: String? = null
    override fun onClick(position: Int) {
        this.position = position
        pathOldFile = musicModelList!![position].filePath
        STATE_PLAY_MUSIC = STATE_STOP
        playMusic(position)
        if (viewPlay!!.visibility == View.GONE) {
            viewPlay!!.visibility = View.VISIBLE
        }
    }

    private val isExistSong: Boolean
        private get() = musicModelList != null && musicModelList!!.size > 0

    private fun onSave() {
        if (mediaPlayer != null && mediaPlayer!!.isPlaying) {
            mediaPlayer!!.pause()
            ivPlay!!.setImageResource(R.drawable.ic_play_arrow_black_24dp)
        }
        saveRingtone("temp")
    }

    private fun saveRingtone(title: String) {
        val externalFilesDir = context?.getExternalFilesDir(null)?.absolutePath
        if (externalFilesDir == null) {
            Toast.makeText(context, "Cannot access external files directory", Toast.LENGTH_SHORT).show()
            return
        }

        // Use the same extension as the input file to avoid codec conflicts
        val inputExtension = File(musicModelList!![position].filePath).extension
        outPath = "$externalFilesDir/$title.$inputExtension"
        timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: saveRingtone output path: $outPath")
        timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: Input file path: ${musicModelList!![position].filePath}")
        timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: Input file pathOldFile: $pathOldFile")

        var timeStart = (rangeSeekbar!!.selectedMinValue.toString() + "").toInt()
        var timeEnd = (rangeSeekbar!!.selectedMaxValue.toString() + "").toInt()
        timeStart = timeStart / 1000
        timeEnd = timeEnd / 1000
        durationAudio = timeEnd - timeStart

        timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: Time range - start: ${timeStart}s, end: ${timeEnd}s, duration: ${durationAudio}s")

        if (durationAudio > 0) {
            val command: Array<String?>
            val commandNewApi: String
            
            // Build command with proper path escaping
            val inputPath = ComModel.getCharEscaptedPath(musicModelList!![position].filePath)
            timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: Escaped input path: $inputPath")
            
            // Simple approach: just trim the audio without re-encoding
            // This should work for any input format and avoid codec issues
            timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: Using stream copy to trim audio")
            
            command = arrayOf(
                "-y",
                "-i", musicModelList!![position].filePath,
                "-ss", timeStart.toString(),
                "-t", durationAudio.toString(),
                "-c", "copy",
                outPath
            )
            
            commandNewApi = "-y -i \"${musicModelList!![position].filePath}\" -ss $timeStart -t $durationAudio -c copy \"$outPath\""

            timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: Array command: ${command.joinToString(" ")}")
            timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: String command: $commandNewApi")

            mProgressDialog = ProgressDialog(context)
            mProgressDialog!!.setProgressStyle(ProgressDialog.STYLE_SPINNER)
            mProgressDialog!!.setTitle(R.string.progress_dialog_saving)
            mProgressDialog!!.isIndeterminate = true
            mProgressDialog!!.setCancelable(false)
            mProgressDialog!!.show()

            // Try array command first to avoid path escaping issues
            timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: Trying array-based command first")
            execFFmpegBinary(command)
        } else {
            Toast.makeText(context, getString(R.string.time_fail), Toast.LENGTH_SHORT).show()
        }
    }

    private fun execFFmpegBinary(command: Any) {
        // Enable detailed FFmpeg logging with multiple levels
        Config.enableLogCallback { message ->
            when (message.level) {
                com.arthenica.mobileffmpeg.Level.AV_LOG_ERROR -> {
                    timber.log.Timber.tag("FFMPEG_ERROR").e("FFmpeg ERROR: ${message.text}")
                }
                com.arthenica.mobileffmpeg.Level.AV_LOG_WARNING -> {
                    timber.log.Timber.tag("FFMPEG_WARN").w("FFmpeg WARN: ${message.text}")
                }
                else -> {
                    timber.log.Timber.tag("FFMPEG_LOG").d("FFmpeg: ${message.text}")
                }
            }
        }
        
        when (command) {
            is String -> {
                timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: Executing FFmpeg string command: $command")
                
                val executionId = FFmpeg.executeAsync(command) { _, rc ->
                    timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: FFmpeg execution completed with return code: $rc")
                    
                    when (rc) {
                        Config.RETURN_CODE_SUCCESS -> {
                            timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: FFmpeg command execution completed successfully")
                            afterFFmpegSuccess()
                        }
                        Config.RETURN_CODE_CANCEL -> {
                            timber.log.Timber.tag("DEBUG_FLOW").w("ChooseMusicFragment: FFmpeg command execution cancelled by user")
                            afterFFmpegFailure()
                        }
                        else -> {
                            timber.log.Timber.tag("DEBUG_FLOW").e("ChooseMusicFragment: FFmpeg command execution failed with rc=$rc")
                            timber.log.Timber.tag("DEBUG_FLOW").e("ChooseMusicFragment: Printing last FFmpeg output:")
                            Config.printLastCommandOutput(Log.ERROR)
                            afterFFmpegFailure()
                        }
                    }
                }
                
                timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: FFmpeg execution started with ID: $executionId")
            }
            is Array<*> -> {
                val arrayCommand = command as Array<String?>
                timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: Executing FFmpeg array command: ${arrayCommand.joinToString(" ")}")
                
                val executionId = FFmpeg.executeAsync(arrayCommand) { _, rc ->
                    timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: FFmpeg execution completed with return code: $rc")
                    
                    when (rc) {
                        Config.RETURN_CODE_SUCCESS -> {
                            timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: FFmpeg command execution completed successfully")
                            afterFFmpegSuccess()
                        }
                        Config.RETURN_CODE_CANCEL -> {
                            timber.log.Timber.tag("DEBUG_FLOW").w("ChooseMusicFragment: FFmpeg command execution cancelled by user")
                            afterFFmpegFailure()
                        }
                        else -> {
                            timber.log.Timber.tag("DEBUG_FLOW").e("ChooseMusicFragment: FFmpeg command execution failed with rc=$rc")
                            timber.log.Timber.tag("DEBUG_FLOW").e("ChooseMusicFragment: Printing last FFmpeg output:")
                            Config.printLastCommandOutput(Log.ERROR)
                            afterFFmpegFailure()
                        }
                    }
                }
                
                timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: FFmpeg execution started with ID: $executionId")
            }
            else -> {
                timber.log.Timber.tag("DEBUG_FLOW").e("ChooseMusicFragment: Unknown command type: ${command::class.java}")
                afterFFmpegFailure()
            }
        }
    }
    
    private fun afterFFmpegFailure() {
        timber.log.Timber.tag("DEBUG_FLOW").e("ChooseMusicFragment: Processing failed, dismissing progress dialog")
        if (mProgressDialog != null && mProgressDialog!!.isShowing) {
            mProgressDialog!!.dismiss()
        }
        Toast.makeText(context, "Failed to process audio file", Toast.LENGTH_SHORT).show()
    }

    private fun afterFFmpegSuccess() {
        isSuccess = true
        timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: FFmpeg processing completed successfully")
        timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: Output file created at: $outPath")
        
        if (isSuccess) {
            // Check if output file was actually created
            val outputFile = File(outPath ?: "")
            if (outputFile.exists() && outputFile.length() > 0) {
                timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: Output file verified - size: ${outputFile.length()} bytes")
                
                mProgressDialog!!.dismiss()
                requireContext().sendBroadcast(
                    Intent(Statistic.SEND_PATH_ADD_MUSIC).putExtra(
                        Statistic.PATH_MUSIC,
                        outPath
                    )
                )
                timber.log.Timber.tag("DEBUG_FLOW").d("ChooseMusicFragment: Broadcast sent with music path, navigating back")
                requireActivity().onBackPressed()
            } else {
                timber.log.Timber.tag("DEBUG_FLOW").e("ChooseMusicFragment: Output file not created or empty")
                afterFFmpegFailure()
            }
        } else {
            SharedPrefs.instance?.put(Statistic.MUSIC, 0)
            mProgressDialog!!.dismiss()
            Toast.makeText(context, getString(R.string.choose_other_file), Toast.LENGTH_SHORT)
                .show()
        }
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.iv_play -> if (isExistSong) {
                playMusic(position)
            }

            R.id.iv_next -> {
                STATE_PLAY_MUSIC = STATE_STOP
                if (isExistSong) {
                    if (position < musicModelList!!.size - 1) {
                        position++
                    }
                    playMusic(position)
                }
            }

            R.id.iv_previous -> {
                STATE_PLAY_MUSIC = STATE_STOP
                if (isExistSong) {
                    if (position > 0) {
                        position--
                    }
                    playMusic(position)
                }
            }
        }
    }

    companion object {
        fun newInstance(): ChooseMusicFragment {
            val args = Bundle()
            val fragment = ChooseMusicFragment()
            fragment.arguments = args
            return fragment
        }
    }
}