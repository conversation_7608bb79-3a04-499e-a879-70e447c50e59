package com.videomontage.freeeditingapps.fragment;

import android.app.ProgressDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Outline;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.widget.TextView;
import android.widget.Toast;

import com.arthenica.mobileffmpeg.Config;
import com.arthenica.mobileffmpeg.ExecuteCallback;
import com.arthenica.mobileffmpeg.LogCallback;
import com.arthenica.mobileffmpeg.LogMessage;
//import com.github.hiteshsondhi88.libffmpeg.ExecuteBinaryResponseHandler;
//import com.github.hiteshsondhi88.libffmpeg.FFmpeg;
//import com.github.hiteshsondhi88.libffmpeg.exceptions.FFmpegCommandAlreadyRunningException;
import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.PlaybackParameters;
import com.google.android.exoplayer2.source.ProgressiveMediaSource;
import com.google.android.exoplayer2.source.LoopingMediaSource;
import com.google.android.exoplayer2.source.MediaSource;
import com.google.android.exoplayer2.trackselection.DefaultTrackSelector;
import com.google.android.exoplayer2.ui.PlayerView;
import com.google.android.exoplayer2.upstream.DefaultDataSourceFactory;
import com.videomontage.freeeditingapps.R;
import com.videomontage.freeeditingapps.analytics.AmplitudeHelper;
import com.videomontage.freeeditingapps.analytics.FragmentAnalytics;
import com.videomontage.freeeditingapps.analytics.TimeWatch;
import com.videomontage.freeeditingapps.application.MyApplication;
import com.videomontage.freeeditingapps.listener.IInputNameFile;
import com.videomontage.freeeditingapps.model.ComModel;
import com.videomontage.freeeditingapps.model.VideoModel;
import com.videomontage.freeeditingapps.statistic.Statistic;
import com.videomontage.freeeditingapps.utils.FileUtil;
import com.videomontage.freeeditingapps.utils.Flog;
import com.videomontage.freeeditingapps.utils.Utils;
import com.videomontage.freeeditingapps.utils.VideoUtil;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

import static com.arthenica.mobileffmpeg.Config.RETURN_CODE_CANCEL;
import static com.arthenica.mobileffmpeg.Config.RETURN_CODE_SUCCESS;

/**
 * Created by Hung on 11/15/2018.
 */

public class SpeedFragment extends AbsFragment implements IInputNameFile, com.videomontage.freeeditingapps.custom.BubbleSeekBar.OnProgressChangedListener {
    private DialogInputName dialogInputName;
//    private FFmpeg ffmpeg;

    private VideoModel videoModel;
    private ProgressDialog progressDialog;
    private TextView tvShowSpeed;
    private boolean isSuccessCreate = false;
    private SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.US);
    private com.videomontage.freeeditingapps.custom.BubbleSeekBar seekBar;
    private float tempoVideo = 1.0f, ptsVideo = 1.0f;
    private float listTempoAudio[] = new float[]{0.5f, 0.75f, 1.0f, 1.25f, 1.5f, 1.75f, 2.0f};
    private float listPtsVideo[] = new float[]{2.0f, 4 / 3f, 1.0f, 4 / 5f, 4 / 6f, 4 / 7f, 0.5f};
    private boolean ffmpegDone;

    private PlayerView videoView;
    private ExoPlayer player;
    private View view;
    private boolean hasAudio;
    private boolean ffmpegFail;

    private TimeWatch watch;
    private FragmentAnalytics fragmentAnalytics;

    @Override
    public void initViews() {
        watch = TimeWatch.start();
        fragmentAnalytics = new FragmentAnalytics("SpeedFragment");
        AmplitudeHelper.setSpeedOpened();

        tvShowSpeed = (TextView) findViewById(R.id.tv_show_speed);
        seekBar = (com.videomontage.freeeditingapps.custom.BubbleSeekBar) findViewById(R.id.demo_3_seek_bar_1);
        seekBar.setOnProgressChangedListener(this);
        seekBar.getConfigBuilder()
                .min(1)
                .max(7)
                .progress(3)
                .sectionCount(6)
                .trackColor(Color.WHITE)
                .secondTrackColor(ContextCompat.getColor(getContext(), R.color.colorAccent))
                .thumbColor(ContextCompat.getColor(getContext(), R.color.colorAccent))
                .bubbleTextColor(ContextCompat.getColor(getContext(), android.R.color.transparent))
                .sectionTextColor(ContextCompat.getColor(getContext(), R.color.colorPrimary))
                .sectionTextSize(18)
                .bubbleColor(ContextCompat.getColor(getContext(), android.R.color.transparent))
                .bubbleTextSize(18)
                .showSectionMark()
                .seekStepSection()
                .touchToSeek()
                .build();

//        ffmpeg = FFmpeg.getInstance(getContext());

        videoModel = getArguments().getParcelable(Statistic.VIDEO_MODEL);

        Flog.e("        " + videoModel.getPath());

        getToolbar().setTitle(getString(R.string.speed));
        getToolbar().getMenu().findItem(R.id.item_save).setOnMenuItemClickListener(menuItem -> dialogLocalSave());

//        initVideo();
    }

    @Override
    public void onDestroy() {

        fragmentAnalytics.setDuration(watch.time(TimeUnit.SECONDS));
        MyApplication.addEvent(fragmentAnalytics);

        super.onDestroy();
    }

    @Override
    public void onResume() {
        super.onResume();
        initVideo();
    }

    @Override
    public void onPause() {
        super.onPause();
        pauseVideo();
    }

    private void pauseVideo() {
        if (player != null) {
            player.setPlayWhenReady(false);

            releasePlayer();
        }
    }

    private void releasePlayer() {
        if (player != null) {
            player.stop();
            player.release();
            player = null;
        }
    }

    private boolean dialogLocalSave() {
        fragmentAnalytics.addClick("Save");

        AmplitudeHelper.setSave();

        pauseVideo();
        String defaultName = "VS_" + simpleDateFormat.format(System.currentTimeMillis());
        dialogInputName = new DialogInputName(getContext(), this, defaultName, getString(R.string.save));
        dialogInputName.initDialog();
        return true;
    }

    public static SpeedFragment newInstance(Bundle bundle) {
        SpeedFragment fragment = new SpeedFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    private String newPath = null, oldPath = null;


    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        view = inflater.inflate(R.layout.fragment_speed, container, false);
        ;
        return view;

    }

    private void initVideo() {
        initVideoView();

    }

    private void initVideoView() {

        hasAudio = VideoUtil.isVideoHaveAudioTrack(videoModel.getPath());
        videoView = view.findViewById(R.id.videoView);


        try {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (player == null) {
                        Context context = getContext(); // or requireContext(), or use your Activity
                        player = new ExoPlayer.Builder(context).build();
                        videoView.setShutterBackgroundColor(Color.TRANSPARENT);
                    }
                    MediaItem mediaItem = MediaItem.fromUri(Uri.parse(videoModel.getPath()));
                    MediaSource assetVideo = new ProgressiveMediaSource.Factory(
                            new DefaultDataSourceFactory(getActivity().getApplicationContext(), "MyExoplayer")
                    ).createMediaSource(mediaItem);
                    player.setPlaybackParameters(new PlaybackParameters(tempoVideo));
                    player.prepare(new LoopingMediaSource(assetVideo));
                    videoView.setPlayer(player);

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                        videoView.setOutlineProvider(new ViewOutlineProvider() {
                            @Override
                            public void getOutline(View view, Outline outline) {
                                outline.setRoundRect(0, 0, view.getWidth(), view.getHeight(), 15);
                            }
                        });
                        videoView.setClipToOutline(true);
                    }

                    player.setPlayWhenReady(true);

                }
            }, 300);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public void onApplySelect(String nameFile) {
        saveFile(nameFile);
    }

    private void saveFile(String nameFile) {

        isCancelSaveFile = false;

        String extensionFile = null;

        if (FileUtil.isEmpty(nameFile)) {
            Toast.makeText(getContext(), getString(R.string.name_file_can_not_empty), Toast.LENGTH_SHORT).show();
            return;
        }

        if (Utils.isStringHasCharacterSpecial(nameFile)) {
            Toast.makeText(getContext(), getString(R.string.name_file_can_not_contain_character), Toast.LENGTH_SHORT).show();
            return;
        }

//        newPath = Environment.getExternalStorageDirectory().getAbsolutePath() + Statistic.DIR_APP + Statistic.DIR_SPEED + "/";
        newPath = Environment.getExternalStoragePublicDirectory(
                Environment.DIRECTORY_DCIM).toString()
                + File.separator + "Montage"
                + Statistic.DIR_SPEED + "/";

        File f = new File(newPath);
        if (!f.exists()) f.mkdirs();

        extensionFile = Utils.getFileExtension(videoModel.getPath());

        newPath = newPath + nameFile + extensionFile;

        if (new File(newPath).exists()) {
            dialogInputName.hideDialog();
            Toast.makeText(getContext(), getString(R.string.name_file_exist), Toast.LENGTH_SHORT).show();
            return;
        }

        String[] complexCommand;

        if (hasAudio) {
            String sSpeed = "[0:v]setpts=" + ptsVideo + "*PTS[v];[0:a]atempo=" + tempoVideo + "[a]";
            complexCommand = new String[]{"-i", videoModel.getPath(), "-filter_complex", sSpeed, "-map", "[v]", "-map", "[a]", "-preset", "ultrafast", newPath};

        } else {
            String sSpeed = "[0:v]setpts=" + ptsVideo + "*PTS[v]";
            complexCommand = new String[]{"-i", videoModel.getPath(), "-filter_complex", sSpeed, "-map", "[v]", "-preset", "ultrafast", newPath};

        }

        String commandNewApi;
        if (hasAudio) {
            String sSpeed = "[0:v]setpts=" + ptsVideo + "*PTS[v];[0:a]atempo=" + tempoVideo + "[a]";
            commandNewApi = "-i " + ComModel.getCharEscaptedPath(videoModel.getPath()) + " -filter_complex " + sSpeed + " -map [v] -map [a] -preset ultrafast " + newPath;

        } else {
            String sSpeed = "[0:v]setpts=" + ptsVideo + "*PTS[v]";
            commandNewApi = "-i " + ComModel.getCharEscaptedPath(videoModel.getPath()) + " -filter_complex " + sSpeed + " -map [v] -preset ultrafast " + newPath;

        }

        initDialogProgress();
//        showInterstitial();


        if (ComModel.isNewApiRequired())
            execFFmpegBinary(commandNewApi, newPath, nameFile);
        else
            execFFmpegBinary(complexCommand, newPath, nameFile);

    }

    private void initDialogProgress() {
        progressDialog = new ProgressDialog(getContext());
        progressDialog.setCancelable(false);
        progressDialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
        progressDialog.setTitle(getString(R.string.progress_dialog_saving));
        progressDialog.setProgress(0);
        progressDialog.setButton(DialogInterface.BUTTON_NEGATIVE, getString(R.string.cancel), (dialog, which) -> cancelCreateFile());
        progressDialog.show();
    }

    private boolean isCancelSaveFile = false;

    private void cancelCreateFile() {
        isCancelSaveFile = true;

//        if (ffmpeg.isFFmpegCommandRunning()) {
//            ffmpeg.killRunningProcesses();
//        }


        if (newPath != null) {
            new File(newPath).delete();
        }

        if (progressDialog != null) {
            progressDialog.dismiss();
        }

        initVideo();
    }

    @Override
    public void afterInterstitial() {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (!ffmpegFail)
                    pauseVideo();


                if (ffmpegDone) {
                    afterFFmpegOnSuccess();
                }
                ffmpegDone = false;

            }
        }, 1000);

    }

    private void execFFmpegBinary(final Object command, String path, String title) {

        if (ComModel.isNewApiRequired()) {
            String myCommand = (String) command;
            ffmpegDone = false;

            long executionId = com.arthenica.mobileffmpeg.FFmpeg.executeAsync(myCommand, new ExecuteCallback() {

                @Override
                public void apply(final long executionId, final int rc) {
                    if (rc == RETURN_CODE_SUCCESS) {
                        Log.i(Config.TAG, "Command execution completed successfully.");
                        afterFFmpegSuccess(path, title);

                    } else if (rc == RETURN_CODE_CANCEL) {
                        Log.i(Config.TAG, "Command execution cancelled by user.");
                        afterFFmpegFailure();

                    } else {
                        Log.i(Config.TAG, String.format("Command execution failed with rc=%d and the output below.", rc));
                        Config.printLastCommandOutput(Log.INFO);
                        afterFFmpegFailure();


                    }
                }
            });

            Config.enableLogCallback(new LogCallback() {
                public void apply(LogMessage message) {
//                    Log.d(Config.TAG, message.getText());
                    double durationFile = (int) Utils.getProgress(message.getText(), Long.parseLong(videoModel.getDuration()) / 1000) * tempoVideo;
                    double percent = durationFile / (Double.parseDouble(videoModel.getDuration()) / 1000);
                    Log.e("xxx", " durrrrrr  " + durationFile + "___" + percent * 100);
                    if (progressDialog != null) {
                        if ((int) (percent * 100) > 0) {
                            progressDialog.setProgress((int) (percent * 100));
                        }
                    }
                }
            });

        }
//        else {
//            try {
//                ffmpeg.execute((String[]) command, new ExecuteBinaryResponseHandler() {
//                    @Override
//                    public void onFailure(String s) {
//                        Flog.e("Successs     " + s);
//
//                        afterFFmpegFailure();
//                    }
//
//                    @Override
//                    public void onSuccess(String s) {
//                        Flog.e("Failllllllll   " + s);
//                        afterFFmpegSuccess(path, title);
//
//                    }
//
//                    @Override
//                    public void onProgress(String s) {
//                        Flog.e(s);
//                        double durationFile = (int) Utils.getProgress(s, Long.parseLong(videoModel.getDuration()) / 1000) * tempoVideo;
//                        double percent = durationFile / (Double.parseDouble(videoModel.getDuration()) / 1000);
//                        Log.e("xxx", " durrrrrr  " + durationFile + "___" + percent * 100);
//                        if (progressDialog != null) {
//                            if ((int) (percent * 100) > 0) {
//                                progressDialog.setProgress((int) (percent * 100));
//                            }
//                        }
//                    }
//
//                    @Override
//                    public void onStart() {
//
//                    }
//
//                    @Override
//                    public void onFinish() {
//
//                    }
//                });
//
//            } catch (FFmpegCommandAlreadyRunningException e) {
//                e.printStackTrace();
//            }
//        }

    }

    private void afterFFmpegFailure() {
        ffmpegFail = true;
        Toast.makeText(getContext(), getString(R.string.can_not_create_file), Toast.LENGTH_LONG).show();
        if (progressDialog != null) {
            progressDialog.dismiss();
        }
    }

    private void afterFFmpegSuccess(String path, String title) {
        ffmpegFail = false;

        ffmpegDone = true;
        if (isCancelSaveFile) return;

        if (progressDialog != null) {
            progressDialog.setProgress(100);
            progressDialog.dismiss();
        }

//        FileUtil.addFileToContentProvider(getContext(), path, title);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q)
            FileUtil.scanGallery(getContext(), path);
        else
            FileUtil.addFileToContentProvider(getContext(), path, title);

        Toast.makeText(getContext(), getString(R.string.create_file) + ": " + path, Toast.LENGTH_SHORT).show();

        afterFFmpegOnSuccess();
    }

    private void afterFFmpegOnSuccess() {
        if (isPauseFragment()) {
            return;
        }

        Utils.clearFragment(getFragmentManager());

        Context context = getContext();
        if (context != null) {
            context.sendBroadcast(new Intent(Statistic.OPEN_SPEED_STUDIO));
        }
    }


    @Override
    public void onCancelSelect() {
        if (dialogInputName != null) {
            dialogInputName.hideDialog();
        }

        initVideo();
    }

    @Override
    public void onFileNameEmpty() {
        Toast.makeText(getContext(), getString(R.string.error), Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onFileNameHasSpecialCharacter() {
        Toast.makeText(getContext(), getString(R.string.name_file_can_not_contain_character), Toast.LENGTH_SHORT).show();
    }


    @Override
    public void onProgressChanged(com.videomontage.freeeditingapps.custom.BubbleSeekBar bubbleSeekBar, int progress, float progressFloat, boolean fromUser) {
        tvShowSpeed.setText(listTempoAudio[progress - 1] + "x");
        tempoVideo = listTempoAudio[progress - 1];
        ptsVideo = listPtsVideo[progress - 1];
        if (player != null)
            player.setPlaybackParameters(new PlaybackParameters(tempoVideo));
        Flog.e("xxxxxx  changeeeeeeeeeeeeee");

//            mNiceVideoPlayer.setSpeed(listTempoAudio[progress - 1]);
    }

    @Override
    public void getProgressOnActionUp(com.videomontage.freeeditingapps.custom.BubbleSeekBar bubbleSeekBar, int progress, float progressFloat) {

    }

    @Override
    public void getProgressOnFinally(com.videomontage.freeeditingapps.custom.BubbleSeekBar bubbleSeekBar, int progress, float progressFloat, boolean fromUser) {

    }


}
