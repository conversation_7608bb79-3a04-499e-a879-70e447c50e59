//package com.videomontage.freeeditingapps.fragment;
//
//import android.os.Bundle;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.TextView;
//
//import com.videomontage.freeeditingapps.R;
//
//import androidx.fragment.app.Fragment;
//
//public class PageFragment extends Fragment {
//    public static final String ARG_PAGE = "ARG_PAGE";
//
//    private int mPage;
//
//
//
//    public static PageFragment newInstance(int page) {
//        Bundle args = new Bundle();
//        args.putInt(ARG_PAGE, page);
//        PageFragment fragment = new PageFragment();
//        fragment.setArguments(args);
//        return fragment;
//    }
//
//    @Override
//    public void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        mPage = getArguments().getInt(ARG_PAGE);
//    }
//
//    @Override
//    public View onCreateView(LayoutInflater inflater, ViewGroup container,
//                             Bundle savedInstanceState) {
//        View view = inflater.inflate(R.layout.sticker_fragment_layout, container, false);
//
//        TextView textView = view.findViewById(R.id.textView2);
//        textView.setText("Fragment #" + mPage);
//        return view;
//    }
//}