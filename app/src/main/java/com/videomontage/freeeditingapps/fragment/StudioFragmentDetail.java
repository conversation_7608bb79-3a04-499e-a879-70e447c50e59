package com.videomontage.freeeditingapps.fragment;

import android.app.Activity;
import android.app.RecoverableSecurityException;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.IntentSender;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.MediaStore;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import androidx.core.content.FileProvider;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.view.ActionMode;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.TextView;
import android.widget.Toast;

import com.videomontage.freeeditingapps.BuildConfig;
import com.videomontage.freeeditingapps.R;
import com.videomontage.freeeditingapps.activity.MainActivity;
import com.videomontage.freeeditingapps.adapter.VideoAdapter;
import com.videomontage.freeeditingapps.adapter.VideoStudioAdapter;
import com.videomontage.freeeditingapps.analytics.AmplitudeHelper;
import com.videomontage.freeeditingapps.analytics.FragmentAnalytics;
import com.videomontage.freeeditingapps.application.MyApplication;
import com.videomontage.freeeditingapps.listener.IInputNameFile;
import com.videomontage.freeeditingapps.model.VideoModel;
import com.videomontage.freeeditingapps.statistic.Statistic;
import com.videomontage.freeeditingapps.utils.SharedPrefs;
import com.videomontage.freeeditingapps.utils.SortOrder;
import com.videomontage.freeeditingapps.utils.Utils;


import java.io.File;
import java.util.ArrayList;
import java.util.List;

import timber.log.Timber;

import static com.videomontage.freeeditingapps.utils.FileUtil.deleteAudio;
import static com.videomontage.freeeditingapps.utils.FileUtil.renameContentProvider;
import static com.videomontage.freeeditingapps.utils.Utils.closeKeyboard;
import static com.videomontage.freeeditingapps.utils.Utils.getFileExtension;

/**
 * Created by Hung on 11/15/2018.
 */

public class StudioFragmentDetail extends AbsFragment implements VideoAdapter.ItemSelected, VideoStudioAdapter.ItemSelectedStudio, IInputNameFile {
    private VideoStudioAdapter videoAdapter;
    private List<VideoModel> videoModelList = new ArrayList<>(), listAllVideo = new ArrayList<>(), mListChecked = new ArrayList<>();
    private RecyclerView rvVideo;
    private TextView tvNoVideo;
    public boolean isActionMode = false, isSelectAll = false;
    public int countItemSelected = 0;
    private String checkCurrentFragment = null;
    private MainActivity mainActivity;
    private int indexOption = 0;
    private ActionMode actionMode = null;
    private BottomSheetDialog bottomSheetDialog;
    
    // For handling delete permission requests
    private static final int REQUEST_DELETE_PERMISSION = 1001;
    private VideoModel pendingDeleteVideo = null;

//    private TimeWatch watch;
    private FragmentAnalytics fragmentAnalytics;

    private BroadcastReceiver receiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent == null || intent.getAction() == null) {
                return;
            }

            switch (intent.getAction()) {

                case Statistic.CLEAR_ACTION_MODE:

                    if (actionMode == null) {
                        return;
                    }

                    actionMode.finish();
                    break;

                case Statistic.UPDATE_CHOOSE_SORT_ORDER:
                    mSortOrder = intent.getIntExtra(Statistic.SORT_ORDER_CURRENT, SortOrder.ID_SONG_DATE_ADDED_DESCENDING);
                    updateList();
                    break;
            }

        }
    };

    public void beginSearch(String s) {
        videoModelList = Utils.filterVideoModel(listAllVideo, s);
        videoAdapter.setFilter(videoModelList);
    }


    public static StudioFragmentDetail newInstance(Bundle bundle) {
        StudioFragmentDetail fragment = new StudioFragmentDetail();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        this.mainActivity = (MainActivity) context;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_studio_detail, container, false);
    }

    public void createAction() {
        mainActivity.startSupportActionMode(new ActionMode.Callback() {
            @Override
            public boolean onCreateActionMode(ActionMode mode, Menu menu) {
                isActionMode = true;
                for (VideoModel audioEntity : videoModelList) {
                    audioEntity.setCheck(false);
                }
                videoAdapter.notifyDataSetChanged();
                actionMode = mode;
                actionMode.setTitle("0");
                mainActivity.getMenuInflater().inflate(R.menu.setting_menu, menu);
                return true;
            }

            @Override
            public boolean onPrepareActionMode(ActionMode mode, Menu menu) {
                return false;
            }

            @Override
            public boolean onActionItemClicked(ActionMode mode, MenuItem item) {
                switch (item.getItemId()) {
                    case R.id.item_delete:
                        actionModeDelete(mode);
                        break;
                    case R.id.item_check_all:
                        selectItem();
                        break;
                }
                return true;
            }

            @Override
            public void onDestroyActionMode(ActionMode mode) {
                isActionMode = false;
                isSelectAll = false;
                countItemSelected = 0;
                if (mListChecked != null) {
                    mListChecked.clear();
                }
                for (VideoModel videoModel : videoModelList) {
                    videoModel.setCheck(false);
                }
                videoAdapter.notifyDataSetChanged();
                actionMode.finish();
                actionMode = null;
            }
        });
    }

    private void selectItem() {
        if (!isSelectAll) {
            countItemSelected = videoModelList.size();
            isSelectAll = true;
            for (VideoModel videoModel : videoModelList) {
                videoModel.setCheck(true);
            }
            videoAdapter.notifyDataSetChanged();
            actionMode.setTitle(countItemSelected + "");
            mListChecked.clear();
            mListChecked.addAll(videoModelList);
        } else {
            isSelectAll = false;
            for (VideoModel videoModel : videoModelList) {
                videoModel.setCheck(false);
            }
            mListChecked.clear();
            countItemSelected = 0;
            actionMode.setTitle(countItemSelected + " ");
            actionMode.finish();
        }
    }

    private void settingDeleteRecord() {

        if (mListChecked.size() != 0) {
            for (VideoModel videoModel : mListChecked) {
                countItemSelected = countItemSelected - 1;
                new File(videoModel.getPath()).delete();
                deleteAudio(getContext(), videoModel.getPath());
            }

            updateList();

            updateCountItemSelected();

            notifiAdapter();

            getContext().sendBroadcast(new Intent(Statistic.UPDATE_DELETE_RECORD));
        }
    }

    private void notifiAdapter() {
        videoAdapter.notifyDataSetChanged();
    }

    private void updateList() {
        listAllVideo.clear();
        listAllVideo.addAll(Utils.getStudioVideos(getContext(), checkCurrentFragment, mSortOrder));
        videoModelList.clear();
        videoModelList.addAll(listAllVideo);

        notifiAdapter();
        hideBottomSheetDialog();
        closeKeyboard(getActivity());
        checkStateFile();
    }

    private void checkStateFile() {
        if (tvNoVideo == null) {
            return; // Safety check
        }
        
        if (videoModelList.size() > 0) {
            tvNoVideo.setVisibility(View.GONE);
            rvVideo.setVisibility(View.VISIBLE);
        } else {
            tvNoVideo.setVisibility(View.VISIBLE);
            rvVideo.setVisibility(View.GONE);
        }
    }


    private void actionModeDelete(final ActionMode mode) {
        if (mListChecked.size() != 0) {
            AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
            builder.setTitle(getResources().getString(R.string.delete_this_record));
            builder.setNegativeButton(android.R.string.no, (dialog, id) -> dialog.dismiss());
            builder.setPositiveButton(android.R.string.yes, (dialog, id) -> {
                settingDeleteRecord();
                isActionMode = false;
                isSelectAll = false;
                videoAdapter.notifyDataSetChanged();
                mode.finish();
            });

            AlertDialog alertDialog = builder.create();
            alertDialog.show();

        }
    }


    public void updateCountItemSelected() {
        if (countItemSelected == 0) {
            actionMode.setTitle("0");
            return;
        }

        actionMode.setTitle(countItemSelected + "");
    }

    public void prepareSelection(View view, int i) {
        if (((CheckBox) view).isChecked()) {
            if (!mListChecked.contains(videoModelList.get(i))) {
                mListChecked.add(videoModelList.get(i));
                countItemSelected = countItemSelected + 1;
                updateCountItemSelected();
            }
        } else {
            if (mListChecked.contains(videoModelList.get(i))) {
                mListChecked.remove(videoModelList.get(i));
                countItemSelected = countItemSelected - 1;
                updateCountItemSelected();
            }
        }
    }

    private int mSortOrder = 3;

    @Override
    public void initViews() {

        fragmentAnalytics = new FragmentAnalytics("StudioFragmentDetail");


        mSortOrder = SharedPrefs.getInstance().get(Statistic.SORT_ORDER_CURRENT, Integer.class, 3);

        checkCurrentFragment = getArguments().getString(Statistic.CHECK_STUDIO_FRAGMENT, null);
        if (checkCurrentFragment == null) {
            return;
        }

        listAllVideo.clear();
        listAllVideo.addAll(Utils.getStudioVideos(getContext(), checkCurrentFragment, mSortOrder));

        videoModelList.clear();
        videoModelList.addAll(listAllVideo);

        videoAdapter = new VideoStudioAdapter(videoModelList, this, this, true);

        tvNoVideo = (TextView) findViewById(R.id.tv_no_video);
        rvVideo = (RecyclerView) findViewById(R.id.recycle_view);
        rvVideo.setHasFixedSize(true);
        rvVideo.setLayoutManager(new LinearLayoutManager(getContext()));
        rvVideo.setAdapter(videoAdapter);

        checkStateFile();
        initActions();
    }

    private void initActions() {
        IntentFilter it = new IntentFilter();
        it.addAction(Statistic.CLEAR_ACTION_MODE);
        it.addAction(Statistic.UPDATE_CHOOSE_SORT_ORDER);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            getContext().registerReceiver(receiver, it, Context.RECEIVER_NOT_EXPORTED);
        } else {
            getContext().registerReceiver(receiver, it);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        getContext().unregisterReceiver(receiver);

        if (!fragmentAnalytics.isClicksEmpty())
            MyApplication.addEvent(fragmentAnalytics);
    }

    private void isHasVideo() {
        if (videoModelList.size() == 0) {
            tvNoVideo.setVisibility(View.VISIBLE);
            return;
        }

        tvNoVideo.setVisibility(View.GONE);
    }

    @Override
    public void onClick(int index) {
        indexOption = index;
        openVideoWith();
    }

    @Override
    public boolean onLongClick(int index) {
        createAction();
        videoModelList.get(index).setCheck(true);
        videoAdapter.notifyDataSetChanged();
        return true;
    }

    @Override
    public void onOptionClick(int index) {
        indexOption = index;
        showBottomSheet(indexOption);
    }

    private void showBottomSheet(int index) {

        View view = LayoutInflater.from(getContext()).inflate(R.layout.dialog_option_bottom, null);

        TextView tvTitle = view.findViewById(R.id.btn_title);
        VideoModel videoModel = videoModelList.get(index);

        tvTitle.setText(videoModel.getNameAudio());

        view.findViewById(R.id.btn_share).setOnClickListener(v -> shareVideo());
        view.findViewById(R.id.btn_detail).setOnClickListener(v -> detailVideo());
        view.findViewById(R.id.btn_rename).setOnClickListener(v -> renameVideo());
        view.findViewById(R.id.btn_open_file).setOnClickListener(v -> openVideoWith());

        // Only show Delete button for videos created by our app
        View btnDelete = view.findViewById(R.id.btn_delete);
        boolean shouldShowDelete = isVideoCreatedByApp(videoModel);
        
        if (shouldShowDelete) {
            btnDelete.setVisibility(View.VISIBLE);
            btnDelete.setOnClickListener(v -> deleteVideo());
        } else {
            btnDelete.setVisibility(View.GONE);
        }

        bottomSheetDialog = new BottomSheetDialog(getContext());
        bottomSheetDialog.setContentView(view);
        bottomSheetDialog.show();
    }

//    private void playVideo() {
//
//        Bundle bundle = new Bundle();
//
//        bundle.putParcelable(Statistic.VIDEO_MODEL, videoModelList.get(indexOption));
//
//        getActivity().getSupportFragmentManager().beginTransaction()
//                .setCustomAnimations(R.anim.animation_left_to_right
//                        , R.anim.animation_right_to_left
//                        , R.anim.animation_left_to_right
//                        , R.anim.animation_right_to_left)
//                .add(R.id.view_container1, PlayVideoFragment.newInstance(bundle))
//                .addToBackStack(null)
//                .commit();
//    }

    private void openVideoWith() {
        fragmentAnalytics.addClick("Open video with");
        AmplitudeHelper.setOpenVideoWith();

        Uri uri;

        VideoModel videoModel = videoModelList.get(indexOption);

        Intent intent = new Intent();

        intent.setAction(Intent.ACTION_VIEW);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            uri = FileProvider.getUriForFile(getContext(), BuildConfig.APPLICATION_ID + ".provider", new File(videoModel.getPath()));
        } else {
            uri = Uri.fromFile(new File(videoModel.getPath()));
        }

        intent.setDataAndType((uri), "video/*");

        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);

        startActivity(intent);

        hideBottomSheetDialog();
    }

    private void hideBottomSheetDialog() {
        if (bottomSheetDialog != null) {
            bottomSheetDialog.dismiss();
        }
    }

    private DialogInputName dialogInputName;

    private void renameVideo() {
        fragmentAnalytics.addClick("Rename video file");
        AmplitudeHelper.setRenameVideoFile();

        dialogInputName = new DialogInputName(getContext(), this, "", getString(R.string.rename));
        dialogInputName.initDialog();
        hideBottomSheetDialog();
    }

    private void detailVideo() {
        fragmentAnalytics.addClick("Detail Video");
        AmplitudeHelper.setDetailVideo();

        VideoModel videoModel = videoModelList.get(indexOption);
        TextView tvTitle, tvFilePath, tvDuration, tvSize, tvDateAdded;
        DialogDetail dialogDetail = new DialogDetail(getContext());
        dialogDetail.setOnClickBtnOk(v -> dialogDetail.hideDialog());

        View view = dialogDetail.getView();
        tvSize = view.findViewById(R.id.tvSize);
        tvTitle = view.findViewById(R.id.tvTitle);
        tvFilePath = view.findViewById(R.id.tvFilePath);
        tvDuration = view.findViewById(R.id.tvDuaration);
        tvDateAdded = view.findViewById(R.id.tvDateTime);
        tvTitle.setText(getResources().getString(R.string.title_audio) + ": " + videoModel.getNameAudio());
        tvSize.setText(getString(R.string.size) + ": " + Utils.getStringSizeLengthFile(videoModel.getSize()));
        tvFilePath.setText(getResources().getString(R.string.path) + ": " + videoModel.getPath());
        tvDuration.setText(getResources().getString(R.string.duration) + ": " + Utils.convertMillisecond(Long.parseLong(videoModel.getDuration())));
        tvDateAdded.setText(getString(R.string.date_time) + ": " + Utils.convertDate(String.valueOf(videoModel.getDateModifier()), "dd/MM/yyyy HH:mm:ss"));
        hideBottomSheetDialog();

    }

    private void deleteVideo() {
        VideoModel videoModel = videoModelList.get(indexOption);
        
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle(getResources().getString(R.string.delete_this_record));
        builder.setNegativeButton(android.R.string.no, (dialog, id) -> dialog.dismiss());
        builder.setPositiveButton(getResources().getString(R.string.yes), (dialog, id) -> {
            fragmentAnalytics.addClick("Delete Video");
            AmplitudeHelper.setDeleteVideo();

            boolean deleteResult = tryForceDelete(videoModel);
            
            if (!deleteResult) {
                Toast.makeText(getContext(), "Failed to delete video file", Toast.LENGTH_SHORT).show();
            }
            
            deleteAudio(getContext(), videoModel.getPath());
            updateList();
        });
        AlertDialog alertDialog = builder.create();
        alertDialog.show();
        hideBottomSheetDialog();
    }

    private void shareVideo() {

        fragmentAnalytics.addClick("Share");
        AmplitudeHelper.setShareVideo();


        Uri uri;
        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_SEND);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            uri = FileProvider.getUriForFile(getContext(), BuildConfig.APPLICATION_ID + ".provider", new File(videoModelList.get(indexOption).getPath()));
        } else {
            uri = Uri.fromFile(new File(videoModelList.get(indexOption).getPath()));
        }
        intent.setType("video/*");
        intent.putExtra(Intent.EXTRA_STREAM, uri);
        startActivity(Intent.createChooser(intent, getString(R.string.share_file)));
        hideBottomSheetDialog();
    }

    @Override
    public void onApplySelect(String nameFile) {
        applyRenameVideo(nameFile);
    }

    private void applyRenameVideo(String nameFile) {
        File currentFile, newFile;

        VideoModel videoModel = videoModelList.get(indexOption);

        currentFile = new File(videoModel.getPath());

        newFile = new File(videoModel.getPath().replace(videoModel.getNameAudio() + getFileExtension(videoModel.getPath()), "") + nameFile + getFileExtension(videoModel.getPath()));

        if (newFile.exists()) {
            Toast.makeText(getContext(), getString(R.string.name_file_exist), Toast.LENGTH_SHORT).show();

        } else {
            rename(currentFile, newFile);
            renameContentProvider(nameFile, getFileExtension(videoModel.getPath()), videoModel, getContext());
            updateList();
            onHideDialogInputNameFile();
        }
    }

    private void onHideDialogInputNameFile() {
        if (dialogInputName != null) {
            dialogInputName.hideDialog();
        }
    }

    private boolean rename(File from, File to) {
        return from.getParentFile().exists() && from.exists() && from.renameTo(to);
    }

    /**
     * Try to delete video file with proper RecoverableSecurityException handling
     */
    private boolean tryForceDelete(VideoModel videoModel) {
        String filePath = videoModel.getPath();
        Timber.tag("DEBUG_FLOW").d("StudioFragmentDetail: tryForceDelete - attempting to delete: " + filePath);
        
        // Find the video in MediaStore first
        Uri videoUri = findVideoInMediaStore(filePath);
        if (videoUri == null) {
            Timber.tag("DEBUG_FLOW").w("StudioFragmentDetail: Video not found in MediaStore: " + filePath);
            return false;
        }
        
        try {
            // Try to delete via ContentResolver
            int deletedRows = getContext().getContentResolver().delete(videoUri, null, null);
            Timber.tag("DEBUG_FLOW").d("StudioFragmentDetail: Successfully deleted rows: " + deletedRows);
            return deletedRows > 0;
            
        } catch (RecoverableSecurityException e) {
            // This is the key - handle RecoverableSecurityException properly!
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                Timber.tag("DEBUG_FLOW").d("StudioFragmentDetail: RecoverableSecurityException - requesting user permission");
                
                // Store the video for retry after permission is granted
                pendingDeleteVideo = videoModel;
                
                try {
                    // Request permission from user via system dialog  
                    IntentSender intentSender = e.getUserAction().getActionIntent().getIntentSender();
                    startIntentSenderForResult(intentSender, REQUEST_DELETE_PERMISSION, null, 0, 0, 0, null);
                    
                    // System will show permission dialog
                    return false; // Will be completed in onActivityResult
                    
                } catch (Exception intentException) {
                    Timber.tag("DEBUG_FLOW").e(intentException, "StudioFragmentDetail: Failed to start permission intent");
                    return false;
                }
            } else {
                Timber.tag("DEBUG_FLOW").e(e, "StudioFragmentDetail: SecurityException on older Android version");
                return false;
            }
        } catch (Exception e) {
            Timber.tag("DEBUG_FLOW").e(e, "StudioFragmentDetail: Delete failed with exception");
            return false;
        }
    }
    
    /**
     * Find video URI in MediaStore by file path
     */
    private Uri findVideoInMediaStore(String filePath) {
        try {
            Uri contentUri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI;
            String[] projection = {MediaStore.Video.Media._ID};
            String selection = MediaStore.Video.Media.DATA + "=?";
            String[] selectionArgs = {filePath};
            
            android.database.Cursor cursor = getContext().getContentResolver().query(
                contentUri, projection, selection, selectionArgs, null);
            
            if (cursor != null && cursor.moveToFirst()) {
                long id = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Video.Media._ID));
                cursor.close();
                
                Uri videoUri = Uri.withAppendedPath(contentUri, String.valueOf(id));
                Timber.tag("DEBUG_FLOW").d("StudioFragmentDetail: Found video URI: " + videoUri);
                return videoUri;
            } else {
                if (cursor != null) cursor.close();
                Timber.tag("DEBUG_FLOW").w("StudioFragmentDetail: Video not found in MediaStore");
                return null;
            }
        } catch (Exception e) {
            Timber.tag("DEBUG_FLOW").e(e, "StudioFragmentDetail: Error finding video in MediaStore");
            return null;
        }
    }
    

    /**
     * Check if a video was created by our application.
     * Our app creates videos in specific directories under DCIM/Montage/
     * @param videoModel The video to check
     * @return true if the video was created by our app, false otherwise
     */
    private boolean isVideoCreatedByApp(VideoModel videoModel) {
        if (videoModel == null || videoModel.getPath() == null) {
            Timber.tag("DEBUG_FLOW").d("StudioFragmentDetail: isVideoCreatedByApp - videoModel or path is null");
            return false;
        }

        String videoPath = videoModel.getPath();
        Timber.tag("DEBUG_FLOW").d("StudioFragmentDetail: Checking video path: " + videoPath);
        
        // Log the directories we're checking against
        Timber.tag("DEBUG_FLOW").d("StudioFragmentDetail: Checking against directories:");
        Timber.tag("DEBUG_FLOW").d("  - Cutter: /Montage" + Statistic.DIR_CUTTER + "/");
        Timber.tag("DEBUG_FLOW").d("  - Speed: /Montage" + Statistic.DIR_SPEED + "/");
        Timber.tag("DEBUG_FLOW").d("  - Merger: /Montage" + Statistic.DIR_MERGER + "/");
        Timber.tag("DEBUG_FLOW").d("  - AddMusic: /Montage" + Statistic.DIR_ADD_MUSIC + "/");
        Timber.tag("DEBUG_FLOW").d("  - Effects: /Montage" + Statistic.DIR_EFFECTS + "/");
        
        // Check if video is in any of our app's directories
        boolean isAppCreated = videoPath.contains("/Montage" + Statistic.DIR_CUTTER + "/") ||
               videoPath.contains("/Montage" + Statistic.DIR_SPEED + "/") ||
               videoPath.contains("/Montage" + Statistic.DIR_MERGER + "/") ||
               videoPath.contains("/Montage" + Statistic.DIR_ADD_MUSIC + "/") ||
               videoPath.contains("/Montage" + Statistic.DIR_EFFECTS + "/");
        
        Timber.tag("DEBUG_FLOW").d("StudioFragmentDetail: isVideoCreatedByApp result: " + isAppCreated);
        return isAppCreated;
    }

    @Override
    public void onCancelSelect() {
        onHideDialogInputNameFile();
    }

    @Override
    public void onFileNameEmpty() {
        Toast.makeText(getContext(), getString(R.string.name_file_can_not_empty), Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onFileNameHasSpecialCharacter() {
        Toast.makeText(getContext(), getString(R.string.name_file_can_not_contain_character), Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == REQUEST_DELETE_PERMISSION) {
            if (resultCode == Activity.RESULT_OK && pendingDeleteVideo != null) {
                Timber.tag("DEBUG_FLOW").d("StudioFragmentDetail: Delete permission granted, retrying deletion");
                
                // Permission granted, retry the deletion
                Uri videoUri = findVideoInMediaStore(pendingDeleteVideo.getPath());
                if (videoUri != null) {
                    try {
                        int deletedRows = getContext().getContentResolver().delete(videoUri, null, null);
                        
                        if (deletedRows > 0) {
                            updateList(); // Refresh the list
                        }
                    } catch (Exception e) {
                        // Silent failure - user already saw system dialog
                    }
                }
            }
            
            // Clear pending delete
            pendingDeleteVideo = null;
        }
    }
}