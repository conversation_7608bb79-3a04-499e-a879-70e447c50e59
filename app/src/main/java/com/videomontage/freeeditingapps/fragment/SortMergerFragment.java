package com.videomontage.freeeditingapps.fragment;

import android.app.ProgressDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.media.MediaMetadataRetriever;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.ItemTouchHelper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.Toast;

import com.arthenica.mobileffmpeg.Config;
import com.arthenica.mobileffmpeg.ExecuteCallback;
import com.arthenica.mobileffmpeg.LogCallback;
import com.arthenica.mobileffmpeg.LogMessage;
//import com.github.hiteshsondhi88.libffmpeg.ExecuteBinaryResponseHandler;
//import com.github.hiteshsondhi88.libffmpeg.FFmpeg;
//import com.github.hiteshsondhi88.libffmpeg.exceptions.FFmpegCommandAlreadyRunningException;
import com.videomontage.freeeditingapps.R;
import com.videomontage.freeeditingapps.adapter.SimpleItemTouchHelperCallback;
import com.videomontage.freeeditingapps.adapter.SortAdapter;
import com.videomontage.freeeditingapps.analytics.AmplitudeHelper;
import com.videomontage.freeeditingapps.analytics.FragmentAnalytics;
import com.videomontage.freeeditingapps.analytics.TimeWatch;
import com.videomontage.freeeditingapps.application.MyApplication;
import com.videomontage.freeeditingapps.listener.IInputNameFile;
import com.videomontage.freeeditingapps.listener.IListSongChanged;
import com.videomontage.freeeditingapps.model.ComModel;
import com.videomontage.freeeditingapps.model.VideoModel;
import com.videomontage.freeeditingapps.statistic.Statistic;
import com.videomontage.freeeditingapps.utils.FileUtil;
import com.videomontage.freeeditingapps.utils.Flog;
import com.videomontage.freeeditingapps.utils.Keys;
import com.videomontage.freeeditingapps.utils.Utils;
import com.videomontage.freeeditingapps.utils.VideoUtil;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

import static com.arthenica.mobileffmpeg.Config.RETURN_CODE_CANCEL;
import static com.arthenica.mobileffmpeg.Config.RETURN_CODE_SUCCESS;
import static com.videomontage.freeeditingapps.statistic.Statistic.EXTENSION_MP4;


public class SortMergerFragment extends AbsFragment implements IListSongChanged, SortAdapter.OnStartDragListener, IInputNameFile {
    private List<VideoModel> videoModelList = new ArrayList<>();
    private RecyclerView rvAudio;
    private SortAdapter audioAdapter;
    private Toolbar toolbar;
    private ItemTouchHelper.Callback callback;
    private ItemTouchHelper itemTouchHelper;
//    private FFmpeg ffmpeg;

    private AlertDialog.Builder builder;
    private AlertDialog alertDialog;
    private EditText edtNameFile;
    private long duration;
    private ProgressDialog progressDialog;
    private SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.US);
    private String path;
    private boolean isCancelSaveFile = false;
    private boolean isSuccess = false;
    private boolean ffmpegDone;
    private int width = 1920;
    private int height = 1080;
    private boolean containAudio = true;
    private TimeWatch watch;
    private FragmentAnalytics fragmentAnalytics;


    public static SortMergerFragment newInstance(Bundle bundle) {
        SortMergerFragment fragment = new SortMergerFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public void onDestroy() {
        Utils.closeKeyboard(getActivity());

        fragmentAnalytics.setDuration(watch.time(TimeUnit.SECONDS));
        MyApplication.addEvent(fragmentAnalytics);
        super.onDestroy();
    }

    @Override
    public void initViews() {
        watch = TimeWatch.start();
        fragmentAnalytics = new FragmentAnalytics("SortMergerFragment");
        AmplitudeHelper.setMergerOpened();

//        ffmpeg = FFmpeg.getInstance(getContext());

        duration = getArguments().getLong(Statistic.DURATION);
        videoModelList.clear();
        videoModelList.addAll(getArguments().getParcelableArrayList(Statistic.LIST_VIDEO));
        audioAdapter = new SortAdapter(videoModelList, getContext(), this, this);

        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(getContext());

        rvAudio = (RecyclerView) findViewById(R.id.rv_audio);
        rvAudio.setHasFixedSize(true);
        rvAudio.setLayoutManager(linearLayoutManager);
        rvAudio.setAdapter(audioAdapter);

        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(rvAudio.getContext(),
                linearLayoutManager.getOrientation());

        rvAudio.addItemDecoration(dividerItemDecoration);

        callback = new SimpleItemTouchHelperCallback(audioAdapter);

        itemTouchHelper = new ItemTouchHelper(callback);
        itemTouchHelper.attachToRecyclerView(rvAudio);

        initToolbar();
    }


    public void initToolbar() {
        super.initToolbar();
        getToolbar().setTitle(R.string.sort);
        getToolbar().getMenu().clear();
        getToolbar().inflateMenu(R.menu.menu_merger);
        getToolbar().getMenu().findItem(R.id.item_ads).setOnMenuItemClickListener(item -> dialogSelectLocalSaveFile());
    }

    public static void appendVideoLog(String text) {
        if (!Keys.TEMP_DIRECTORY.exists()) {
            Keys.TEMP_DIRECTORY.mkdirs();
        }
        File logFile = new File(Keys.TEMP_DIRECTORY, "video.txt");
//        Log.d("FFMPEG", "File append " + text);
        if (!logFile.exists()) {
            try {
                logFile.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        try {
            BufferedWriter buf = new BufferedWriter(new FileWriter(logFile, true));
            buf.append(text);
            buf.newLine();
            buf.close();
        } catch (IOException e2) {
            e2.printStackTrace();
        }
    }


    private void initDialogProgress() {
        progressDialog = new ProgressDialog(getContext());
        progressDialog.setCancelable(false);
        progressDialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
        progressDialog.setTitle(getString(R.string.progress_dialog_saving));
        progressDialog.setProgress(0);
        progressDialog.setButton(DialogInterface.BUTTON_NEGATIVE, getString(R.string.cancel), (dialog, which) -> cancelMerger());
        progressDialog.show();
    }

    private void cancelMerger() {
        isCancelSaveFile = true;
//        ffmpeg.killRunningProcesses();
        new File(path).delete();
    }

    public static final String[] listSpecialCharacter = new String[]{"%", "/", "#", "^", ":", "?", ","};

    private boolean mergerAudio(String nameFile) {

        isCancelSaveFile = false;

        boolean isAbleMerger = true;

        for (VideoModel videoModel : videoModelList) {
//            Flog.e("exxxxxxxxxxxxxxx   " + Utils.getFileExtension(videoModel.getPath()));
            if (!Utils.getFileExtension(videoModel.getPath()).equals(EXTENSION_MP4)) {
                isAbleMerger = false;
            }
        }

        if (isAbleMerger) {

            if (!nameFile.isEmpty()) {

//                path = Environment.getExternalStorageDirectory().getAbsolutePath() + Statistic.DIR_APP + Statistic.DIR_MERGER + "/";
                path = Environment.getExternalStoragePublicDirectory(
                        Environment.DIRECTORY_DCIM).toString()
                        + File.separator + "Montage"
                        + Statistic.DIR_MERGER + "/";
                File f = new File(path);
                if (!f.exists()) {
                    f.mkdirs();
                }

                path = path + nameFile + EXTENSION_MP4;

                if (new File(path).exists()) {
                    Toast.makeText(getContext(), getString(R.string.file), Toast.LENGTH_SHORT).show();

                } else {

                    isCancelSaveFile = false;

                    initDialogProgress();

//                    showInterstitial();

                    // delete last list path file
                    new File(Keys.TEMP_DIRECTORY, "video.txt").delete();

                    // add list path file to file txt
                    for (VideoModel videoModel : videoModelList) {
                        appendVideoLog(String.format("file '%s'", videoModel.getPath()));
                    }

                    File listFile = new File(Keys.TEMP_DIRECTORY, "video.txt");

//                    String command[] = new String[]{"-f", "concat", "-safe", "0", "-i", listFile.getAbsolutePath(), "-c", "copy", path};

                    execFFmpegBinary(getMergeCommand(path), path, nameFile);

//                    Log.d("PROVERA", "mergerAudio: " + Arrays.toString(commandTest));
//                    execFFmpegBinary(commandTest, path, nameFile);

                }
            } else {
                Toast.makeText(getContext(), getString(R.string.name_file_can_not_empty), Toast.LENGTH_SHORT).show();
            }

        } else {
            Toast.makeText(getContext(), getString(R.string.support_mp4), Toast.LENGTH_SHORT).show();
            getFragmentManager().popBackStack();
        }

        if (alertDialog != null) {
            alertDialog.dismiss();
        }

        return true;

    }

    private Object getMergeCommand(String outputPath) {
        MediaMetadataRetriever retriever = new MediaMetadataRetriever();

        try {
            retriever.setDataSource(videoModelList.get(0).getPath());
            int widthInit = Integer.valueOf(retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH));
            int heightInit = Integer.valueOf(retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT));
            String s = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION);

//        Log.e("Rotation", s);

            if (s.equalsIgnoreCase("90")) {
                width = heightInit;
                height = widthInit;
            } else {
                width = widthInit;
                height = heightInit;
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                retriever.release();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }

        }


        StringBuilder stringBuilder = new StringBuilder();
        StringBuilder filterComplex = new StringBuilder();
        StringBuilder concatComplex = new StringBuilder();
        for (int i = 0; i < videoModelList.size(); i++) {
            stringBuilder.append("-i" + "," + (android.os.Build.VERSION.SDK_INT > Build.VERSION_CODES.P ? ComModel.getCharEscaptedPath(videoModelList.get(i).getPath()) : videoModelList.get(i).getPath()) + ",");
            if (i == videoModelList.size() - 1)
                stringBuilder.append("-f,lavfi,-t,0.1,-i,anullsrc,-strict,experimental," + "-filter_complex,");
            filterComplex.append("[").append(i).append(":v").append("]");
            filterComplex.append("scale=iw*min(" + width + "/iw\\," + height + "/ih):ih*min(" + width + "/iw\\," + height + "/ih),pad=" + width + ":" + height + ":(" + width + "-iw*min(" + width + "/iw\\," + height + "/ih))/2:(" + height + "-ih*min(" + width + "/iw\\," + height + "/ih))/2,setsar=1:1");
            filterComplex.append("[v").append(i).append("];");
            concatComplex.append("[v" + i + "]");

            if (!VideoUtil.isVideoHaveAudioTrack(videoModelList.get(i).getPath())) {
                concatComplex.append("[" + videoModelList.size() + ":a]");

            } else {
                concatComplex.append("[" + i + ":a]");
            }
        }
        concatComplex.append("concat=n=").append(videoModelList.size()).append(":v=1:a=1");
        String[] inputCommand = stringBuilder.toString().split(",");
        String[] filterCommand = new String[]{filterComplex.toString() + concatComplex.toString()};
        String[] destinationCommand = {"-ab", "48000", "-ac", "2", "-ar", "22050", "-s", width + "x" + height, "-vcodec", "libx264", "-crf", "27", "-q", "4", "-preset", "ultrafast", outputPath};

        String commandNewApi =
                stringBuilder.toString().replace(",", " ") + " "
                        + filterComplex.toString() + concatComplex.toString()
                        + " -ab 48000 -ac 2 -ar 22050 -s " + width + "x" + height + " -vcodec libx264 -crf 27 -q 4 -preset ultrafast  -vsync 2 " + outputPath;

        return ComModel.isNewApiRequired() ? commandNewApi : ComModel.combine(inputCommand, filterCommand, destinationCommand);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_sort, container, false);
    }


    @Override
    public void onStartDrag(RecyclerView.ViewHolder viewHolder) {
        itemTouchHelper.startDrag(viewHolder);
    }

    private void createDialog(View view) {
        builder = new AlertDialog.Builder(getContext());
        builder.setView(view);
        alertDialog = builder.create();
        alertDialog.show();
    }


    private DialogInputName dialogInputName;

    private boolean dialogSelectLocalSaveFile() {
//        for (int i = 0; i < videoModelList.size(); i++) {
//            containAudio = VideoUtil.isVideoHaveAudioTrack(videoModelList.get(i).getPath());
//            if (!containAudio){
//                Snackbar.make(getView(), "Some of videos missing audio. You should fix it!", Snackbar.LENGTH_LONG)
//                        .setAction("CLOSE", new View.OnClickListener() {
//                            @Override
//                            public void onClick(View view) {
//
//                            }
//                        })
//                        .setActionTextColor(getResources().getColor(R.color.colorAccent ))
//                        .show();
//
//                break;
//            }
//        }

        if (!containAudio) {
            containAudio = true;
            return false;
        }

        if (videoModelList.size() >= 2) {
            String nameDefault = "VM_" + simpleDateFormat.format(System.currentTimeMillis());
            dialogInputName = new DialogInputName(getContext(), this, nameDefault, getString(R.string.save));
            dialogInputName.initDialog();

            fragmentAnalytics.addClick("Save");
            AmplitudeHelper.setSave();


        } else {
            AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
            builder.setTitle(getResources().getString(R.string.error));
            builder.setMessage(getString(R.string.you_need_to_have));
            builder.setPositiveButton(getResources().getString(R.string.lib_crs_yes), (dialog, id) -> {
                dialog.dismiss();
                getFragmentManager().popBackStack();
            });
            builder.setNegativeButton(getResources().getString(R.string.lib_crs_no), (dialog, id) -> dialog.dismiss());
            AlertDialog dialog = builder.create();
            dialog.show();
        }

        return true;
    }

    private boolean isError = false;


    @Override
    public void afterInterstitial() {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (ffmpegDone)
                    afterFFmpegOnSuccess();

                ffmpegDone = false;


            }
        }, 1000);

    }

    private void execFFmpegBinary(final Object command, String path, String title) {
//        Flog.e("xxx", "cccccccccccccc");

        if (ComModel.isNewApiRequired()) {
            String myCommand = (String) command;
            ffmpegDone = false;

            long executionId = com.arthenica.mobileffmpeg.FFmpeg.executeAsync(myCommand, new ExecuteCallback() {

                @Override
                public void apply(final long executionId, final int rc) {
                    if (rc == RETURN_CODE_SUCCESS) {
                        Log.i(Config.TAG, "Command execution completed successfully.");
                        afterFFmpegSuccess(path, title);

                    } else if (rc == RETURN_CODE_CANCEL) {
                        Log.i(Config.TAG, "Command execution cancelled by user.");
                        afterFFmpegFailure(path);

                    } else {
                        Log.i(Config.TAG, String.format("Command execution failed with rc=%d and the output below.", rc));
                        Config.printLastCommandOutput(Log.INFO);
                        afterFFmpegFailure(path);


                    }
                }
            });

            Config.enableLogCallback(new LogCallback() {
                public void apply(LogMessage message) {
//                    Log.d(Config.TAG, message.getText());
                    Flog.d("aaaa", "apply: " + message.getText());

                    try {
                        int durationFile = (int) Utils.getProgress(message.getText(), duration / 1000);

                        float percent = durationFile / (float) (duration / 1000);

                        if (progressDialog != null) {
                            if (percent * 100 > 0) {
                                progressDialog.setProgress((int) (percent * 100));
                            }
                        }
                    } catch (ArithmeticException e) {
                        e.printStackTrace();
                    }
                }
            });

        }
//        else {
//            try {
//                ffmpeg.execute((String[]) command, new ExecuteBinaryResponseHandler() {
//                    @Override
//                    public void onFailure(String s) {
////                    Flog.e("xxx", "FAILED with output: " + s);
//
//                        afterFFmpegFailure(path);
//                    }
//
//                    @Override
//                    public void onSuccess(String s) {
//
//                        afterFFmpegSuccess(path, title);
//
//                    }
//
//                    @Override
//                    public void onProgress(String s) {
////                    Flog.e(s);
//
//                        try {
//                            int durationFile = (int) Utils.getProgress(s, duration / 1000);
//
//                            float percent = durationFile / (float) (duration / 1000);
//
//                            if (progressDialog != null) {
//                                if (percent * 100 > 0) {
//                                    progressDialog.setProgress((int) (percent * 100));
//                                }
//                            }
//                        } catch (ArithmeticException e) {
//                            e.printStackTrace();
//                        }
//                    }
//
//                    @Override
//                    public void onStart() {
//
//                    }
//
//                    @Override
//                    public void onFinish() {
//
//                    }
//                });
//            } catch (FFmpegCommandAlreadyRunningException e) {
//                e.printStackTrace();
//            }
//        }

    }

    private void afterFFmpegSuccess(String path, String title) {
        ffmpegDone = true;
//                    Flog.e("Success " + s);

        if (isCancelSaveFile) return;

//        FileUtil.addFileToContentProvider(getContext(), path, title);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q)
            FileUtil.scanGallery(getContext(), path);
        else
            FileUtil.addFileToContentProvider(getContext(), path, title);

        Toast.makeText(getContext(), getString(R.string.create_file) + path, Toast.LENGTH_SHORT).show();

        if (progressDialog != null) {
            progressDialog.setProgress(100);
            progressDialog.dismiss();
        }

        afterFFmpegOnSuccess();
    }

    private void afterFFmpegFailure(String path) {
        new File(path).delete();

        Toast.makeText(getContext(), getString(R.string.error), Toast.LENGTH_SHORT).show();

        if (getFragmentManager() != null) {
            getFragmentManager().popBackStack();
        }

        if (progressDialog != null) {
            progressDialog.dismiss();
        }
    }

    private void afterFFmpegOnSuccess() {
        if (isPauseFragment()) return;

        if (getFragmentManager() == null) return;

        Utils.clearFragment(getFragmentManager());

        Context context = getContext();
        if (context != null) {
            context.sendBroadcast(new Intent(Statistic.OPEN_MERGER_STUDIO));
        }
    }

    @Override
    public void onNoteListChanged(List<VideoModel> videoModelList) {

    }

    @Override
    public void onApplySelect(String nameFile) {
        mergerAudio(nameFile);
    }

    @Override
    public void onCancelSelect() {
        if (dialogInputName == null) {
            return;
        }

        dialogInputName.hideDialog();
    }

    @Override
    public void onFileNameEmpty() {
        Toast.makeText(getContext(), getString(R.string.error), Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onFileNameHasSpecialCharacter() {
        Toast.makeText(getContext(), getString(R.string.name_file_can_not_contain_character), Toast.LENGTH_SHORT).show();
    }
}
