package com.videomontage.freeeditingapps.fragment

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import androidx.appcompat.app.AlertDialog

/**
 * Created by <PERSON> on 11/19/2018.
 */
abstract class AbsDialog(private val context: Context) {
    private var builder: AlertDialog.Builder? = null
    private var alertDialog: AlertDialog? = null
    var view: View? = null
        private set

    abstract fun initLayout(): Int
    open fun initDialog() {
        view = LayoutInflater.from(context).inflate(initLayout(), null)
        builder = AlertDialog.Builder(context)
        builder!!.setView(view)
        alertDialog = builder!!.create()
        alertDialog!!.show()
    }

    fun showDialog() {
        alertDialog!!.show()
    }

    fun hideDialog() {
        alertDialog!!.dismiss()
    }
}