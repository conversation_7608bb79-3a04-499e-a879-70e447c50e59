package com.videomontage.freeeditingapps.fragment.viewmodel

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
//import com.my.dropbox_lib.data.local.database.model.StickerCache
//import com.my.dropbox_lib.data.local.database.model.StickerGroupCache
import com.videomontage.freeeditingapps.data.usecase.GetStickers

class StickerDialogViewModel : ViewModel() {


//    val stateListOfStickers : StateLiveData<List<List<StickerCache>>>
//    get() = GetStickers.stateListOfStickers
//
//    val stateListOfStickerGroupCache: StateLiveData<List<StickerGroupCache>>
//        get() = GetStickers.stateListOfStickerGroupCache
//
//    fun getStickers(context: Context){
//        val getStickersInstance = GetStickers()
//        getStickersInstance.getStickers(context)
//    }

}