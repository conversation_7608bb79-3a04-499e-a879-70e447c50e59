package com.videomontage.freeeditingapps.fragment

import android.app.ProgressDialog
import android.content.BroadcastReceiver
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.IntentFilter
import android.media.MediaExtractor
import android.media.MediaFormat
import android.media.MediaPlayer
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.util.Log
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.TextView
import android.widget.Toast
import com.arthenica.mobileffmpeg.Config
import com.arthenica.mobileffmpeg.FFmpeg
import com.halilibo.bettervideoplayer.BetterVideoCallback
import com.halilibo.bettervideoplayer.BetterVideoPlayer
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.analytics.AmplitudeHelper
import com.videomontage.freeeditingapps.analytics.FragmentAnalytics
import com.videomontage.freeeditingapps.analytics.TimeWatch
import com.videomontage.freeeditingapps.application.MyApplication
import com.videomontage.freeeditingapps.listener.IInputNameFile
import com.videomontage.freeeditingapps.model.ComModel
import com.videomontage.freeeditingapps.model.VideoModel
import com.videomontage.freeeditingapps.statistic.Statistic
import com.videomontage.freeeditingapps.utils.FileUtil
import com.videomontage.freeeditingapps.utils.Flog
import com.videomontage.freeeditingapps.utils.Utils
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.concurrent.TimeUnit

//import com.github.hiteshsondhi88.libffmpeg.ExecuteBinaryResponseHandler;
//import com.github.hiteshsondhi88.libffmpeg.FFmpeg;
//import com.github.hiteshsondhi88.libffmpeg.exceptions.FFmpegCommandAlreadyRunningException;
/**
 * Created by Hung on 11/15/2018.
 */
class AddMusicFragment : AbsFragment(), View.OnClickListener, SeekBar.OnSeekBarChangeListener,
    MediaPlayer.OnPreparedListener, MediaPlayer.OnCompletionListener, IInputNameFile {
    private var videoModel: VideoModel? = null
    private var bvp: BetterVideoPlayer? = null
    private var sbVolumeVideo: SeekBar? = null
    private var sbVolumeMusic: SeekBar? = null
    private var ivAddMusic: ImageView? = null
    private var pathMusicAdd: String? = null
    private var mediaPlayer: MediaPlayer? = null
    private var uriVideo: Uri? = null

    //    private FFmpeg ffmpeg;
    private var volumeVideo = 1.0f
    private var volumeMusic = 1.0f
    private val simpleDateFormat = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.US)
    private var dialogInputName: DialogInputName? = null
    private var tvProgressVideo: TextView? = null
    private var tvProgressMusic: TextView? = null
    private val isAddMusicSuccess = false
    private var progressDialog: ProgressDialog? = null
    private var ffmpegDone = false
    private var watch: TimeWatch? = null
    private var fragmentAnalytics: FragmentAnalytics? = null
    private val receiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (intent == null || intent.action == null) {
                return
            }
            when (intent.action) {
                Statistic.SEND_PATH_ADD_MUSIC -> {
                    pathMusicAdd = intent.getStringExtra(Statistic.PATH_MUSIC)
                    bvp!!.stop()
                    bvp!!.reset()
                    if (!bvp!!.isPrepared) {
                        bvp!!.prepare()
                    }
                    initMusic()
                }
            }
        }
    }

    private fun initMusic() {
        try {
            mediaPlayer = MediaPlayer()
            mediaPlayer!!.setDataSource(pathMusicAdd)
            mediaPlayer!!.setOnPreparedListener(this)
            mediaPlayer!!.setOnCompletionListener(this)
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    private fun releaseVideo() {
        if (bvp == null) return
        bvp!!.stop()
    }

    private var isCompleteVideo = false
    private fun initVideo(isReset: Boolean) {
        bvp!!.setAutoPlay(false)
        bvp!!.setSource(uriVideo!!)
        bvp!!.setHideControlsOnPlay(true)
        bvp!!.setBottomProgressBarVisibility(false)
        bvp!!.enableSwipeGestures(requireActivity().window)
        bvp!!.setCallback(object : BetterVideoCallback {
            override fun onStop(player: BetterVideoPlayer) {
                Flog.e(" Stop video ")
                if (mediaPlayer == null) {
                    return
                }
                mediaPlayer!!.stop()
            }

            override fun onStarted(player: BetterVideoPlayer) {
                Flog.e("Started")
                if (mediaPlayer == null) {
                    return
                }
                try {
                    mediaPlayer!!.start()
                    if (isCompleteVideo) {
                        mediaPlayer!!.seekTo(0)
                    }
                } catch (ex: IllegalStateException) {
                    ex.printStackTrace()
                }
            }

            override fun onPaused(player: BetterVideoPlayer) {
                Flog.e("Paused")
                if (mediaPlayer == null) {
                    return
                }
                try {
                    mediaPlayer!!.pause()
                } catch (ex: IllegalStateException) {
                    ex.printStackTrace()
                }
            }

            override fun onPreparing(player: BetterVideoPlayer) {
                Flog.e("Preparing")
            }

            override fun onPrepared(player: BetterVideoPlayer) {
                Flog.e("Prepared")
                try {
                    if (mediaPlayer == null) return
                    mediaPlayer!!.prepare()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }

            override fun onBuffering(percent: Int) {
                Flog.e("Buffering $percent")
            }

            override fun onError(player: BetterVideoPlayer, e: Exception) {
                Flog.e("Error " + e.message)
                Toast.makeText(
                    context,
                    getString(R.string.not_support_this_file),
                    Toast.LENGTH_SHORT
                ).show()
                activity!!.onBackPressed()
            }

            override fun onCompletion(player: BetterVideoPlayer) {
                isCompleteVideo = true
                if (mediaPlayer == null) {
                    return
                }
                try {
                    mediaPlayer!!.pause()
                } catch (ex: IllegalStateException) {
                    ex.printStackTrace()
                }
            }

            override fun onToggleControls(player: BetterVideoPlayer, isShowing: Boolean) {}
            override fun onSeekbarProgressChanged(position: Int) {
                if (mediaPlayer == null) return
                if (position < mediaPlayer!!.duration) {
                    mediaPlayer!!.seekTo(position)
                }
            }
        })
        //        bvp.setProgressCallback(new BetterVideoProgressCallback() {
//            @Override
//            public void onVideoProgressUpdate(int position, int duration) {
//                Flog.e(" preeeeeeeeeeee       " + position + "___" + duration);
//            }
//        });
    }

    override fun initViews() {
        watch = TimeWatch.start()
        fragmentAnalytics = FragmentAnalytics("AddMusicFragment")
        AmplitudeHelper.setAddMusicOpened()

//        ffmpeg = FFmpeg.getInstance(getContext());
        bvp = findViewById(R.id.bvp) as BetterVideoPlayer?
        tvProgressMusic = findViewById(R.id.tv_music_pecent) as TextView?
        tvProgressVideo = findViewById(R.id.tv_video_pecent) as TextView?
        sbVolumeVideo = findViewById(R.id.seekbar_volume) as SeekBar?
        sbVolumeMusic = findViewById(R.id.seekbar_music) as SeekBar?
        ivAddMusic = findViewById(R.id.iv_add_music) as ImageView?
        ivAddMusic!!.setOnClickListener { v: View? -> addMusic() }
        sbVolumeMusic!!.max = MAX_VOLUME
        sbVolumeVideo!!.max = MAX_VOLUME
        sbVolumeVideo!!.progress = MAX_VOLUME
        sbVolumeMusic!!.progress = MAX_VOLUME
        sbVolumeVideo!!.setOnSeekBarChangeListener(this)
        sbVolumeMusic!!.setOnSeekBarChangeListener(this)
        findViewById(R.id.view2)!!.setOnClickListener(this)
        findViewById(R.id.view3)!!.setOnClickListener(this)
        videoModel = requireArguments().getParcelable(Statistic.VIDEO_MODEL)
        uriVideo = Uri.fromFile(File(videoModel!!.path))
        initVideo(false)
        initActions()
    }

    private fun initActions() {
        val it = IntentFilter()
        it.addAction(Statistic.SEND_PATH_ADD_MUSIC)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            requireContext().registerReceiver(receiver, it, Context.RECEIVER_NOT_EXPORTED)
        } else {
            requireContext().registerReceiver(receiver, it)
        }
    }

    override fun onDestroy() {
        fragmentAnalytics!!.setDuration(watch!!.time(TimeUnit.SECONDS))
        MyApplication.addEvent(fragmentAnalytics)
        requireContext().unregisterReceiver(receiver)
        if (bvp != null) {
            bvp!!.stop()
            bvp!!.reset()
            bvp!!.release()
        }
        if (mediaPlayer != null) {
            mediaPlayer!!.stop()
            mediaPlayer!!.reset()
            mediaPlayer!!.release()
        }
        super.onDestroy()
    }

    private fun stopVideoAudio() {
        if (bvp != null) {
            bvp!!.stop()
            bvp!!.reset()
        }
    }

    private fun addMusic() {
        bvp!!.pause()
        requireActivity().supportFragmentManager.beginTransaction()
            .setCustomAnimations(
                R.anim.animation_left_to_right,
                R.anim.animation_right_to_left,
                R.anim.animation_left_to_right,
                R.anim.animation_right_to_left
            )
            .add(R.id.view_container, ChooseMusicFragment.newInstance())
            .addToBackStack(null)
            .commit()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_add_music, container, false)
    }

    override fun initToolbar() {
        super.initToolbar()
        toolbar!!.title = getString(R.string.add_music)
        toolbar!!.menu.findItem(R.id.item_save).setOnMenuItemClickListener { menuItem: MenuItem? ->
            if (bvp == null || pathMusicAdd == null) {
                Toast.makeText(context, getString(R.string.you_not_add_music), Toast.LENGTH_SHORT)
                    .show()
                return@setOnMenuItemClickListener true
            }
            fragmentAnalytics!!.addClick("Save")
            AmplitudeHelper.setSave()
            bvp!!.pause()
            initDialogSaveFile()
            true
        }
    }

    private fun initDialogSaveFile() {
        val defaultName = "VA_" + simpleDateFormat.format(System.currentTimeMillis())
        dialogInputName = context?.let { DialogInputName(it, this, defaultName, getString(R.string.save)) }
        dialogInputName?.initDialog()
    }

    override fun onClick(view: View) {}
    override fun onProgressChanged(seekBar: SeekBar, i: Int, b: Boolean) {
        when (seekBar.id) {
            R.id.seekbar_volume -> {
                volumeVideo = sbVolumeVideo!!.progress.toFloat() / 100
                tvProgressVideo!!.text = sbVolumeVideo!!.progress.toString() + "%"
                if (bvp == null) {
                    return
                }
                if (bvp!!.isPrepared) bvp!!.setVolume(volumeVideo, volumeVideo)
            }

            R.id.seekbar_music -> {
                volumeMusic = sbVolumeMusic!!.progress.toFloat() / 100
                tvProgressMusic!!.text = sbVolumeMusic!!.progress.toString() + "%"
                if (mediaPlayer == null) {
                    return
                }
                mediaPlayer!!.setVolume(volumeMusic, volumeMusic)
            }
        }
    }

    override fun onStartTrackingTouch(seekBar: SeekBar) {}
    override fun onStopTrackingTouch(seekBar: SeekBar) {}
    override fun onStop() {
        super.onStop()
        bvp!!.pause()
    }

    override fun onPrepared(mp: MediaPlayer) {
        Flog.e(" prepare  media  ")
        bvp!!.start()
    }

    override fun onCompletion(mp: MediaPlayer) {
        mediaPlayer!!.pause()
    }

    override fun onApplySelect(nameFile: String) {
        saveFileAddMusic(nameFile)
    }

    var pathNewFile: String? = null

    private fun saveFileAddMusic(nameFile: String) {
        isCancelSaveFile = false

        pathNewFile = (Environment.getExternalStoragePublicDirectory(
            Environment.DIRECTORY_DCIM
        ).toString() + File.separator + "Montage" + Statistic.DIR_ADD_MUSIC + "/")
        if (!File(pathNewFile).exists()) {
            File(pathNewFile).mkdirs()
        }
        pathNewFile = pathNewFile + nameFile + Utils.getFileExtension(videoModel!!.path)
        val f = File(pathNewFile)
        if (f.exists()) {
            Toast.makeText(context, getString(R.string.name_file_exist), Toast.LENGTH_SHORT).show()
            return
        }

        val hasAudioStream = hasAudioStream(videoModel!!.path)

        val commandNewApi = if (hasAudioStream) {

            "-i " + ComModel.getCharEscaptedPath(videoModel!!.path) + " -i " +
                    ComModel.getCharEscaptedPath(pathMusicAdd) + " -filter_complex \"[0:a]volume=" + volumeVideo + "[a0];[1:0]volume=" + volumeMusic + "[a1];[a0][a1]amix=inputs=2:duration=longest:dropout_transition=2,aresample=async=1[a]\" " +
                    "-map 0:v -map [a] -c:v copy -c:a aac " + pathNewFile

        } else {
            "-i " + ComModel.getCharEscaptedPath(videoModel!!.path) + " -i " +
                    ComModel.getCharEscaptedPath(pathMusicAdd) + " -filter_complex \"[1:0]volume=" + volumeMusic + "[a1];[a1]amix=inputs=1:duration=first:dropout_transition=2[a]\" " +
                    "-map 0:v -map [a] -c:v copy -c:a aac " + pathNewFile
        }

        initDialogProgress()

        execFFmpegBinary(commandNewApi, pathNewFile!!, nameFile)
    }

    private fun hasAudioStream(videoPath: String): Boolean {
        val extractor = MediaExtractor()
        try {
            extractor.setDataSource(videoPath)
            for (i in 0 until extractor.trackCount) {
                val format = extractor.getTrackFormat(i)
                val mime = format.getString(MediaFormat.KEY_MIME)
                if (mime?.startsWith("audio/") == true) {
                    return true
                }
            }
        } catch (e: IOException) {
            e.printStackTrace()
        } finally {
            extractor.release()
        }
        return false
    }


    override fun afterInterstitial() {
        Handler().postDelayed({
            if (ffmpegDone) afterFFmpegOnSuccess()
            ffmpegDone = false
        }, 1000)
    }

    private fun execFFmpegBinary(command: Any, path: String, title: String) {
//        Log.e("xxx", "cccccccccccccc");
        if (ComModel.isNewApiRequired()) {
            val myCommand = command as String
            ffmpegDone = false
            val executionId = FFmpeg.executeAsync(myCommand) { executionId, rc ->
                if (rc == Config.RETURN_CODE_SUCCESS) {
                    Log.i(Config.TAG, "Command execution completed successfully.")
                    afterFFmpegSuccess(path, title)
                } else if (rc == Config.RETURN_CODE_CANCEL) {
                    Log.i(Config.TAG, "Command execution cancelled by user.")
                    afterFFmpegFailure()
                } else {
                    Log.i(
                        Config.TAG,
                        String.format(
                            "Command execution failed with rc=%d and the output below.",
                            rc
                        )
                    )
                    Config.printLastCommandOutput(Log.INFO)
                    afterFFmpegFailure()
                }
            }
            Config.enableLogCallback { message ->
                val durationFile =
                    Utils.getProgress(message.text, videoModel!!.duration.toLong() / 1000).toInt()
                val percent = durationFile / (videoModel!!.duration.toFloat() / 1000)
                if (progressDialog != null) {
                    if (percent * 100 > 0 && percent * 100 <= 100) {
                        progressDialog!!.progress = (percent * 100).toInt()
                    }
                }
            }
        }
    }

    private fun afterFFmpegFailure() {
        if (progressDialog != null) {
            progressDialog!!.dismiss()
        }
        Toast.makeText(
            context,
            requireContext().resources.getString(R.string.warning_video_must_have_audio),
            Toast.LENGTH_SHORT
        ).show()
    }

    private fun afterFFmpegSuccess(path: String, title: String) {
        ffmpegDone = true
        //                    Flog.e("Successs     " + s);
        if (isCancelSaveFile) return
        if (progressDialog != null) {
            progressDialog!!.progress = 100
            progressDialog!!.dismiss()
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) FileUtil.scanGallery(
            context, path
        ) else FileUtil.addFileToContentProvider(
            context, path, title
        )
        //        FileUtil.addFileToContentProvider(getContext(), path, title);
        Toast.makeText(context, getString(R.string.create_file) + ": " + path, Toast.LENGTH_SHORT)
            .show()
        afterFFmpegOnSuccess()
    }

    private fun afterFFmpegOnSuccess() {
        if (isPauseFragment) {
            return
        }
        Utils.clearFragment(fragmentManager)
        requireContext().sendBroadcast(Intent(Statistic.OPEN_ADD_MUSIC_STUDIO))
    }

    private fun initDialogProgress() {
        progressDialog = ProgressDialog(context)
        progressDialog!!.setCancelable(false)
        progressDialog!!.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL)
        progressDialog!!.setTitle(getString(R.string.progress_dialog_saving))
        progressDialog!!.progress = 0
        progressDialog!!.setButton(
            DialogInterface.BUTTON_NEGATIVE,
            getString(R.string.cancel)
        ) { dialog: DialogInterface?, which: Int -> cancelAddMusic() }
        progressDialog!!.show()
    }

    private var isCancelSaveFile = false
    private fun cancelAddMusic() {
        isCancelSaveFile = true

//        if (ffmpeg.isFFmpegCommandRunning()) {
//            ffmpeg.killRunningProcesses();
//        }
        if (pathNewFile != null) {
            File(pathNewFile).delete()
        }
        if (progressDialog != null) {
            progressDialog!!.dismiss()
        }
        dialogInputName!!.hideDialog()
    }

    override fun onCancelSelect() {
        if (dialogInputName != null) {
            dialogInputName!!.hideDialog()
        }
    }

    override fun onFileNameEmpty() {
        Toast.makeText(context, getString(R.string.name_file_can_not_empty), Toast.LENGTH_SHORT)
            .show()
    }

    override fun onFileNameHasSpecialCharacter() {
        Toast.makeText(
            context,
            getString(R.string.name_file_can_not_contain_character),
            Toast.LENGTH_SHORT
        ).show()
    }

    companion object {
        private const val MAX_VOLUME = 100
        @JvmStatic
        fun newInstance(bundle: Bundle?): AddMusicFragment {
            val fragment = AddMusicFragment()
            fragment.arguments = bundle
            return fragment
        }
    }
}