//package com.videomontage.freeeditingapps.adapter;
//
//import android.app.Activity;
//import android.content.Context;
//
//import com.videomontage.freeeditingapps.fragment.PageFragment;
//
//import androidx.fragment.app.Fragment;
//import androidx.fragment.app.FragmentManager;
//import androidx.fragment.app.FragmentPagerAdapter;
//
//public class SampleFragmentPagerAdapter extends FragmentPagerAdapter {
//    final int PAGE_COUNT = 3;
//    private String tabTitles[] = new String[] { "Tab1", "Tab2", "Tab3" };
//    private Context context;
//
//    public SampleFragmentPagerAdapter(FragmentManager fm, Context context) {
//        super(fm);
//        this.context = context;
//    }
//
//    @Override
//    public int getCount() {
//        return PAGE_COUNT;
//    }
//
//    @Override
//    public Fragment getItem(int position) {
//        return PageFragment.newInstance(position + 1);
//    }
//
//    @Override
//    public CharSequence getPageTitle(int position) {
//        // Generate title based on item position
////        return tabTitles[position];
//        return null;
//    }
//}
