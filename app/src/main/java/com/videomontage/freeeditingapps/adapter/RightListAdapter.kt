package com.videomontage.freeeditingapps.adapter

import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.adapter.AudioFilesViewHolder.Companion.bind
import com.videomontage.freeeditingapps.listener.OnGalleryFileSelectListener
import com.videomontage.freeeditingapps.model.VideoModel
import com.yalantis.multiselection.lib.adapter.BaseRightAdapter

class RightListAdapter(
    private val mContext: Context,
    private var mListener: OnGalleryFileSelectListener
) : BaseRightAdapter<VideoModel, AudioFilesViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AudioFilesViewHolder {
        val view = LayoutInflater.from(mContext).inflate(R.layout.item_gallery, parent, false)
        return AudioFilesViewHolder(view)
    }

    override fun onBindViewHolder(holder: AudioFilesViewHolder, position: Int) {
        super.onBindViewHolder(holder, position)
        bind(mContext, holder, getItemAt(position))
        holder.itemView.setOnClickListener { v: View ->
            v.isPressed = true
            v.postDelayed({
                v.isPressed = false
                Log.e("xxx", " index  " + holder.adapterPosition)
                mListener.onGalleryFileSelectListener(holder.adapterPosition)
            }, 200)
        }
    }

    fun setOnGalleryFileSelectListener(mListener: OnGalleryFileSelectListener) {
        this.mListener = mListener
    }
}