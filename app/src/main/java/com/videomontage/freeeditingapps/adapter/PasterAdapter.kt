package com.videomontage.freeeditingapps.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.PagerAdapter
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.databinding.ItemPasterBinding

class PasterAdapter(private val context: Context, private val imgList: IntArray?) :
    RecyclerView.Adapter<PasterAdapter.ViewHolder>() {
    private val TAG = PagerAdapter::class.java.simpleName
    private val imagesGif = intArrayOf(
        R.raw.aini,
        R.raw.dengliao,
        R.raw.baituole,
        R.raw.burang<PERSON>,
        R.raw.bufuhanz<PERSON>,
        R.raw.nizabushagntian,
        R.raw.zan,
        R.raw.buyue,
        R.raw.nizai<PERSON><PERSON>,
        R.raw.<PERSON>,
        R.raw.xiase
    )

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(
                context
            ).inflate(R.layout.item_paster, parent, false)
        )
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.binding.pasterview.setImageResource(imgList!![position])
    }

    override fun getItemCount(): Int {
        return imgList?.size ?: 0
    }

    inner class ViewHolder(itemView: View?) : RecyclerView.ViewHolder(
        itemView!!
    ) {
        val binding: ItemPasterBinding

        init {
            binding = ItemPasterBinding.bind(itemView!!)
            binding.pasterview.setOnClickListener {
                if (pasterItemSelectListener != null) {
                    pasterItemSelectListener!!.pasterItemSelect(
                        imgList!![layoutPosition],
                        imagesGif[layoutPosition]
                    )
                }
            }
        }
    }

    interface PasterItemSelectListener {
        fun pasterItemSelect(resourseId: Int, gifId: Int)
    }

    var pasterItemSelectListener: PasterItemSelectListener? = null
}