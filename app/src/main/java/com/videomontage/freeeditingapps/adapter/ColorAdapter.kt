package com.videomontage.freeeditingapps.adapter

import android.content.Context
import android.graphics.*
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.AsyncListDiffer
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.videomontage.freeeditingapps.R
//import kotlinx.android.synthetic.main.color_item.view.*


class ColorAdapter : RecyclerView.Adapter<ColorAdapter.ColorViewHolder>() {

    inner class ColorViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    var lastClicked = 0

    private val differCallback = object : DiffUtil.ItemCallback<String>() {
        override fun areItemsTheSame(oldItem: String, newItem: String): Boolean {
            return oldItem.contentEquals(newItem)
        }

        override fun areContentsTheSame(oldItem: String, newItem: String): Boolean {
            return oldItem == newItem
        }
    }

    val differ = AsyncListDiffer(this, differCallback)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ColorViewHolder {
        return ColorViewHolder(
                LayoutInflater.from(parent.context).inflate(
                        R.layout.color_item,
                        parent,
                        false
                )
        )
    }

    override fun getItemCount(): Int {
        return differ.currentList.size
    }

    private var onItemClickListener: ((String) -> Unit)? = null

    override fun onBindViewHolder(holder: ColorViewHolder, position: Int) {
        val colorItem = differ.currentList[position]

        holder.itemView.apply {
        val colorIv: ImageView = findViewById(R.id.colorIv)

//            colorIv.setBackgroundColor(Color.parseColor(colorItem))
//            colorIv.setColorFilter(Color.YELLOW, android.graphics.PorterDuff.Mode.MULTIPLY)

            var bmp = Bitmap.createBitmap(500, 500, Bitmap.Config.ARGB_8888)
            var canvas = Canvas(bmp);
            canvas.drawColor(Color.parseColor(colorItem))

            colorIv.setImageBitmap(getRoundedCornerBitmap(bmp, if (lastClicked == position) Color.WHITE else Color.parseColor(colorItem), 16, 16, this.context))
            setOnClickListener {
                lastClicked = position
                notifyDataSetChanged()
                onItemClickListener?.let { it(colorItem) }
            }
        }
    }

    fun getRoundedCornerBitmap(bitmap: Bitmap, color: Int, cornerDips: Int, borderDips: Int, context: Context): Bitmap? {
        val output = Bitmap.createBitmap(bitmap.width, bitmap.height,
                Bitmap.Config.ARGB_8888)
        val canvas = Canvas(output)
        val borderSizePx = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, borderDips.toFloat(),
                context.getResources().getDisplayMetrics()).toInt()
        val cornerSizePx = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, cornerDips.toFloat(),
                context.getResources().getDisplayMetrics()).toInt()
        val paint = Paint()
        val rect = Rect(0, 0, bitmap.width, bitmap.height)
        val rectF = RectF(rect)

        // prepare canvas for transfer
        paint.isAntiAlias = true
        paint.color = -0x1
        paint.style = Paint.Style.FILL
        canvas.drawARGB(0, 0, 0, 0)
        canvas.drawRoundRect(rectF, cornerSizePx.toFloat(), cornerSizePx.toFloat(), paint)

        // draw bitmap
        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)
        canvas.drawBitmap(bitmap, rect, rect, paint)

        // draw border
        paint.color = color
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = borderSizePx.toFloat()
        canvas.drawRoundRect(rectF, cornerSizePx.toFloat(), cornerSizePx.toFloat(), paint)
        return output
    }

    fun setOnItemClickListener(listener: (String) -> Unit) {
        onItemClickListener = listener
    }

}