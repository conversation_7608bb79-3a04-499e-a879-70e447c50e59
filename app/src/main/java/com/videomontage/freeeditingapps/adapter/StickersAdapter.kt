//package com.videomontage.freeeditingapps.adapter
//
//import android.graphics.Bitmap
//import android.view.LayoutInflater
//import android.view.View
//import android.view.ViewGroup
//import androidx.recyclerview.widget.AsyncListDiffer
//import androidx.recyclerview.widget.DiffUtil
//import androidx.recyclerview.widget.RecyclerView
//import com.bumptech.glide.Glide
//import com.bumptech.glide.request.RequestOptions
//import com.bumptech.glide.request.target.BitmapImageViewTarget
//import com.my.dropbox_lib.data.local.database.model.StickerCache
//import com.my.dropbox_lib.domain.LoadStickers
//import com.videomontage.freeeditingapps.R
//import kotlinx.android.synthetic.main.sticker_item.view.*
//
//
//class StickersAdapter : RecyclerView.Adapter<StickersAdapter.StickerViewHolder>() {
//
//    inner class StickerViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)
//
//    private val differCallback = object : DiffUtil.ItemCallback<StickerCache>() {
//        override fun areItemsTheSame(oldItem: StickerCache, newItem: StickerCache): Boolean {
//            return oldItem.url == newItem.url
//        }
//
//        override fun areContentsTheSame(oldItem: StickerCache, newItem: StickerCache): Boolean {
//            return oldItem == newItem
//        }
//    }
//
//    val differ = AsyncListDiffer(this, differCallback)
//
//    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): StickerViewHolder {
//        return StickerViewHolder(
//                LayoutInflater.from(parent.context).inflate(
//                        R.layout.sticker_item,
//                        parent,
//                        false
//                )
//        )
//    }
//
//    override fun getItemCount(): Int {
//        return differ.currentList.size
//    }
//
//    private var onItemClickListener: ((Bitmap) -> Boolean)? = null
//
//    override fun onBindViewHolder(holder: StickerViewHolder, position: Int) {
//        val stickerItem = differ.currentList[position]
//        holder.itemView.apply {
//            var bitmap : Bitmap
//            val conf = Bitmap.Config.ARGB_8888 // see other conf types
//            bitmap = Bitmap.createBitmap(500, 500, conf)
//
//            val options = RequestOptions()
//                    .centerCrop()
//                    .placeholder(R.mipmap.ic_launcher)
//                    .error(R.mipmap.ic_launcher)
//
//
////            Glide.with(this).load(stickerItem.url).apply(options).into(stickerIv)
//
//            Glide.with(context)
//                    .asBitmap()
//                    .apply(options)
//                    .load(stickerItem.path)
//                    .into(object : BitmapImageViewTarget(stickerIv) {
//                        override fun setResource(resource: Bitmap?) {
//                            // Do bitmap magic here
//                            super.setResource(resource)
//                            if (resource != null) {
//                                bitmap = resource
//                            }
//
//                        }
//                    })
//
////            stickerIv.setImageBitmap(LoadStickers.bitmap)
//
////            ImageDownloader.getBitmap(this.context, stickerItem.url){
////                Log.d("ddd", "load: aaaaaaaaaaaaaaaaaaa")
////                stickerIv.setImageBitmap(it)
////
////            }
//
//            setOnClickListener {
//                onItemClickListener?.let { it(bitmap) }
//            }
//        }
//    }
//
//    fun setOnItemClickListener(listener: (Bitmap) -> Boolean) {
//        onItemClickListener = listener
//    }
//
//}