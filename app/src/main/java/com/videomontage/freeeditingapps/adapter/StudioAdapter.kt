package com.videomontage.freeeditingapps.adapter

import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import com.videomontage.freeeditingapps.fragment.StudioFragmentDetail
import com.videomontage.freeeditingapps.statistic.Statistic

class StudioAdapter(fm: FragmentManager?) : FragmentStatePagerAdapter(
    fm!!
) {
    private val listTab = arrayOf(CUTTER, MERGER, CONVERTER, RECORDER, EFFECTS)
    override fun getItem(position: Int): Fragment {
        return when (position) {
            INDEX_CUTTER -> {
                val bundle = Bundle()
                bundle.putString(
                    Statistic.CHECK_STUDIO_FRAGMENT,
                    Statistic.DIR_APP + Statistic.DIR_CUTTER
                )
                return StudioFragmentDetail.newInstance(bundle)
            }

            INDEX_MERGER -> {
                val b1 = Bundle()
                b1.putString(
                    Statistic.CHECK_STUDIO_FRAGMENT,
                    Statistic.DIR_APP + Statistic.DIR_MERGER
                )
                return StudioFragmentDetail.newInstance(b1)
            }

            INDEX_SPEED -> {
                val b2 = Bundle()
                b2.putString(
                    Statistic.CHECK_STUDIO_FRAGMENT,
                    Statistic.DIR_APP + Statistic.DIR_SPEED
                )
                return StudioFragmentDetail.newInstance(b2)
            }

            INDEX_ADD_MUSIC -> {
                val b3 = Bundle()
                b3.putString(
                    Statistic.CHECK_STUDIO_FRAGMENT,
                    Statistic.DIR_APP + Statistic.DIR_ADD_MUSIC
                )
                return StudioFragmentDetail.newInstance(b3)
            }

            INDEX_EFFECTS -> {
                val b4 = Bundle()
                b4.putString(
                    Statistic.CHECK_STUDIO_FRAGMENT,
                    Statistic.DIR_APP + Statistic.DIR_EFFECTS
                )
                return StudioFragmentDetail.newInstance(b4)
            }

            else -> {
                val b4 = Bundle()
                b4.putString(
                    Statistic.CHECK_STUDIO_FRAGMENT,
                    Statistic.DIR_APP + Statistic.DIR_EFFECTS
                )
                return StudioFragmentDetail.newInstance(b4)
            }
        }
    }

    override fun getCount(): Int {
        return listTab.size
    }

    companion object {
        private const val CUTTER = "Cutter"
        private const val MERGER = "Merger"
        private const val CONVERTER = "Converter"
        private const val RECORDER = "Recorder"
        private const val EFFECTS = "Effects"
        private const val INDEX_CUTTER = 0
        private const val INDEX_SPEED = 1
        private const val INDEX_MERGER = 2
        private const val INDEX_ADD_MUSIC = 3
        private const val INDEX_EFFECTS = 4
    }
}