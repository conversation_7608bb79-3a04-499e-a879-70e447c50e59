package com.videomontage.freeeditingapps.adapter

import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.CompoundButton
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.fragment.StudioFragmentDetail
import com.videomontage.freeeditingapps.model.VideoModel
import com.videomontage.freeeditingapps.utils.Utils
import java.io.File

/**
 * Created by <PERSON> on 11/15/2018.
 */
class VideoStudioAdapter(
    private var videoModelList: List<VideoModel>,
    private val callback: ItemSelectedStudio,
    private val context: StudioFragmentDetail,
    private val isStudio: Boolean
) : RecyclerView.Adapter<VideoStudioAdapter.ViewHolder>() {
    fun setFilter(list: List<VideoModel>) {
        videoModelList = ArrayList()
        videoModelList = list
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val itemView = inflater.inflate(R.layout.item_studio, parent, false)
        return ViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val videoModel = videoModelList[position]
        holder.checkBox.isChecked = videoModelList[position].isCheck
        holder.tvName.text = videoModel.nameAudio
        holder.tvTime.text = Utils.convertDate(
            videoModel.dateModifier,
            Utils.CONVERT_LONG_TO_DATE
        )
        holder.tvDateTime.text =
            (Utils.getStringSizeLengthFile(videoModel.size)
                    + "  " + Utils.getFileExtension(videoModel.path)
                    + "  " + Utils.convertMillisecond(videoModel.duration.toLong()))
        Glide.with(context).load(Uri.fromFile(File(videoModel.path))).into(holder.ivThumb)

        // action mode
        if (context.isActionMode) {
            if (context.isSelectAll) {
                holder.checkBox.isChecked = videoModelList[position].isCheck
            }
            holder.checkBox.visibility = View.VISIBLE
            holder.ivMore.visibility = View.GONE
        } else {
            holder.checkBox.visibility = View.GONE
            holder.ivMore.visibility = View.VISIBLE
        }
    }

    override fun getItemCount(): Int {
        return videoModelList.size
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvName: TextView
        val tvTime: TextView
        val tvDateTime: TextView
        val ivThumb: ImageView
        val ivMore: ImageView
        val checkBox: CheckBox

        init {
            tvName = itemView.findViewById(R.id.tv_name)
            tvTime = itemView.findViewById(R.id.tv_time)
            tvDateTime = itemView.findViewById(R.id.tv_date_time)
            ivThumb = itemView.findViewById(R.id.iv_thumb)
            ivMore = itemView.findViewById(R.id.iv_more)
            checkBox = itemView.findViewById(R.id.checkbox)
            checkBox.setOnCheckedChangeListener { compoundButton: CompoundButton?, checked: Boolean ->
                if (context.isActionMode) {
                    if (checked) {
                        videoModelList[adapterPosition].isCheck = true
                        var isAll = true
                        for (videoModel1 in videoModelList) {
                            if (!videoModel1.isCheck) {
                                isAll = false
                            }
                        }
                        if (isAll) context.isSelectAll = true
                    } else {
                        context.isSelectAll = false
                        videoModelList[adapterPosition].isCheck = false
                    }
                    context.prepareSelection(checkBox, adapterPosition)
                }
            }
            itemView.setOnClickListener { v: View? ->
                if (context.isActionMode) {
                    val videoModel = videoModelList[adapterPosition]
                    context.isSelectAll = false
                    if (videoModel.isCheck) {
                        videoModel.isCheck = false
                        checkBox.isChecked = videoModel.isCheck
                    } else {
                        videoModel.isCheck = true
                        checkBox.isChecked = videoModel.isCheck
                    }
                    context.prepareSelection(checkBox, adapterPosition)
                } else {
                    callback.onClick(adapterPosition)
                }
                context.isSelectAll = false
            }
            itemView.setOnLongClickListener { v: View? ->
                callback.onLongClick(
                    adapterPosition
                )
            }
            ivMore.setOnClickListener { v: View? ->
                callback.onOptionClick(
                    adapterPosition
                )
            }
        }
    }

    interface ItemSelectedStudio {
        fun onClick(index: Int)
        fun onLongClick(index: Int): Boolean
        fun onOptionClick(index: Int)
    }
}