package com.videomontage.freeeditingapps.adapter

import android.content.Context
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.model.VideoModel
import com.videomontage.freeeditingapps.utils.Utils

class AudioFilesViewHolder(view: View) : RecyclerView.ViewHolder(view) {
    var name: TextView
    var size: TextView
    var dateCreate: TextView
    var avatar: ImageView

    init {
        name = view.findViewById(R.id.name)
        avatar = view.findViewById(R.id.avatar)
        size = view.findViewById(R.id.size)
        dateCreate = view.findViewById(R.id.dateCreate)
    }

    companion object {
        @JvmStatic
        fun bind(context: Context?, viewHolder: AudioFilesViewHolder, videoModel: VideoModel) {
            viewHolder.name.text = videoModel.nameAudio
            viewHolder.size.text =
                (Utils.getStringSizeLengthFile(videoModel.size)
                        + "  " + Utils.getFileExtension(
                    videoModel.path
                            + "  " + Utils.convertMillisecond(
                        videoModel.duration.toLong()
                    )
                ))
            Glide.with(context!!).load(videoModel.path).into(viewHolder.avatar)
        }
    }
}