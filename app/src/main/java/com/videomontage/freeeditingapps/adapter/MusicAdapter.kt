package com.videomontage.freeeditingapps.adapter

import android.net.Uri
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.videomontage.freeeditingapps.databinding.ItemMusicBinding
import com.videomontage.freeeditingapps.model.MusicModel
import com.videomontage.freeeditingapps.utils.Utils
import java.io.File

/**
 * Created by <PERSON> on 11/19/2018.
 */
class MusicAdapter(
    private var musicModelList: List<MusicModel>,
    private val callback: ItemSelected
) : RecyclerView.Adapter<MusicAdapter.MusicAdapterViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MusicAdapterViewHolder {
        val binding = ItemMusicBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return MusicAdapterViewHolder(binding)
    }

    override fun onBindViewHolder(holder: MusicAdapterViewHolder, position: Int) {
        val musicModel = musicModelList[position]
        holder.bind(musicModel)
    }

    override fun getItemCount(): Int {
        return musicModelList.size
    }

    fun setFilter(list: List<MusicModel>) {
        musicModelList = list
        notifyDataSetChanged()
    }

    inner class MusicAdapterViewHolder(private val binding: ItemMusicBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(musicModel: MusicModel) {
            binding.tvName.text = musicModel.title
            binding.tvTime.text = Utils.convertMillisecond(musicModel.duration)
            Glide.with(binding.root.context).load(Uri.fromFile(File(musicModel.filePath))).into(binding.ivThumb)
            binding.root.setOnClickListener { callback.onClick(adapterPosition) }
        }
    }

    interface ItemSelected {
        fun onClick(index: Int)
    }
}
