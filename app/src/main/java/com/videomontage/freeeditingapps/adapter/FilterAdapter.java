package com.videomontage.freeeditingapps.adapter;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.bumptech.glide.Glide;
import com.bumptech.glide.Priority;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.videomontage.freeeditingapps.R;

import java.util.ArrayList;

public class FilterAdapter extends BaseAdapter {
    private Context mContext;
    private final ArrayList Imageid;
    private final ArrayList web;
//    private final String[] web;

    public FilterAdapter(Context c, ArrayList web, ArrayList Imageid) {
        mContext = c;
        this.Imageid = Imageid;
        this.web = web;
    }

    @Override
    public int getCount() {
        return Imageid.size();
    }

    @Override
    public Object getItem(int position) {
        return position;
    }

    @Override
    public long getItemId(int position) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup
            parent) {
        LayoutInflater inflater = (LayoutInflater)
                mContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View gridView;

            gridView = new View(mContext);
            // get layout from mobile.xml
            gridView = inflater.inflate(R.layout.filter_item_, null);
            // set value into textview
            TextView textView = (TextView)
                    gridView.findViewById(R.id.grid_item_label);
            textView.setText(web.get(position).toString());
            // set image based on selected text
            ImageView imageView = (ImageView)
                    gridView.findViewById(R.id.grid_item_image);
            RequestOptions options = new RequestOptions()
                    .centerCrop()
                    .placeholder(R.drawable.btn_fx)
                    .error(R.drawable.btn_fx)
                    .diskCacheStrategy(DiskCacheStrategy.NONE)
                    .skipMemoryCache(true)
                    .priority(Priority.HIGH);
            Glide.with(mContext)
                    .load(Imageid.get(position))
                    .apply(options)
                    .into(imageView);
        return gridView;
    }
}