package com.videomontage.freeeditingapps.adapter

import android.content.Context
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.model.VideoModel
import com.videomontage.freeeditingapps.utils.Utils
import java.io.File

/**
 * Created by <PERSON> on 11/15/2018.
 */
class VideoAdapter(
    private var videoModelList: List<VideoModel>,
    private val callback: ItemSelected,
    private val context: Context,
    private val isStudio: Boolean
) : RecyclerView.Adapter<VideoAdapter.ViewHolder>() {
    fun setFilter(list: List<VideoModel>) {
        videoModelList = ArrayList()
        videoModelList = list
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val itemView = inflater.inflate(R.layout.item_video, parent, false)
        return ViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val videoModel = videoModelList[position]
        holder.tvName.text = videoModel.nameAudio
        holder.tvTime.text =
            Utils.getStringSizeLengthFile(videoModel.size) + "     " + Utils.convertMillisecond(
                videoModel.duration.toLong()
            )
        Glide.with(context).load(Uri.fromFile(File(videoModel.path))).into(holder.ivThumb)
        if (isStudio) {
            holder.ivMore.visibility = View.VISIBLE
        } else {
            holder.ivMore.visibility = View.GONE
        }
    }

    override fun getItemCount(): Int {
        return videoModelList.size
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvName: TextView
        val tvTime: TextView
        val ivThumb: ImageView
        val ivMore: ImageView
        val checkBox: CheckBox? = null

        init {
            tvName = itemView.findViewById(R.id.tv_name)
            tvTime = itemView.findViewById(R.id.tv_time)
            ivThumb = itemView.findViewById(R.id.iv_thumb)
            ivMore = itemView.findViewById(R.id.iv_more)
            itemView.setOnClickListener { v: View? -> callback.onClick(adapterPosition) }
            itemView.setOnLongClickListener { v: View? ->
                callback.onLongClick(
                    adapterPosition
                )
            }
            ivMore.setOnClickListener { v: View? ->
                callback.onOptionClick(
                    adapterPosition
                )
            }
        }
    }

    interface ItemSelected {
        fun onClick(index: Int)
        fun onLongClick(index: Int): Boolean
        fun onOptionClick(index: Int)
    }
}