package com.videomontage.freeeditingapps.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.MotionEventCompat
import androidx.recyclerview.widget.RecyclerView
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.listener.IItemTouchHelperAdapter
import com.videomontage.freeeditingapps.listener.IListSongChanged
import com.videomontage.freeeditingapps.model.VideoModel
import com.videomontage.freeeditingapps.utils.Utils
import java.util.Collections

class SortAdapter(
    private val videoModelList: MutableList<VideoModel?>,
    private val context: Context,
    private val callback: OnStartDragListener,
    private val iListSongChanged: IListSongChanged
) : RecyclerView.Adapter<SortAdapter.ViewHolder?>(), IItemTouchHelperAdapter {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context).inflate(R.layout.item_sort_audio, null)
        )
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val videoModel = videoModelList[position]
        holder.tvName.text = videoModel!!.nameAudio
        holder.tvArtist.text = videoModel.nameArtist
        holder.tvDuration.text = Utils.convertMillisecond(
            videoModel.duration.toLong()
        )
        holder.ivSort.setOnTouchListener { view: View?, motionEvent: MotionEvent? ->
            if (MotionEventCompat.getActionMasked(motionEvent) == MotionEvent.ACTION_DOWN) {
                callback.onStartDrag(holder)
            }
            false
        }
    }

    interface OnStartDragListener {
        fun onStartDrag(viewHolder: RecyclerView.ViewHolder?)
    }

    override fun getItemCount(): Int {
        return videoModelList.size
    }

    override fun onItemDismiss(position: Int) {
        videoModelList.removeAt(position)
        iListSongChanged.onNoteListChanged(videoModelList)
        notifyItemRemoved(position)
    }

    override fun onItemMove(fromPosition: Int, toPosition: Int): Boolean {
        Collections.swap(videoModelList, fromPosition, toPosition)
        iListSongChanged.onNoteListChanged(videoModelList)
        notifyItemMoved(fromPosition, toPosition)
        return true
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val ivSort: ImageView
        val tvName: TextView
        val tvArtist: TextView
        val tvDuration: TextView

        init {
            ivSort = itemView.findViewById(R.id.iv_sort)
            tvName = itemView.findViewById(R.id.name_song)
            tvArtist = itemView.findViewById(R.id.name_artist)
            tvDuration = itemView.findViewById(R.id.duration)
        }
    }
}