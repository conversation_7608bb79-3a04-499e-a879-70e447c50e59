package com.videomontage.freeeditingapps.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.Priority
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.model.ComModel

class OptionsAdapter(context: Context?, private val mAnimals: MutableList<ComModel>) :
    RecyclerView.Adapter<OptionsAdapter.ViewHolder>() {
    private val mInflater: LayoutInflater
    private var mClickListener: ItemClickListener? = null
    private var mLongClickListener: ItemLongClickListener? = null
    private var clickedPosition = 0

    // data is passed into the constructor
    init {
        mInflater = LayoutInflater.from(context)
    }

    // inflates the row layout from xml when needed
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = mInflater.inflate(R.layout.option_item, parent, false)
        return ViewHolder(view)
    }

    // binds the data to the view and textview in each row
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
//        holder.setIsRecyclable(false);
        val animal = mAnimals[position].effectName
        //        holder.myView.setBackgroundColor(Color.MAGENTA);
        holder.myTextView.text = animal
        if (clickedPosition == position) holder.myTextView.setTextColor(
            holder.myTextView.context.resources.getColor(
                R.color.colorAccentTwo
            )
        ) else holder.myTextView.setTextColor(
            holder.myTextView.context.resources.getColor(
                R.color.colorPrimaryDark
            )
        )
        val options = RequestOptions()
            .centerCrop()
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .priority(Priority.HIGH)
        Glide.with(holder.imageView.context)
            .load(mAnimals[position].drawable)
            .apply(options)
            .into(holder.imageView)
        holder.bindDataWithHolder(mAnimals[position])

//        Log.d("ddd", "onBindViewHolder: "+position +" / " + mAnimals.get(position).isPreviewAvailable());

//        if (mAnimals.get(position).isPreviewAvailable()) {
//            holder.visibilityIV.setVisibility(View.VISIBLE);
//            holder.visibilityIV.bringToFront();
//
//        }
    }

    // total number of rows
    override fun getItemCount(): Int {
        return mAnimals.size
    }

    fun setPreviewAvailable(position: Int) {
        mAnimals.removeAt(position)
        val comModel = ComModel.getListOfCommands()[position]
        comModel.isPreviewAvailable = true
        mAnimals.add(position, comModel)
        notifyItemChanged(position)
    }

    // stores and recycles views as they are scrolled off screen
    inner class ViewHolder internal constructor(itemView: View) : RecyclerView.ViewHolder(itemView),
        View.OnClickListener, View.OnLongClickListener {
        var imageView: ImageView
        var visibilityIV: ImageView
        var myTextView: TextView

        init {
            imageView = itemView.findViewById(R.id.imageView)
            visibilityIV = itemView.findViewById(R.id.visibilityIV)
            myTextView = itemView.findViewById(R.id.tvAnimalName)
            itemView.setOnClickListener(this)
            itemView.setOnLongClickListener(this)
        }

        fun bindDataWithHolder(comModel: ComModel) {
            if (comModel.isPreviewAvailable) visibilityIV.visibility =
                View.VISIBLE else visibilityIV.visibility = View.GONE
        }

        override fun onClick(view: View) {
            clickedPosition = adapterPosition
            if (mClickListener != null) mClickListener!!.onItemClick(view, adapterPosition)
            notifyDataSetChanged()
        }
        
        override fun onLongClick(view: View): Boolean {
            return if (mLongClickListener != null) {
                mLongClickListener!!.onItemLongClick(view, adapterPosition)
                true
            } else {
                false
            }
        }
    }

    // convenience method for getting data at click position
    fun getItem(id: Int): String {
        return mAnimals[id].effectName
    }

    // allows clicks events to be caught
    fun setClickListener(itemClickListener: ItemClickListener?) {
        mClickListener = itemClickListener
    }
    
    // allows long clicks events to be caught
    fun setLongClickListener(itemLongClickListener: ItemLongClickListener?) {
        mLongClickListener = itemLongClickListener
    }

    // parent activity will implement this method to respond to click events
    interface ItemClickListener {
        fun onItemClick(view: View?, position: Int)
    }
    
    // parent activity will implement this method to respond to long click events
    interface ItemLongClickListener {
        fun onItemLongClick(view: View?, position: Int)
    }
}