package com.videomontage.freeeditingapps.adapter

import android.graphics.drawable.Drawable
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.AsyncListDiffer
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.utils.assets.FontItem
import java.io.IOException
import java.io.InputStream

class FontAdapter : RecyclerView.Adapter<FontAdapter.FontViewHolder>() {

    inner class FontViewHolder(itemView: View): RecyclerView.ViewHolder(itemView)

    private val differCallback = object : DiffUtil.ItemCallback<FontItem>() {
        override fun areItemsTheSame(oldItem: FontItem, newItem: FontItem): Boolean {
            return oldItem.fontImage == newItem.fontImage
        }

        override fun areContentsTheSame(oldItem: FontItem, newItem: FontItem): Boolean {
            return oldItem == newItem
        }
    }

    val differ = AsyncListDiffer(this, differCallback)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FontViewHolder {
        return FontViewHolder(
                LayoutInflater.from(parent.context).inflate(
                        R.layout.font_item,
                        parent,
                        false
                )
        )
    }

    override fun getItemCount(): Int {
        return differ.currentList.size
    }

    private var onItemClickListener: ((FontItem) -> Unit)? = null

    override fun onBindViewHolder(holder: FontViewHolder, position: Int) {
        val fontItem = differ.currentList[position]
        holder.itemView.apply {
            val fontIV: ImageView = findViewById(R.id.fontIV)
            // load image
            // load image
            try {
                // get input stream
                val ims: InputStream = context.getAssets().open("fonts/" + fontItem.fontImage)
                // load image as Drawable
                val d: Drawable? = Drawable.createFromStream(ims, null)
                // set image to ImageView
                fontIV.setImageDrawable(d)
            } catch (ex: IOException) {
                Log.d("ddd", "onBindViewHolder: " + ex.message)
                return
            }

            setOnClickListener {
                onItemClickListener?.let { it(fontItem) }
            }
        }
    }

    fun setOnItemClickListener(listener: (FontItem) -> Unit) {
        onItemClickListener = listener
    }

}