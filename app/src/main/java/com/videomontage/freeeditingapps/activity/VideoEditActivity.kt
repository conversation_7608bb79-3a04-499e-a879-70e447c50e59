package com.videomontage.freeeditingapps.activity

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Color
import android.media.MediaMetadataRetriever
import android.media.MediaPlayer
import android.net.Uri
import android.os.AsyncTask
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.text.TextUtils
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
//import com.my.dropbox_lib.data.local.database.model.StickerCache
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.databinding.ActivityVideoEditBinding
import com.videomontage.freeeditingapps.fragment.CustomFragment.CallBackListener
import com.videomontage.freeeditingapps.fragment.StickersDialog
import com.videomontage.freeeditingapps.fragment.viewmodel.StateData
import com.videomontage.freeeditingapps.fragment.viewmodel.StateData.DataStatus
import com.videomontage.freeeditingapps.fragment.viewmodel.StickerDialogViewModel
import com.videomontage.freeeditingapps.model.VideoInfo
import com.videomontage.freeeditingapps.utils.StaticFinalValues
import com.videomontage.freeeditingapps.utils.StatusBarUtil
import com.videomontage.freeeditingapps.utils.filter.GifDecoder
import com.videomontage.freeeditingapps.utils.filter.MagicFilterType
import com.videomontage.freeeditingapps.utils.videoclipper.VideoClipper
import com.videomontage.freeeditingapps.video.MediaPlayerWrapper.IMediaCallback
import com.videomontage.freeeditingapps.view.BaseImageView
import com.videomontage.freeeditingapps.view.BubbleInputDialog
import com.videomontage.freeeditingapps.view.BubbleInputDialog.CompleteCallBack
import com.videomontage.freeeditingapps.view.DynamicImageView
import com.videomontage.freeeditingapps.view.StickInfoImageView
import com.videomontage.freeeditingapps.view.VideoEditView
import com.videomontage.freeeditingapps.view.VideoEditView.OnSelectTimeChangeListener
import com.videomontage.freeeditingapps.view.VideoPreviewView
import com.videomontage.freeeditingapps.view.bubble.BubbleTextView
import com.videomontage.freeeditingapps.view.bubble.PopBubbleEditView
import com.videomontage.freeeditingapps.view.bubble.PopBubbleView
import com.videomontage.freeeditingapps.view.gif.PopPasterView
import com.videomontage.freeeditingapps.view.gif.PopPasterView.PasterSelectListener
import com.videomontage.freeeditingapps.view.gif.StickerView
import com.videomontage.freeeditingapps.view.sticker.BubbleTextView_Third
import com.videomontage.freeeditingapps.view.sticker.PopBubbleEditView_Third
import com.videomontage.freeeditingapps.view.sticker.PopBubbleView_Third
import com.videomontage.freeeditingapps.view.text.PopBubbleEditView_Second
import com.videomontage.freeeditingapps.view.text.PopBubbleView_Second
import com.videomontage.freeeditingapps.view.text.PopBubbleView_Second.TextOverlayProperties
import com.videomontage.freeeditingapps.view.text.TextOverlay
import java.io.IOException
import java.util.concurrent.Executors

class VideoEditActivity() : FragmentActivity(), PopBubbleView.BubbleSelectListener,
    PasterSelectListener, PopBubbleView_Second.BubbleSelectListener,
    PopBubbleView_Third.BubbleSelectListener, IMediaCallback, View.OnTouchListener,
    OnSelectTimeChangeListener, CallBackListener {
    private var _binding: ActivityVideoEditBinding? = null
    private val binding: ActivityVideoEditBinding
        get() = _binding!!


    //    @BindView(R.id.pb_loading)
    //    ProgressBar mPbLoading;
    //    @BindView(R.id.tv_hint)
    //    TextView mTvHint;
    //    @BindView(R.id.pop_video_loading_fl)
    //    FrameLayout mPopVideoLoadingFl;
    private val TAG = VideoEditActivity::class.java.simpleName

    //    @BindView(R.id.iv_back)
    //    ImageView ivBack;
    //    @BindView(R.id.rl_title)
    //    RelativeLayout rlTitle;
    //    @BindView(R.id.ll_edit_seekbar)
    var videoEditView: VideoEditView? = null

    //    @BindView(R.id.ll_select_bar)
    //    LinearLayout llSelectBar;
    //    @BindView(R.id.rl_content_root)
    var mContentRootView: FrameLayout? = null

    //    @BindView(R.id.bigicon_play)
    //    ImageView bigiconPlay;
    //    @BindView(R.id.video_preview)
    var mVideoView: VideoPreviewView? = null

    //    @BindView(R.id.ll_add_filter)
    //    TextView mLlAddFilterTv;
    //    @BindView(R.id.pop_video_percent_tv)
    //    TextView mPopVideoPercentTv;
    //当前处于编辑状态的贴纸
    private var mCurrentView: StickerView? = null

    //当前处于编辑状态的气泡
    private var mCurrentEditTextView: BubbleTextView? = null
    private var mCurrentEditTextView_second: TextOverlay? = null
    private var mCurrentEditTextView_third: BubbleTextView_Third? = null

    //存储贴纸列表
    private val mViews: ArrayList<BaseImageView?>? = ArrayList()

    //气泡输入框
    private var mBubbleInputDialog: BubbleInputDialog? = null
    private val stickerViews = ArrayList<StickInfoImageView>()
    private val dynamicImageViews = ArrayList<DynamicImageView>()
    private var popBubbleView: PopBubbleView? = null
    private var popPasterView: PopPasterView? = null
    private var popBubbleView_second: PopBubbleView_Second? = null
    private val popBubbleView_third: PopBubbleView_Third? = null
    private val bubbleArray = intArrayOf(
        R.drawable.bubbleone,
        R.drawable.bubbletwo,
        R.drawable.bubblethree,
        R.drawable.bubblefour,
        R.drawable.bubblefive,
        R.drawable.bubblesix,
        R.drawable.bubbleseven,
        R.drawable.bubbleeight
    )
    private val dropboxList: ArrayList<String>? = null
    private var mVideoPath: String? = "/storage/emulated/0/ych/1234.mp4"
    var mVideoHeight = 0
    var mVideoWidth = 0
    var mVideoDuration = 0 //mIsNotComeLocal 1表示拍摄,mIsAnswer 1表示回答者
    private var mContext: Context? = null
    private var hasSelectStickerView = false
    private var mPixel = 1.778f
    private var lastTime: Long = 0
    private val isVideoPause = false
    private var popBubbleEditView: PopBubbleEditView? = null
    private var popBubbleEditView_second: PopBubbleEditView_Second? = null
    private var popBubbleEditView_third: PopBubbleEditView_Third? = null
    private var currentTime: Long = 0
    private var isPlayVideo = false
    var mVideoRotation: String? = "90"
    private var stickerDialogViewModel: StickerDialogViewModel? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivityVideoEditBinding.inflate(
            layoutInflater
        )
        setContentView(binding.root)
        videoEditView = binding.llEditSeekbar
        mContentRootView = binding.rlContentRoot.root
        mContentRootView!!.setOnClickListener { view: View -> onViewClicked(view) }
        binding.llAddSticker.setOnClickListener { view: View -> onViewClicked(view) }
        binding.llAddSubtitle.setOnClickListener { view: View -> onViewClicked(view) }
        binding.llAddText.setOnClickListener { view: View -> onViewClicked(view) }
        binding.ivBack.setOnClickListener { view: View -> onViewClicked(view) }
        binding.llEditSeekbar.setPlayButtonClickListener {
            onViewClicked(it)
        }

        mVideoView = binding.rlContentRoot.videoPreview
        stickerDialogViewModel = ViewModelProvider(this).get(
            StickerDialogViewModel::class.java
        )
//        stickerDialogViewModel!!.getStickers(this)
        mContext = this
        mBubbleInputDialog = BubbleInputDialog(this)
        mBubbleInputDialog!!.setCompleteCallBack(CompleteCallBack { bubbleTextView, str ->
            (bubbleTextView as BubbleTextView).setText(
                str
            )
        })
        initData()
        initListener()
        mThumbBitmap!!.clear()
        StatusBarUtil.transparencyBar(this)
//        initDropBox()


//        Map<String, Integer> map = VideoMetadata.INSTANCE.initVideoInfo(mVideoPath);
//
//        Log.d(TAG, "onCreate: rotation " + map.get(VideoMetadata.VIDEO_ROTATION));
//        Log.d(TAG, "onCreate: width " + map.get(VideoMetadata.VIDEO_WIDTH));
//        Log.d(TAG, "onCreate: height " + map.get(VideoMetadata.VIDEO_HEIGHT));
//        Log.d(TAG, "onCreate: layoutRotation " + mVideoRotation);


//        stickerDialogViewModel.getStickerGroups(this);

//        stickerDialogViewModel.getStateListOfStickerGroupCache().observe(this, stickerGroupCaches -> Log.d(TAG, "onChanged: "  + stickerGroupCaches));
    }

//    private fun initDropBox() {
//        val stickerBtn = findViewById<ImageView>(R.id.llAdSticker)
//        stickerDialogViewModel!!.stateListOfStickers.observe(
//            this
//        ) { lists ->
//            lists?.let { nonNullLists ->
//                when (nonNullLists.status) {
//                    DataStatus.SUCCESS -> {
//                        Log.d(TAG, "initDropBox SUCCESS: ")
//                        stickerBtn.isEnabled = true
//                        stickerBtn.clearColorFilter()
//                    }
//
//                    DataStatus.ERROR -> Log.d(TAG, "initDropBox ERROR: ")
//                    DataStatus.LOADING -> {
//                        Log.d(TAG, "initDropBox LOADING: ")
//                        stickerBtn.isEnabled = false
//                        stickerBtn.setColorFilter(R.color.dim_color)
//                    }
//                }
//            }
//        }
//    }

    private fun initData() {
        mVideoPath = intent.getStringExtra(StaticFinalValues.VIDEOFILEPATH)
        initThumbs()
        val srcList = ArrayList<String?>()
        srcList.add(mVideoPath)
        mVideoView!!.setVideoPath(srcList)
        initSetParam()
    }

    private fun initListener() {
//        mVideoView.setOnFilterChangeListener(this);
        mVideoView!!.setIMediaCallback(this)
        binding.llEditSeekbar.setOnSelectTimeChangeListener(this)
    }

    private fun initSetParam() {
        val layoutParams = mContentRootView!!.layoutParams
        val layoutParams1 = mVideoView!!.layoutParams
        if (!TextUtils.isEmpty(mVideoRotation) && (mVideoRotation == "0") && (mVideoWidth > mVideoHeight) || (mVideoRotation == "180") && mVideoWidth > mVideoHeight) { //本地视频横屏
            layoutParams.width = 1120
            layoutParams.height = 630
            layoutParams1.width = 1120
            layoutParams1.height = 630
        } else {
//            layoutParams.width = (int) (mVideoWidth * StaticFinalValues.VIDEO_WIDTH_HEIGHT);
            layoutParams.width = 630
            layoutParams.height = 1120
            layoutParams1.width = 630
            layoutParams1.height = 1120
        }
        mContentRootView!!.layoutParams = layoutParams
        mVideoView!!.layoutParams = layoutParams1
    }

    var mFilterSel = 0
    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        if (binding.popVideoLoadingFl.root.visibility == View.VISIBLE) {
            Log.e(TAG, "dispatchTouchEvent: ")
            return true
        } else {
            return super.dispatchTouchEvent(ev)
        }
    }

    private fun showStickersDialog(fragmentManager: FragmentManager) {
        val ft = fragmentManager.beginTransaction()
        val prev = fragmentManager.findFragmentByTag("dialog")
        if (prev != null) {
            ft.remove(prev)
        }
        ft.addToBackStack(null)

        // Create and show the dialog.
        val dialog = StickersDialog()
        dialog.setStyle(DialogFragment.STYLE_NORMAL, R.style.Dialog_NoTitle)
        dialog.show(ft, "dialog")
    }

    //    @OnClick({R.id.rl_content_root, R.id.iv_back, R.id.ll_add_sticker, R.id.ll_add_subtitle, R.id.edit_video_next_tv, R.id.ll_play_video, R.id.llAddText, R.id.llAdSticker})
    private fun onViewClicked(view: View) {
        if (System.currentTimeMillis() - lastTime < 500 || binding.popVideoLoadingFl.root.visibility == View.VISIBLE) {
            return
        }
        lastTime = System.currentTimeMillis()
        when (view.id) {
            R.id.llAdSticker -> {
                logMessage(Throwable().stackTrace[0].lineNumber.toString() + " R.id.llAdSticker")
                showStickersDialog(supportFragmentManager)

//                if (popBubbleView_third == null) {
//                    logMessage(new Throwable().getStackTrace()[0].getLineNumber() + " llAdSticker == null");
//
//
//                    popBubbleView_third = new PopBubbleView_Third(VideoEditActivity.this, VideoEditActivity.this.getSupportFragmentManager());
//                    popBubbleView_third.setBubbleSelectListener(VideoEditActivity.this);
//                }
                if (isPlayVideo) {
                    videoPlay()
                }
            }

            R.id.llAddText -> {
                logMessage(Throwable().stackTrace[0].lineNumber.toString() + " R.id.llAddText")
                if (popBubbleView_second == null) {
                    logMessage(Throwable().stackTrace[0].lineNumber.toString() + " llAddText == null")
                    popBubbleView_second = PopBubbleView_Second(this@VideoEditActivity)

//                    popBubbleView_second.setBubbleSelectListener(VideoEditActivity.this);
                    popBubbleView_second!!.setOnItemClickListener { textOverlayProperties: TextOverlayProperties ->
                        proccesFontItemCLicked(
                            textOverlayProperties
                        )
                    }
                }
                if (isPlayVideo) {
                    videoPlay()
                }
                popBubbleView_second!!.show()
            }

            R.id.rl_content_root -> {
                logMessage(Throwable().stackTrace[0].lineNumber.toString() + " R.id.rl_content_root")
                if (mCurrentEditTextView != null) {
                    mCurrentEditTextView!!.isInEdit = false
                }
                if (mCurrentEditTextView_second != null) {
                    mCurrentEditTextView_second!!.setInEdit(false)
                }
                if (mCurrentEditTextView_third != null) {
                    mCurrentEditTextView_third!!.isInEdit = false
                }
                if (mCurrentView != null) {
                    mCurrentView!!.setInEdit(false)
                }
            }

            R.id.iv_back -> {
                logMessage(Throwable().stackTrace[0].lineNumber.toString() + " R.id.iv_back")
                onBackPressed()
            }

            R.id.ll_add_sticker -> {
                logMessage(Throwable().stackTrace[0].lineNumber.toString() + " R.id.ll_add_sticker")
                if (popPasterView == null) {
                    popPasterView = PopPasterView(this@VideoEditActivity)
                    popPasterView!!.setPasterSelectListener(this@VideoEditActivity)
                }
                if (isPlayVideo) {
                    videoPlay()
                }
                popPasterView!!.show()
            }

            R.id.ll_add_subtitle -> {
                logMessage(Throwable().stackTrace[0].lineNumber.toString() + " R.id.ll_add_subtitle")
                if (popBubbleView == null) {
                    logMessage(Throwable().stackTrace[0].lineNumber.toString() + " popBubbleView == null")
                    popBubbleView = PopBubbleView(this@VideoEditActivity)
                    popBubbleView!!.setBubbleSelectListener(this@VideoEditActivity)
                }
                if (isPlayVideo) {
                    videoPlay()
                }
                popBubbleView!!.show()
            }

            R.id.edit_video_next_tv -> {
                logMessage(Throwable().stackTrace[0].lineNumber.toString() + " R.id.edit_video_next_tv")
                videoEditView!!.recoverView()
                if (mCurrentEditTextView != null) {
                    mCurrentEditTextView!!.isInEdit = false
                }
                if (mCurrentView != null) {
                    mCurrentView!!.setInEdit(false)
                }
                mVideoView!!.pause()
                val clipper = VideoClipper(this)
                clipper.setInputVideoPath(mVideoPath)
                val outputPath = StaticFinalValues.STORAGE_TEMP_VIDEO_PATH
                clipper.setOutputVideoPath(outputPath)
                clipper.setOnVideoCutFinishListener(object : VideoClipper.OnVideoCutFinishListener {
                    override fun onFinish() {
//                            VideoViewPlayerActivity.launch(VideoEditActivity.this,outputPath);
                        mHandler.sendEmptyMessage(VIDEO_CUT_FINISH)
                    }

                    override fun onProgress(percent: Float) {
                        val message = Message()
                        message.what = CLIP_VIDEO_PERCENT
                        message.obj = percent
                        mHandler.sendMessage(message)
                    }
                })
                try {
                    val clipDur = mVideoView!!.videoDuration * 1000
                    clipper.clipVideo((mVideoRotation == "90"), mViews?.filterNotNull()?.let { ArrayList(it) } ?: arrayListOf(), resources)

//                    progressDialog = new PopupManager(mContext).showLoading();
                    binding.popVideoLoadingFl.root.visibility = View.VISIBLE
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }

            R.id.ll_play_video -> {
                logMessage(Throwable().stackTrace[0].lineNumber.toString() + " R.id.ll_play_video")
                videoPlay()
            }
        }
    }

    private fun proccesFontItemCLicked(textOverlayProperties: TextOverlayProperties): Int {
        Toast.makeText(mContext, textOverlayProperties.fontItem.fontFile, Toast.LENGTH_SHORT).show()
        addText(0, textOverlayProperties)
        return 0
    }

    private fun videoPlay() {
        Log.e(TAG, "currentTime:$currentTime,mVideoDuration:$mVideoDuration")
        if (currentTime >= mVideoDuration) {
            return
        }
        for (stickInfoImageView: StickInfoImageView in stickerViews) {  //清空gif图
            mContentRootView!!.removeView(stickInfoImageView)
        }
        stickerViews.clear()
        for (baseImageView: BaseImageView? in mViews!!) {
            baseImageView!!.visibility = View.GONE
            //            addGifView(baseImageView);
        }
        videoEditView!!.videoPlay(mViews)
    }

    //添加表情
    private fun addStickerView(resourceId: Int, gifId: Int) {
        if (mViews!!.size >= 40) {
            Toast.makeText(
                this@VideoEditActivity,
                "The number of subtitles and stickers cannot exceed 40",
                Toast.LENGTH_SHORT
            ).show()
            return
        }
        if ((mVideoDuration - currentTime) / 1000 < 2) {
            Toast.makeText(
                this@VideoEditActivity,
                "Not enough time to add stickers",
                Toast.LENGTH_SHORT
            ).show()
            return
        }
        hasSelectStickerView = true
        val stickerView = StickerView(this)
        //        stickerView.setImageResource(R.drawable.ic_cat);
        stickerView.setParentSize(
            mContentRootView!!.measuredWidth,
            mContentRootView!!.measuredHeight
        )
        val gifDecoder = GifDecoder()
        gifDecoder.read(resources.openRawResource(gifId))
        val bitmaps: MutableList<Bitmap> = ArrayList()
        for (i in 0 until gifDecoder.frameCount) {
            bitmaps.add(gifDecoder.getFrame(i))
        }
        stickerView.bitmaps = bitmaps
        //        stickerView.setImageResource(resourceId);
        stickerView.bitmap = bitmaps.get(bitmaps.size / 2)
        stickerView.isGif = true
        stickerView.gifId = gifId
        stickerView.setOperationListener(object : StickerView.OperationListener {
            override fun onDeleteClick() {
                mViews.remove(stickerView)
                mContentRootView!!.removeView(stickerView)
            }

            override fun onEdit(stickerView: StickerView) {
                Log.e(TAG, "StickerView onEdit")
                hasSelectStickerView = true
                setCurrentEdit(stickerView)
                if (mViews != null && mViews.size > 0) {
                    val position: Int
                    position = mViews.indexOf(mCurrentView)
                    if (position != -1) {
                        Log.d(TAG, "Rotate degree: " + mCurrentView!!.rotateDegree)
                        mViews.get(position)!!.rotateDegree = mCurrentView!!.rotateDegree
                        mViews.get(position)!!.viewHeight = mCurrentView!!.viewHeight
                        mViews.get(position)!!.viewWidth = mCurrentView!!.viewWidth
                        mViews.get(position)!!.x = mCurrentView!!.x
                        mViews.get(position)!!.y = mCurrentView!!.y
                    }
                }
                videoEditView!!.recoverView(mViews, stickerView, true)
                if (isPlayVideo) {   //如果已经处于播放状态，则暂停播放
                    videoEditView!!.videoPlay(mViews)
                }
            }

            override fun onTop(stickerView: StickerView) {
                Log.e(TAG, "StickerView onTop")
                val position = mViews.indexOf(stickerView)
                if (position == mViews.size - 1) {
                    return
                }
                val stickerTemp = mViews.removeAt(position) as StickerView?
                mViews.add(mViews.size, stickerTemp)
            }
        })
        val lp = RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.MATCH_PARENT,
            RelativeLayout.LayoutParams.MATCH_PARENT
        )
        mContentRootView!!.addView(stickerView, lp)
        Log.e(TAG, " 初始位置,X=" + stickerView.posX + (stickerView.bitmap.width / 2))
        Log.e(TAG, " 初始位置,Y=" + stickerView.posY + (stickerView.bitmap.height / 2))
        stickerView.x = stickerView.posX + stickerView.bitmap.width / 2
        stickerView.y = stickerView.posY + stickerView.bitmap.height / 2
        stickerView.startTime = currentTime
        var endTime = currentTime + 2000
        if (endTime > mVideoDuration) {
            endTime = mVideoDuration.toLong()
        }
        stickerView.endTime = endTime
        stickerView.timeStamp = System.currentTimeMillis()
        mViews.add(stickerView)
        setCurrentEdit(stickerView)
        videoEditView!!.recoverView(mViews, stickerView, false)
    }

    private fun addSticker(index: Int, bitmap: Bitmap?) {
        if (mViews!!.size >= 40) {
            Toast.makeText(
                this@VideoEditActivity,
                resources.getString(R.string.cannot_exceed_40),
                Toast.LENGTH_SHORT
            ).show()
            return
        }
        if ((mVideoDuration - currentTime) / 1000 < 2) {
            Toast.makeText(
                this@VideoEditActivity,
                resources.getString(R.string.current_time_not_enough),
                Toast.LENGTH_SHORT
            ).show()
            return
        }
        hasSelectStickerView = false
        val bubbleTextView = BubbleTextView_Third(
            this,
            Color.BLACK, 0, index
        )
        //        bubbleTextView.setImageResource(R.drawable.bubble_7_rb);
        bubbleTextView.setParentSize(
            mContentRootView!!.measuredWidth,
            mContentRootView!!.measuredHeight
        )
        if (bitmap != null) bubbleTextView.bitmap = bitmap else bubbleTextView.setImageResource(
            bubbleArray[index]
        )
        //        bubbleTextView.setImageResource(R.drawable.blank);
        bubbleTextView.isGif = false
        bubbleTextView.setOperationListener(object : BubbleTextView_Third.OperationListener {
            override fun onDeleteClick() {
                Log.e(TAG, "BubbleTextView onDeleteClick")
                mViews.remove(bubbleTextView)
                mContentRootView!!.removeView(bubbleTextView)
            }

            override fun onEdit(bubbleTextView: BubbleTextView_Third) {
                Log.e(TAG, "BubbleTextView onEdit")
                hasSelectStickerView = false
                setCurrentEdit(bubbleTextView)
                if (mViews != null && mViews.size > 0) {
                    val position: Int
                    position = mViews.indexOf(mCurrentEditTextView_third)
                    Log.d(TAG, "onEdit: index $position")
                    if (position != -1) {
                        Log.d(TAG, "Position X: " + mCurrentEditTextView_third!!.x)
                        Log.d(TAG, "Position Y: " + mCurrentEditTextView_third!!.y)
                        mViews.get(position)!!.rotateDegree =
                            mCurrentEditTextView_third!!.rotateDegree
                        mViews.get(position)!!.viewHeight = mCurrentEditTextView_third!!.viewHeight
                        mViews.get(position)!!.viewWidth = mCurrentEditTextView_third!!.viewWidth
                        mViews.get(position)!!.x = mCurrentEditTextView_third!!.x
                        mViews.get(position)!!.y = mCurrentEditTextView_third!!.y
                    }
                }
                videoEditView!!.recoverView(mViews, bubbleTextView, true)
                if (isPlayVideo) {   //如果已经处于播放状态，则暂停播放
                    videoEditView!!.videoPlay(mViews)
                }
            }

            override fun onClick(bubbleTextView: BubbleTextView_Third) {
//                mBubbleInputDialog.setBubbleTextView(bubbleTextView);
//                mBubbleInputDialog.show();
                if (popBubbleEditView_third == null) {
                    popBubbleEditView_third = PopBubbleEditView_Third(this@VideoEditActivity)
                    popBubbleEditView_third!!.setOnTextSendListener(object :
                        PopBubbleEditView_Third.OnTextSendListener {
                        override fun onTextSend(text: String) {
                            mCurrentEditTextView_third!!.setText(text)
                        }
                    })
                }
                popBubbleEditView_third!!.show(bubbleTextView.getmStr())
            }

            override fun onTop(bubbleTextView: BubbleTextView_Third) {
                val position = mViews.indexOf(bubbleTextView)
                if (position == mViews.size - 1) {
                    return
                }
                val textView = mViews.removeAt(position) as TextOverlay?
                mViews.add(mViews.size, textView)
            }
        })
        val lp = RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.MATCH_PARENT,
            RelativeLayout.LayoutParams.MATCH_PARENT
        )
        mContentRootView!!.addView(bubbleTextView, lp)
        bubbleTextView.startTime = currentTime
        var endTime = currentTime + 2000
        if (endTime > mVideoDuration) {
            endTime = mVideoDuration.toLong()
        }
        bubbleTextView.endTime = endTime
        bubbleTextView.timeStamp = System.currentTimeMillis()
        mViews.add(bubbleTextView)
        setCurrentEdit(bubbleTextView)
        videoEditView!!.recoverView(mViews, bubbleTextView, false)
    }

    private fun addText(index: Int, textOverlayProperties: TextOverlayProperties?) {
        if (mViews!!.size >= 40) {
            Toast.makeText(
                this@VideoEditActivity,
                resources.getString(R.string.cannot_exceed_40),
                Toast.LENGTH_SHORT
            ).show()
            return
        }
        if ((mVideoDuration - currentTime) / 1000 < 2) {
            Toast.makeText(
                this@VideoEditActivity,
                resources.getString(R.string.current_time_not_enough),
                Toast.LENGTH_SHORT
            ).show()
            return
        }
        hasSelectStickerView = false
        val bubbleTextView = TextOverlay(
            this,
            Color.BLACK, 0, index, textOverlayProperties!!.fontItem.fontFile
        )
        bubbleTextView.setFontColor(Color.parseColor(textOverlayProperties.textColor))
        //        bubbleTextView.setImageResource(R.drawable.bubble_7_rb);
        bubbleTextView.setParentSize(
            mContentRootView!!.measuredWidth,
            mContentRootView!!.measuredHeight
        )
        //        bubbleTextView.setImageResource(bubbleArray[index]);
        bubbleTextView.setImageResource(R.drawable.blank)
        bubbleTextView.isGif = false
        //        bubbleTextView.setmFontSize(30);
        bubbleTextView.setOperationListener(object : TextOverlay.OperationListener {
            override fun onDeleteClick() {
                Log.e(TAG, "BubbleTextView onDeleteClick")
                mViews.remove(bubbleTextView)
                mContentRootView!!.removeView(bubbleTextView)
            }

            override fun onEdit(bubbleTextView: TextOverlay) {
                Log.e(TAG, "BubbleTextView onEdit")
                hasSelectStickerView = false
                //                if (mCurrentView != null) {
//                    mCurrentView.setInEdit(false);
//                }
//                setOtherViewsEditOff();
//                mCurrentEditTextView_second.setInEdit(false);
//                mCurrentEditTextView_second = bubbleTextView;
//                mCurrentEditTextView_second.setInEdit(true);
                setCurrentEdit(bubbleTextView)
                if (mViews != null && mViews.size > 0) {
                    val position: Int
                    position = mViews.indexOf(mCurrentEditTextView_second)
                    if (position != -1) {
                        Log.d(TAG, "Position X: " + mCurrentEditTextView_second!!.x)
                        Log.d(TAG, "Position Y: " + mCurrentEditTextView_second!!.y)
                        mViews.get(position)!!.rotateDegree =
                            mCurrentEditTextView_second!!.rotateDegree
                        mViews.get(position)!!.viewHeight = mCurrentEditTextView_second!!.viewHeight
                        mViews.get(position)!!.viewWidth = mCurrentEditTextView_second!!.viewWidth
                        mViews.get(position)!!.x = mCurrentEditTextView_second!!.x
                        mViews.get(position)!!.y = mCurrentEditTextView_second!!.y
                    }
                }
                videoEditView!!.recoverView(mViews, bubbleTextView, true)
                if (isPlayVideo) {   //如果已经处于播放状态，则暂停播放
                    videoEditView!!.videoPlay(mViews)
                }
            }

            override fun onClick(bubbleTextView: TextOverlay) {
//                mBubbleInputDialog.setBubbleTextView(bubbleTextView);
//                mBubbleInputDialog.show();
                if (popBubbleEditView_second == null) {
                    popBubbleEditView_second = PopBubbleEditView_Second(this@VideoEditActivity)
                    popBubbleEditView_second!!.setOnTextSendListener(object :
                        PopBubbleEditView_Second.OnTextSendListener {
                        override fun onTextSend(text: String) {
                            mCurrentEditTextView_second!!.setText(text)
                        }
                    })
                }
                popBubbleEditView_second!!.show(bubbleTextView.getmStr())
            }

            override fun onTop(bubbleTextView: TextOverlay) {
                val position = mViews.indexOf(bubbleTextView)
                if (position == mViews.size - 1) {
                    return
                }
                val textView = mViews.removeAt(position) as TextOverlay?
                mViews.add(mViews.size, textView)
            }
        })
        val lp = RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.MATCH_PARENT,
            RelativeLayout.LayoutParams.MATCH_PARENT
        )
        mContentRootView!!.addView(bubbleTextView, lp)
        bubbleTextView.startTime = currentTime
        var endTime = currentTime + 2000
        if (endTime > mVideoDuration) {
            endTime = mVideoDuration.toLong()
        }
        bubbleTextView.endTime = endTime
        bubbleTextView.timeStamp = System.currentTimeMillis()
        mViews.add(bubbleTextView)
        setCurrentEdit(bubbleTextView)
        videoEditView!!.recoverView(mViews, bubbleTextView, false)

//        new Handler().postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                bubbleTextView.setFontColor(R.color.white);
//                Toast.makeText(mContext, "asdfsadf", Toast.LENGTH_SHORT).show();
//            }
//        }, 3000);
    }

    //添加气泡
    private fun addBubble(index: Int) {
        if (mViews!!.size >= 40) {
            Toast.makeText(
                this@VideoEditActivity,
                resources.getString(R.string.cannot_exceed_40),
                Toast.LENGTH_SHORT
            ).show()
            return
        }
        if ((mVideoDuration - currentTime) / 1000 < 2) {
            Toast.makeText(
                this@VideoEditActivity,
                resources.getString(R.string.current_time_not_enough),
                Toast.LENGTH_SHORT
            ).show()
            return
        }
        hasSelectStickerView = false
        val bubbleTextView = BubbleTextView(
            this,
            Color.BLACK, 0, index
        )
        //        bubbleTextView.setImageResource(R.drawable.bubble_7_rb);
        bubbleTextView.setParentSize(
            mContentRootView!!.measuredWidth,
            mContentRootView!!.measuredHeight
        )
        bubbleTextView.setImageResource(bubbleArray[index])
        bubbleTextView.isGif = false
        bubbleTextView.setOperationListener(object : BubbleTextView.OperationListener {
            override fun onDeleteClick() {
                Log.e(TAG, "BubbleTextView onDeleteClick")
                mViews.remove(bubbleTextView)
                mContentRootView!!.removeView(bubbleTextView)
            }

            override fun onEdit(bubbleTextView: BubbleTextView) {
                Log.e(TAG, "BubbleTextView onEdit")
                hasSelectStickerView = false
                //                if (mCurrentView != null) {
//                    mCurrentView.setInEdit(false);
//                }
//                mCurrentEditTextView.setInEdit(false);
//                mCurrentEditTextView = bubbleTextView;
//                mCurrentEditTextView.setInEdit(true);
                setCurrentEdit(bubbleTextView)
                if (mViews != null && mViews.size > 0) {
                    val position: Int
                    position = mViews.indexOf(mCurrentEditTextView)
                    if (position != -1) {
                        Log.d(TAG, "Position X: " + mCurrentEditTextView!!.x)
                        Log.d(TAG, "Position Y: " + mCurrentEditTextView!!.y)
                        mViews.get(position)!!.rotateDegree = mCurrentEditTextView!!.rotateDegree
                        mViews.get(position)!!.viewHeight = mCurrentEditTextView!!.viewHeight
                        mViews.get(position)!!.viewWidth = mCurrentEditTextView!!.viewWidth
                        mViews.get(position)!!.x = mCurrentEditTextView!!.x
                        mViews.get(position)!!.y = mCurrentEditTextView!!.y
                    }
                }
                videoEditView!!.recoverView(mViews, bubbleTextView, true)
                if (isPlayVideo) {   //如果已经处于播放状态，则暂停播放
                    videoEditView!!.videoPlay(mViews)
                }
            }

            override fun onClick(bubbleTextView: BubbleTextView) {
//                mBubbleInputDialog.setBubbleTextView(bubbleTextView);
//                mBubbleInputDialog.show();
                if (popBubbleEditView == null) {
                    popBubbleEditView = PopBubbleEditView(this@VideoEditActivity)
                    popBubbleEditView!!.setOnTextSendListener(object :
                        PopBubbleEditView.OnTextSendListener {
                        override fun onTextSend(text: String) {
                            mCurrentEditTextView!!.setText(text)
                        }
                    })
                }
                popBubbleEditView!!.show(bubbleTextView.getmStr())
            }

            override fun onTop(bubbleTextView: BubbleTextView) {
                val position = mViews.indexOf(bubbleTextView)
                if (position == mViews.size - 1) {
                    return
                }
                val textView = mViews.removeAt(position) as BubbleTextView?
                mViews.add(mViews.size, textView)
            }
        })
        val lp = RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.MATCH_PARENT,
            RelativeLayout.LayoutParams.MATCH_PARENT
        )
        mContentRootView!!.addView(bubbleTextView, lp)
        bubbleTextView.startTime = currentTime
        var endTime = currentTime + 2000
        if (endTime > mVideoDuration) {
            endTime = mVideoDuration.toLong()
        }
        bubbleTextView.endTime = endTime
        bubbleTextView.timeStamp = System.currentTimeMillis()
        mViews.add(bubbleTextView)
        setCurrentEdit(bubbleTextView)
        videoEditView!!.recoverView(mViews, bubbleTextView, false)
    }

    private fun setCurrentEdit(stickerView: BubbleTextView_Third) {
        Log.d(TAG, "setCurrentEdit: ")

//        if (mCurrentView != null) {
//            mCurrentView.setInEdit(false);
//        }
//        if (mCurrentEditTextView_third != null) {
//            mCurrentEditTextView_third.setInEdit(false);
//        }
        setOtherViewsEditOff()
        mCurrentEditTextView_third = stickerView
        mCurrentEditTextView_third!!.isInEdit = true
    }

    private fun setCurrentEdit(stickerView: TextOverlay) {
        Log.d(TAG, "setCurrentEdit: ")

//        if (mCurrentView != null) {
//            mCurrentView.setInEdit(false);
//        }
        setOtherViewsEditOff()
        //        if (mCurrentEditTextView_second != null) {
//            mCurrentEditTextView_second.setInEdit(false);
//        }
        mCurrentEditTextView_second = stickerView
        mCurrentEditTextView_second!!.setInEdit(true)
    }

    /**
     * 设置当前处于编辑模式的贴纸
     */
    private fun setCurrentEdit(stickerView: StickerView) {
        Log.d(TAG, "setCurrentEdit: ")

//        if (mCurrentView != null) {
//            mCurrentView.setInEdit(false);
//        }
//        if (mCurrentEditTextView != null) {
//            mCurrentEditTextView.setInEdit(false);
//        }
        setOtherViewsEditOff()
        mCurrentView = stickerView
        stickerView.setInEdit(true)
    }

    /**
     * 设置当前处于编辑模式的气泡
     */
    private fun setCurrentEdit(bubbleTextView: BubbleTextView) {
        Log.d(TAG, "setCurrentEdit: ")
        //        if (mCurrentView != null) {
//            mCurrentView.setInEdit(false);
//        }
//        if (mCurrentEditTextView != null) {
//            mCurrentEditTextView.setInEdit(false);
//        }
        setOtherViewsEditOff()
        mCurrentEditTextView = bubbleTextView
        mCurrentEditTextView!!.isInEdit = true
    }

    private fun setOtherViewsEditOff() {
        for (i in mViews!!.indices) {
            mViews[i]!!.setInEdit(false)
        }
    }

    //字幕选择接口回调
    override fun bubbleSelect(bubbleIndex: Int) {
        addBubble(bubbleIndex)
    }

    override fun pasterSelect(resourceId: Int, gifId: Int) {
        addStickerView(resourceId, gifId)
    }

    override fun texterSelect(resourceId: Int) {
        addText(resourceId, null)
    }

    override fun stickerSelect(stickerIndex: Int) {
        addSticker(stickerIndex, null)
    }

    override fun onCallBack(bitmap: Bitmap) {
        addSticker(0, bitmap)
    }

    //视频播放接口
    private var isDestroy = false
    private var isPlaying = false
    private val filterType = MagicFilterType.NONE
    var mHandler: Handler = object : Handler() {
        override fun handleMessage(msg: Message) {
            when (msg.what) {
                CLIP_VIDEO_PERCENT -> {
                    logMessage(Throwable().stackTrace[0].lineNumber.toString() + " CLIP_VIDEO_PERCENT")
                    val aFloat = msg.obj as Float
                    binding.popVideoLoadingFl.popVideoPercentTv.setText(((aFloat * 100).toInt()).toString() + "%")
                }

                VIDEO_PREPARE -> {
                    logMessage(Throwable().stackTrace[0].lineNumber.toString() + "V IDEO_PREPARE")
                    Executors.newSingleThreadExecutor().execute(update)
                    sendEmptyMessageDelayed(AUTO_PAUSE, 50)
                }

                VIDEO_START -> {
                    logMessage(Throwable().stackTrace[0].lineNumber.toString() + " VIDEO_START")
                    isPlaying = true
                }

                VIDEO_UPDATE -> logMessage(Throwable().stackTrace[0].lineNumber.toString() + " VIDEO_UPDATE")
                VIDEO_PAUSE -> {
                    logMessage(Throwable().stackTrace[0].lineNumber.toString() + " VIDEO_PAUSE")
                    isPlaying = false
                }

                VIDEO_CUT_FINISH -> {
                    logMessage(Throwable().stackTrace[0].lineNumber.toString() + " VIDEO_CUT_FINISH")
                    binding.popVideoLoadingFl.popVideoPercentTv.setText("0%")
                    binding.popVideoLoadingFl.getRoot().setVisibility(View.GONE)
                    startActivity(Intent(baseContext, MainActivity::class.java))
                }

                AUTO_PAUSE -> {
                    logMessage(Throwable().stackTrace[0].lineNumber.toString() + " AUTO_PAUSE")
                    mVideoView!!.pause()
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        logMessage(Throwable().stackTrace[0].lineNumber.toString() + " onResume ")

//        mVideoView.start();
    }

    override fun onPause() {
        super.onPause()
        if (mVideoView != null) {
            mVideoView!!.pause()
        }
        if (isPlayVideo) {
            videoPlay()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mHandler.removeCallbacksAndMessages(null)
        isDestroy = true
        mVideoView!!.onDestroy()
        if (mThumbBitmap != null) {
            for (i in mThumbBitmap!!.indices) {
                mThumbBitmap!![i].recycle()
            }
            mThumbBitmap = null
        }
        System.gc()
    }

    private val update: Runnable = object : Runnable {
        override fun run() {
            while (!isDestroy) {
                if (!isPlaying) {
                    try {
                        Thread.sleep(200)
                    } catch (e: InterruptedException) {
                        e.printStackTrace()
                    }
                    continue
                }
                mHandler.sendEmptyMessage(VIDEO_UPDATE)
                try {
                    Thread.sleep(200)
                } catch (e: InterruptedException) {
                    e.printStackTrace()
                }
            }
        }
    }

    override fun onTouch(v: View, event: MotionEvent): Boolean {
        return false
    }

    override fun onVideoPrepare() {
        logMessage(Throwable().stackTrace[0].lineNumber.toString() + " onVideoPrepare")
        mHandler.sendEmptyMessage(VIDEO_PREPARE)
    }

    override fun onVideoStart() {
        logMessage(Throwable().stackTrace[0].lineNumber.toString() + " onVideoStart")
        mHandler.sendEmptyMessage(VIDEO_START)
    }

    override fun onVideoPause() {
        logMessage(Throwable().stackTrace[0].lineNumber.toString() + " onVideoPause")
        mHandler.sendEmptyMessage(VIDEO_PAUSE)
    }

    override fun onCompletion(mp: MediaPlayer) {
        /*mVideoView.seekTo(0);
        mVideoView.start();*/
        logMessage(Throwable().stackTrace[0].lineNumber.toString() + " onCompletion")
    }

    override fun onVideoChanged(info: VideoInfo) {
        logMessage(Throwable().stackTrace[0].lineNumber.toString() + " onVideoChanged")
    }
    //    @Override
    //    public void onFilterChange(MagicFilterType type) {
    //        logMessage(new Throwable().getStackTrace()[0].getLineNumber() + " onFilterChange");
    //
    //        this.filterType = type;
    //    }
    /**
     * 初始化缩略图
     */
    private fun initThumbs() {
        logMessage(Throwable().stackTrace[0].lineNumber.toString() + " selectTimeChange")
        val mediaMetadata = MediaMetadataRetriever()
        mediaMetadata.setDataSource(mContext, Uri.parse(mVideoPath))
        try {
            mVideoRotation =
                mediaMetadata.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION)
            mVideoWidth =
                mediaMetadata.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)!!
                    .toInt()
            mVideoHeight =
                mediaMetadata.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)!!
                    .toInt()
        } catch (e: NumberFormatException) {
            e.printStackTrace()
            finish()
        }
        mPixel = mVideoHeight.toFloat() / mVideoWidth.toFloat()
        mVideoDuration =
            mediaMetadata.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)!!
                .toInt()
        Log.e(TAG, "mVideoDuration:$mVideoDuration")
        videoEditView!!.setTotalTime(mVideoDuration + 100)
        val frame = mVideoDuration / (2 * 1000)
        Log.e(TAG, "frame:$frame")
        val frameTime: Int
        if (frame > 0) {
            frameTime = mVideoDuration / frame * 1000
        } else {
            frameTime = 1 * 1000
        }
        object : AsyncTask<Void?, Void?, Boolean>() {
            override fun onPostExecute(result: Boolean) {
                if (mThumbBitmap != null) {
                    videoEditView!!.addImageView(mThumbBitmap)
                }
            }
            override fun doInBackground(vararg params: Void?): Boolean {
                for (x in 0 until frame) {
                    val bitmap = mediaMetadata.getFrameAtTime(
                        (frameTime * x).toLong(),
                        MediaMetadataRetriever.OPTION_CLOSEST_SYNC
                    )
                    val msg = myHandler.obtainMessage()
                    msg.obj = bitmap
                    msg.arg1 = x
                    myHandler.sendMessage(msg)
                }
                try {
                    mediaMetadata.release()
                } catch (e: IOException) {
                    throw RuntimeException(e)
                }
                return true
            }
        }.execute()
    }

    private var mThumbBitmap: MutableList<Bitmap>? = ArrayList()
    private val myHandler: Handler = object : Handler() {
        override fun handleMessage(msg: Message) {
            if (mThumbBitmap != null) {
                mThumbBitmap!!.add(msg.arg1, msg.obj as Bitmap)
            }
        }
    }

    /*
     * Triggered when whe change start and end time for eny of the overlays.
     * */
    override fun selectTimeChange(startTime: Long, endTime: Long) {
        logMessage(Throwable().stackTrace[0].lineNumber.toString() + " selectTimeChange")
        if (mViews == null || mViews.size == 0) {
            return
        }
        var position: Int
        if (hasSelectStickerView) {
            position = mViews.indexOf(mCurrentView)
        } else {
            position = mViews.indexOf(mCurrentEditTextView)
        }
        if (mCurrentEditTextView_third != null && mCurrentEditTextView_third!!.isInEdit) {
            position = mViews.indexOf(mCurrentEditTextView_third)
        }
        if (position != -1) {
            mViews.get(position)!!.startTime = startTime
            mViews.get(position)!!.endTime = endTime
        }
    }

    /*
     * Triggered when we finish editing start and end time for duration any of the overlays.
     * Good place to let know other components that we finished with editing particular overlay.
     * */
    override fun playChange(isPlayVideo: Boolean) {
        Log.e(TAG, "播放状态变化")
        logMessage(Throwable().stackTrace[0].lineNumber.toString() + " playChange => " + isPlayVideo)
        this.isPlayVideo = isPlayVideo
        if (isPlayVideo) {
            if (mCurrentView != null) {
                mCurrentView!!.setInEdit(false)
            }
            if (mCurrentEditTextView != null) {
                mCurrentEditTextView!!.isInEdit = false
            }
            if (mCurrentEditTextView_third != null) mCurrentEditTextView_third!!.isInEdit = false
        } else {
            for (stickInfoImageView: StickInfoImageView? in stickerViews) {  //清空动态贴纸
                mContentRootView!!.removeView(stickInfoImageView)
            }
            stickerViews.clear()
        }
        try {
            if (isPlayVideo) {
//                    mVideoView.seekTo(0);
                mVideoView!!.start()
            } else {
                mVideoView!!.pause()
            }
        } catch (e: Exception) {
            Log.e(TAG, "异常:$e")
        }
    }

    override fun videoProgressUpdate(currentTime: Long, isVideoPlaying: Boolean) {
        logMessage(Throwable().stackTrace[0].lineNumber.toString() + " videoProgressUpdate")
        this.currentTime = currentTime
        if (!isVideoPlaying) {
            try {
                Log.e(TAG, "currentTime:$currentTime")
                mVideoView!!.seekTo(currentTime.toInt())
            } catch (e: Exception) {
                e.printStackTrace()
                Log.e(TAG, "异常:$e")
            }
        } else {
            Log.e(TAG, "播放中currentTime:$currentTime")
        }
        for (i in mViews!!.indices) {              ////遍历显示静态图
            val baseImageView = mViews[i]
            val startTime = baseImageView!!.startTime
            val endTime = baseImageView.endTime
            if (currentTime >= startTime && currentTime <= endTime) {
                if (baseImageView.isGif) {
                    if (currentTime != 0L) {
                        val frameIndex = baseImageView.frameIndex
                        (baseImageView as StickerView?)!!.changeBitmap(baseImageView.bitmaps[frameIndex])
                        mViews.get(i)!!.frameIndex = frameIndex + 1
                    }
                    baseImageView.visibility = View.VISIBLE
                } else {
                    baseImageView.visibility = View.VISIBLE
                }
            } else {
                baseImageView.visibility = View.GONE
            }
        }
    }

    override fun onBackPressed() {
        setResult(StaticFinalValues.COMR_FROM_VIDEO_EDIT_TIME_ACTIVITY, intent)
        super.onBackPressed()
    }

    private fun logMessage(message: String) {
        Log.d(TAG, " logMessage:  $message")
    }

    companion object {
        val VIDEO_PREPARE = 0
        val VIDEO_START = 1
        val VIDEO_UPDATE = 2
        val VIDEO_PAUSE = 3
        val VIDEO_CUT_FINISH = 4
        val CLIP_VIDEO_PERCENT = 5
        val AUTO_PAUSE = 6
    }
}