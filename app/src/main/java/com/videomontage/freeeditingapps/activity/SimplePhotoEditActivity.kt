package com.videomontage.freeeditingapps.activity

import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.view.View
import android.widget.ImageView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.videomontage.freeeditingapps.R
import ja.burhanrashid52.photoeditor.OnPhotoEditorListener
import ja.burhanrashid52.photoeditor.PhotoEditor
import ja.burhanrashid52.photoeditor.PhotoEditorView
import ja.burhanrashid52.photoeditor.SaveSettings
import ja.burhanrashid52.photoeditor.ViewType
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

/**
 * Simple photo editing activity using the photoeditor module
 */
class SimplePhotoEditActivity : AppCompatActivity(), OnPhotoEditorListener {
    
    override fun onTextStopMoving(text: String?, x: Int, y: Int) {
        // Called when text stops moving - implementation not needed for simple editor
    }

    companion object {
        private const val REQUEST_IMAGE_PICK = 1001
        const val EXTRA_IMAGE_PATH = "image_path"
        const val RESULT_IMAGE_PATH = "result_image_path"
    }

    private lateinit var photoEditorView: PhotoEditorView
    private lateinit var photoEditor: PhotoEditor
    private var imagePath: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_simple_photo_edit)

        // Initialize views
        photoEditorView = findViewById(R.id.photoEditorView)
        val backBtn = findViewById<ImageView>(R.id.backBtn)
        val saveBtn = findViewById<ImageView>(R.id.saveBtn)
        val textBtn = findViewById<View>(R.id.textBtn)
        val brushBtn = findViewById<View>(R.id.brushBtn)
        val eraserBtn = findViewById<View>(R.id.eraserBtn)
        val filterBtn = findViewById<View>(R.id.filterBtn)

        // Initialize photo editor
        photoEditor = PhotoEditor.Builder(this, photoEditorView)
            .setPinchTextScalable(true)
            .build()

        photoEditor.setOnPhotoEditorListener(this)

        // Get image path from intent or open image picker
        imagePath = intent.getStringExtra(EXTRA_IMAGE_PATH)
        if (imagePath != null) {
            loadImage(imagePath!!)
        } else {
            openImagePicker()
        }

        // Set click listeners
        backBtn.setOnClickListener {
            onBackPressed()
        }

        saveBtn.setOnClickListener {
            saveImage()
        }

        textBtn.setOnClickListener {
            photoEditor.addText("Sample Text", resources.getColor(R.color.white))
        }

        brushBtn.setOnClickListener {
            photoEditor.setBrushDrawingMode(true)
            photoEditor.brushSize = 10f
            photoEditor.brushColor = resources.getColor(R.color.colorAccent)
        }

        eraserBtn.setOnClickListener {
            photoEditor.brushEraser()
        }

        filterBtn.setOnClickListener {
            applyRandomFilter()
        }
    }

    private fun openImagePicker() {
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
        startActivityForResult(intent, REQUEST_IMAGE_PICK)
    }

    private fun loadImage(path: String) {
        try {
            val bitmap = BitmapFactory.decodeFile(path)
            photoEditorView.source.setImageBitmap(bitmap)
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, "Failed to load image", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    private fun saveImage() {
        val saveSettings = SaveSettings.Builder()
            .setClearViewsEnabled(true)
            .setTransparencyEnabled(true)
            .build()

        photoEditor.saveAsFile(getSaveFilePath(), saveSettings, object : PhotoEditor.OnSaveListener {
            override fun onSuccess(imagePath: String) {
                Toast.makeText(this@SimplePhotoEditActivity, "Image saved successfully", Toast.LENGTH_SHORT).show()
                
                // Return result
                val resultIntent = Intent()
                resultIntent.putExtra(RESULT_IMAGE_PATH, imagePath)
                setResult(Activity.RESULT_OK, resultIntent)
                finish()
            }

            override fun onFailure(exception: Exception) {
                Toast.makeText(this@SimplePhotoEditActivity, "Failed to save image", Toast.LENGTH_SHORT).show()
            }
        })
    }

    private fun getSaveFilePath(): String {
        val dir = File(getExternalFilesDir(null), "edited_photos")
        if (!dir.exists()) {
            dir.mkdirs()
        }
        return File(dir, "edited_${System.currentTimeMillis()}.jpg").absolutePath
    }

    private fun applyRandomFilter() {
        // Apply a simple filter effect
        photoEditor.setFilterEffect(ja.burhanrashid52.photoeditor.PhotoFilter.SEPIA)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_IMAGE_PICK && resultCode == Activity.RESULT_OK && data != null) {
            val selectedImage = data.data
            val filePath = getRealPathFromURI(selectedImage)
            if (filePath != null) {
                imagePath = filePath
                loadImage(filePath)
            } else {
                Toast.makeText(this, "Failed to get image path", Toast.LENGTH_SHORT).show()
                finish()
            }
        } else {
            // User cancelled image selection
            finish()
        }
    }

    private fun getRealPathFromURI(uri: Uri?): String? {
        if (uri == null) return null
        val projection = arrayOf(MediaStore.Images.Media.DATA)
        val cursor = contentResolver.query(uri, projection, null, null, null)
        return cursor?.use {
            val column_index = it.getColumnIndexOrThrow(MediaStore.Images.Media.DATA)
            it.moveToFirst()
            it.getString(column_index)
        }
    }

    override fun onEditTextChangeListener(rootView: View, text: String, colorCode: Int) {
        // Handle text change
    }

    override fun onAddViewListener(viewType: ViewType, numberOfAddedViews: Int) {
        // Handle view added
    }

    override fun onRemoveViewListener(viewType: ViewType, numberOfAddedViews: Int) {
        // Handle view removed
    }

    override fun onStartViewChangeListener(viewType: ViewType) {
        // Handle view change start
    }

    override fun onStopViewChangeListener(viewType: ViewType) {
        // Handle view change stop
    }
}