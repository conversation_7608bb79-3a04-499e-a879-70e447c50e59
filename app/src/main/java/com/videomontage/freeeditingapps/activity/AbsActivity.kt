package com.videomontage.freeeditingapps.activity

import android.Manifest
import android.R
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.provider.Settings
import android.view.View
import android.view.WindowManager
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.widget.ImageView
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import com.amplitude.api.Amplitude
import com.bumptech.glide.Glide
import com.bumptech.glide.Priority
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.google.android.material.snackbar.Snackbar
import com.skyfishjy.library.RippleBackground
import com.videomontage.freeeditingapps.analytics.AmplitudeHelper
import com.videomontage.freeeditingapps.analytics.AnalyticsVolley
import com.videomontage.freeeditingapps.application.MyApplication
import com.videomontage.freeeditingapps.rating.SfRatingItem
import com.videomontage.freeeditingapps.rating.SharedPref
import com.videomontage.freeeditingapps.utils.FabricEvents


abstract class AbsActivity : TopAbsActivity() {
    //    private FFmpeg ffmpeg;
    // ----------------  rating Dialog -----------------------
    private var rateTitleTV: TextView? = null
    private var rateThisAppTV: TextView? = null
    private var starOneIV: ImageView? = null
    private var starTwoIV: ImageView? = null
    private var starThreeIV: ImageView? = null
    private var starFourIV: ImageView? = null
    private var starFiveIV: ImageView? = null
    private var circleDialogIV: ImageView? = null
    private var closeDialogIV: ImageView? = null
    private var greenStarOneIV: SfRatingItem? = null
    private var greenStarTwoIV: SfRatingItem? = null
    private var greenStarThreeIV: SfRatingItem? = null
    private var greenStarFourIV: SfRatingItem? = null
    private var greenStarFiveIV: SfRatingItem? = null
    private var animation: Animation? = null
    private var starOneStartPosition = 0f
    private var starTwoStartPosition = 0f
    private var starThreeStartPosition = 0f
    private var starFourStartPosition = 0f
    private var starFiveStartPosition = 0f
    private var ratingLayoutContainer: CardView? = null
    private var ratingLayoutContainerGreen: CardView? = null
    private var animationCounter = 1
    private var rippleBackground: RippleBackground? = null
    private var xDestinattionCircle = 0f
    private var yDestinattionCircle = 0f
    private var stopMovingCircle = false
    private var greenStarBitmap: Bitmap? = null
    private var lastPressedStarIndex = 0
    private var appTitle: String? = null

    // -----------------------------------------------------
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //        ffmpeg = FFmpeg.getInstance(this);
        Amplitude.getInstance().initialize(this, "357f8460b2a6c77ddf895e62b6fae00b")
            .enableForegroundTracking(
                application
            )
        setContentView(setView())
        AmplitudeHelper.setAppOpened()
        //        loadFFMpegBinary();
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
//        initMyRatingDialog()
    }

    // Deprecated - use MainActivity's permission handling instead
    @Deprecated("Use MainActivity's centralized permission handling")
    fun checkRuntimePermission() {
        // This method is deprecated and should not be used
        // Permission checking is now handled in MainActivity
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            REQUEST_SETTING_PERMISSION -> {
                // Permission handling is now done in MainActivity
            }
        }
    }

    abstract fun setView(): Int
    fun initMyRatingDialog() {
        if (shouldInitDialog()) initRatingDialog()
    }

    private fun shouldInitDialog(): Boolean {
        SharedPref.init(applicationContext)
        val currentNumber = SharedPref.read(SharedPref.COUNTER, 0)
        return currentNumber > 2 && currentNumber <= 100
    }

    fun addCountForRatingDialog() {
        var currentNumber = SharedPref.read(SharedPref.COUNTER, 0)
        SharedPref.write(SharedPref.COUNTER, ++currentNumber) //save int in shared preference.
    }

    fun setVisibilityForRatingDialog(visibility: Int) {
        if (ratingLayoutContainer != null) ratingLayoutContainer!!.visibility = visibility
        if (ratingLayoutContainerGreen != null) ratingLayoutContainerGreen!!.visibility = View.GONE
    }

    private fun initRatingDialog() {
        appTitle = getApplicationName(baseContext)
        ratingLayoutContainer =
            findViewById(com.videomontage.freeeditingapps.R.id.rating_layout_container)
        ratingLayoutContainer?.let { it.visibility = View.VISIBLE }
        ratingLayoutContainerGreen =
            findViewById(com.videomontage.freeeditingapps.R.id.rating_layout_container_green)
        circleDialogIV = findViewById(com.videomontage.freeeditingapps.R.id.circleRatingDialogIV)
        circleDialogIV?.let { it.visibility = View.GONE }
        initStars()
        ratingLayoutContainer?.addOnAttachStateChangeListener(object :
            View.OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(v: View) {
                runAnimation()
                FabricEvents.logEvent(
                    FabricEvents.RATING_VALUE_SHOWED,
                    FabricEvents.NUMBER_SHOWED,
                    "Yes"
                )
            }

            override fun onViewDetachedFromWindow(v: View) {}
        })
        rippleBackground =
            findViewById<View>(com.videomontage.freeeditingapps.R.id.content) as RippleBackground
    }

    private fun initStars() {
        closeDialogIV = findViewById(com.videomontage.freeeditingapps.R.id.closeDialogIV)
        rateThisAppTV = findViewById(com.videomontage.freeeditingapps.R.id.rateThisAppTV)
        rateThisAppTV?.setOnClickListener(View.OnClickListener {
            ratingLayoutContainer!!.visibility = View.GONE
            SharedPref.write(SharedPref.COUNTER, 101)
            val intent = Intent(Intent.ACTION_SEND)
            intent.type = "plain/text"
            intent.putExtra(Intent.EXTRA_EMAIL, arrayOf("<EMAIL>"))
            intent.putExtra(
                Intent.EXTRA_SUBJECT, "Feedback " + getApplicationName(
                    baseContext
                )
            )
            intent.putExtra(Intent.EXTRA_TEXT, "")
            startActivity(Intent.createChooser(intent, "Feedback by"))
        })
        rateTitleTV = findViewById(com.videomontage.freeeditingapps.R.id.rateTitleTV)
        rateTitleTV?.text = getString(com.videomontage.freeeditingapps.R.string.common_enjoying) + " " + appTitle + "?"
        val rateTitleGreenTV =
            findViewById<TextView>(com.videomontage.freeeditingapps.R.id.rateTitleGreenTV)
        rateTitleGreenTV.text =
            getString(com.videomontage.freeeditingapps.R.string.common_enjoying) + " " + appTitle + "?"
        starOneIV = findViewById<View>(com.videomontage.freeeditingapps.R.id.starOneIV) as ImageView
        starOneIV!!.alpha = 0.0f
        starTwoIV = findViewById<View>(com.videomontage.freeeditingapps.R.id.starTwoIV) as ImageView
        starTwoIV!!.alpha = 0.0f
        starThreeIV =
            findViewById<View>(com.videomontage.freeeditingapps.R.id.starThreeIV) as ImageView
        starThreeIV!!.alpha = 0.0f
        starFourIV =
            findViewById<View>(com.videomontage.freeeditingapps.R.id.starFourIV) as ImageView
        starFourIV!!.alpha = 0.0f
        starFiveIV =
            findViewById<View>(com.videomontage.freeeditingapps.R.id.starFiveIV) as ImageView
        starFiveIV!!.alpha = 0.0f
    }

    private fun initCloseDialogListener() {
        if (closeDialogIV != null) closeDialogIV!!.setOnClickListener {
            ratingLayoutContainer!!.visibility = View.GONE
            SharedPref.write(SharedPref.COUNTER, 0)
        }
    }

    private fun initStarsListener() {
        if (starOneIV != null) starOneIV!!.setOnClickListener { setStarsAndStopAnimation(0) }
        if (starTwoIV != null) starTwoIV!!.setOnClickListener { setStarsAndStopAnimation(1) }
        if (starThreeIV != null) starThreeIV!!.setOnClickListener { setStarsAndStopAnimation(2) }
        if (starFourIV != null) starFourIV!!.setOnClickListener { setStarsAndStopAnimation(3) }
        if (starFiveIV != null) starFiveIV!!.setOnClickListener {
            setStarsAndStopAnimation(4)
            val appPackageName = packageName // getPackageName() from Context or Activity object
            try {
                startActivity(
                    Intent(
                        Intent.ACTION_VIEW, Uri.parse(
                            "market://details?id=$appPackageName"
                        )
                    )
                )
            } catch (anfe: ActivityNotFoundException) {
                startActivity(
                    Intent(
                        Intent.ACTION_VIEW, Uri.parse(
                            "https://play.google.com/store/apps/details?id=$appPackageName"
                        )
                    )
                )
            }
        }
    }

    private fun setStarsAndStopAnimation(index: Int) {
        if (index != 4) rateThisAppTV!!.visibility = View.VISIBLE else {
            ratingLayoutContainer!!.visibility = View.GONE
            SharedPref.write(SharedPref.COUNTER, 1000)
        }
        if (rippleBackground!!.isRippleAnimationRunning) rippleBackground!!.stopRippleAnimation()
        stopMovingCircle = true
        setGreenStar(index)
        FabricEvents.logEvent(
            FabricEvents.RATING_VALUE_PRESSED,
            FabricEvents.NUMBER_OF_STARS,
            index
        )
    }

    private fun setGreenStar(index: Int) {
        val options = RequestOptions()
            .centerCrop()
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .priority(Priority.HIGH)
        when (index) {
            0 -> if (lastPressedStarIndex > index) {
                Glide.with(baseContext).load(com.videomontage.freeeditingapps.R.drawable.ic_star)
                    .apply(options).into(
                    starTwoIV!!
                )
                Glide.with(baseContext).load(com.videomontage.freeeditingapps.R.drawable.ic_star)
                    .apply(options).into(
                    starThreeIV!!
                )
                Glide.with(baseContext).load(com.videomontage.freeeditingapps.R.drawable.ic_star)
                    .apply(options).into(
                    starFourIV!!
                )
                Glide.with(baseContext).load(com.videomontage.freeeditingapps.R.drawable.ic_star)
                    .apply(options).into(
                    starFiveIV!!
                )

//                    Glide.with(getBaseContext()).load(R.drawable.ic_star).centerCrop().into(starTwoIV);
//                    Glide.with(getBaseContext()).load(R.drawable.ic_star).centerCrop().into(starThreeIV);
//                    Glide.with(getBaseContext()).load(R.drawable.ic_star).centerCrop().into(starFourIV);
//                    Glide.with(getBaseContext()).load(R.drawable.ic_star).centerCrop().into(starFiveIV);
            } else Glide.with(baseContext).load(greenStarBitmap).apply(options).into(
                starOneIV!!
            )

            1 -> if (lastPressedStarIndex > index) {
                Glide.with(baseContext).load(com.videomontage.freeeditingapps.R.drawable.ic_star)
                    .apply(options).into(
                    starThreeIV!!
                )
                Glide.with(baseContext).load(com.videomontage.freeeditingapps.R.drawable.ic_star)
                    .apply(options).into(
                    starFourIV!!
                )
                Glide.with(baseContext).load(com.videomontage.freeeditingapps.R.drawable.ic_star)
                    .apply(options).into(
                    starFiveIV!!
                )

//                    Glide.with(getBaseContext()).load(R.drawable.ic_star).centerCrop().into(starThreeIV);
//                    Glide.with(getBaseContext()).load(R.drawable.ic_star).centerCrop().into(starFourIV);
//                    Glide.with(getBaseContext()).load(R.drawable.ic_star).centerCrop().into(starFiveIV);
            } else {
                Glide.with(baseContext).load(greenStarBitmap).apply(options).into(
                    starOneIV!!
                )
                Glide.with(baseContext).load(greenStarBitmap).apply(options).into(
                    starTwoIV!!
                )

//                    Glide.with(getBaseContext()).load(greenStarBitmap).centerCrop().into(starOneIV);
//                    Glide.with(getBaseContext()).load(greenStarBitmap).centerCrop().into(starTwoIV);
            }

            2 -> if (lastPressedStarIndex > index) {
                Glide.with(baseContext).load(com.videomontage.freeeditingapps.R.drawable.ic_star)
                    .apply(options).into(
                    starFourIV!!
                )
                Glide.with(baseContext).load(com.videomontage.freeeditingapps.R.drawable.ic_star)
                    .apply(options).into(
                    starFiveIV!!
                )

//                    Glide.with(getBaseContext()).load(R.drawable.ic_star).centerCrop().into(starFourIV);
//                    Glide.with(getBaseContext()).load(R.drawable.ic_star).centerCrop().into(starFiveIV);
            } else {
                Glide.with(baseContext).load(greenStarBitmap).apply(options).into(
                    starOneIV!!
                )
                Glide.with(baseContext).load(greenStarBitmap).apply(options).into(
                    starTwoIV!!
                )
                Glide.with(baseContext).load(greenStarBitmap).apply(options).into(
                    starThreeIV!!
                )

//                    Glide.with(getBaseContext()).load(greenStarBitmap).centerCrop().into(starOneIV);
//                    Glide.with(getBaseContext()).load(greenStarBitmap).centerCrop().into(starTwoIV);
//                    Glide.with(getBaseContext()).load(greenStarBitmap).centerCrop().into(starThreeIV);
            }

            3 -> if (lastPressedStarIndex > index) {
                Glide.with(baseContext).load(com.videomontage.freeeditingapps.R.drawable.ic_star)
                    .apply(options).into(
                    starFiveIV!!
                )

//                    Glide.with(getBaseContext()).load(R.drawable.ic_star).centerCrop().into(starFiveIV);
            } else {
                Glide.with(baseContext).load(greenStarBitmap).apply(options).into(
                    starOneIV!!
                )
                Glide.with(baseContext).load(greenStarBitmap).apply(options).into(
                    starTwoIV!!
                )
                Glide.with(baseContext).load(greenStarBitmap).apply(options).into(
                    starThreeIV!!
                )
                Glide.with(baseContext).load(greenStarBitmap).apply(options).into(
                    starFourIV!!
                )

//                    Glide.with(getBaseContext()).load(greenStarBitmap).centerCrop().into(starOneIV);
//                    Glide.with(getBaseContext()).load(greenStarBitmap).centerCrop().into(starTwoIV);
//                    Glide.with(getBaseContext()).load(greenStarBitmap).centerCrop().into(starThreeIV);
//                    Glide.with(getBaseContext()).load(greenStarBitmap).centerCrop().into(starFourIV);
            }

            4 -> {
                Glide.with(baseContext).load(greenStarBitmap).apply(options).into(
                    starOneIV!!
                )
                Glide.with(baseContext).load(greenStarBitmap).apply(options).into(
                    starTwoIV!!
                )
                Glide.with(baseContext).load(greenStarBitmap).apply(options).into(
                    starThreeIV!!
                )
                Glide.with(baseContext).load(greenStarBitmap).apply(options).into(
                    starFourIV!!
                )
                Glide.with(baseContext).load(greenStarBitmap).apply(options).into(
                    starFiveIV!!
                )
            }
        }
        lastPressedStarIndex = index
    }

    private fun initGreenStars() {
        //greenStarOneIV, greenStarTwoIV, greenStarThreeIV, greenStarFourIV, greenStarFiveIV;
        greenStarOneIV = findViewById(com.videomontage.freeeditingapps.R.id.greenStarOneIV)
        greenStarOneIV?.setAlpha(0.0f)
        greenStarTwoIV = findViewById(com.videomontage.freeeditingapps.R.id.greenStarTwoIV)
        greenStarTwoIV?.setAlpha(0.0f)
        greenStarThreeIV = findViewById(com.videomontage.freeeditingapps.R.id.greenStarThreeIV)
        greenStarThreeIV?.setAlpha(0.0f)
        greenStarFourIV = findViewById(com.videomontage.freeeditingapps.R.id.greenStarFourIV)
        greenStarFourIV?.setAlpha(0.0f)
        greenStarFiveIV = findViewById(com.videomontage.freeeditingapps.R.id.greenStarFiveIV)
        greenStarFiveIV?.setAlpha(0.0f)
        animation =
            AnimationUtils.loadAnimation(this, com.videomontage.freeeditingapps.R.anim.scale_up)
        animation?.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation) {
                animationCounter++
            }

            override fun onAnimationEnd(animation: Animation) {
                Handler().postDelayed({
                    when (animationCounter) {
                        2 -> {
                            greenStarBitmap = loadBitmapFromView(greenStarOneIV)
                            greenStarTwoIV?.setAlpha(1.0f)
                            greenStarTwoIV?.startAnimation(animation)
                        }

                        3 -> {
                            greenStarThreeIV?.setAlpha(1.0f)
                            greenStarThreeIV?.startAnimation(animation)
                        }

                        4 -> {
                            greenStarFourIV?.setAlpha(1.0f)
                            greenStarFourIV?.startAnimation(animation)
                        }

                        5 -> {
                            greenStarFiveIV?.setAlpha(1.0f)
                            greenStarFiveIV?.startAnimation(animation)
                        }

                        else -> initLayoutContainer()
                    }
                }, 100)
            }

            override fun onAnimationRepeat(animation: Animation) {}
        })
        greenStarOneIV?.setAlpha(1.0f)
        greenStarOneIV?.startAnimation(animation)
    }

    fun initLayoutContainer() {
        if (starOneIV == null) {
            return
        }
        if (!shouldInitDialog()) return
        initStarsListener()
        initCloseDialogListener()
        xDestinattionCircle = greenStarFiveIV!!.x + 20
        yDestinattionCircle = greenStarFiveIV!!.y + 20
        findViewById<View>(com.videomontage.freeeditingapps.R.id.rating_layout_container_green).visibility =
            View.GONE
        findViewById<View>(com.videomontage.freeeditingapps.R.id.rating_layout_container).visibility =
            View.VISIBLE
        circleDialogIV!!.visibility = View.VISIBLE
        circleDialogIV!!.x = xDestinattionCircle + 50
        circleDialogIV!!.y = yDestinattionCircle + 300
        val color = ContextCompat.getColor(
            baseContext,
            com.videomontage.freeeditingapps.R.color.colorAccent
        )
        circleDialogIV!!.setColorFilter(color)
        startMovingCircle()
    }

    private fun startMovingCircle() {
        circleDialogIV!!.animate()
            .withEndAction {
                circleDialogIV!!.alpha = 0.0f
                circleDialogIV!!.x = xDestinattionCircle + 50
                circleDialogIV!!.y = yDestinattionCircle + 300
                rippleBackground!!.startRippleAnimation()
                Handler().postDelayed({
                    rippleBackground!!.stopRippleAnimation()
                    circleDialogIV!!.alpha = 1.0f
                    if (!stopMovingCircle) startMovingCircle()
                }, 2000)
            }
            .x(xDestinattionCircle)
            .y(yDestinattionCircle)
            .setDuration(1000)
            .start()
    }

    private fun runAnimation() {
        Handler().postDelayed({
            starOneStartPosition = starOneIV!!.x
            starTwoStartPosition = starTwoIV!!.x
            starThreeStartPosition = starThreeIV!!.x
            starFourStartPosition = starFourIV!!.x
            starFiveStartPosition = starFiveIV!!.x
            starOneIV!!.alpha = 1.0f
            starOneIV!!.x = starOneStartPosition + 800
            starTwoIV!!.alpha = 1.0f
            starTwoIV!!.x = starTwoStartPosition + 800
            starThreeIV!!.alpha = 1.0f
            starThreeIV!!.x = starThreeStartPosition + 800
            starFourIV!!.alpha = 1.0f
            starFourIV!!.x = starFourStartPosition + 800
            starFiveIV!!.alpha = 1.0f
            starFiveIV!!.x = starFiveStartPosition + 800
            starOneIV!!.animate()
                .x(starOneStartPosition)
                .setDuration(1000)
                .start()
            starTwoIV!!.animate()
                .x(starTwoStartPosition)
                .setDuration(1200)
                .start()
            starThreeIV!!.animate()
                .x(starThreeStartPosition)
                .setDuration(1400)
                .start()
            starFourIV!!.animate()
                .x(starFourStartPosition)
                .setDuration(1600)
                .start()
            starFiveIV!!.animate().withEndAction {
                findViewById<View>(com.videomontage.freeeditingapps.R.id.rating_layout_container_green).visibility =
                    View.VISIBLE
                findViewById<View>(com.videomontage.freeeditingapps.R.id.rating_layout_container).visibility =
                    View.GONE
                initGreenStars()
            }
                .x(starFiveStartPosition)
                .setDuration(1800)
                .start()
        }, 0)
    }

    override fun onResume() {
        MyApplication.onActivityResumed()
        Handler().postDelayed({ AnalyticsVolley.sendAnalytics(applicationContext) }, 1000)
        super.onResume()
    }

    override fun onPause() {
        MyApplication.onActivityPaused()
        super.onPause()
    }

    companion object {
        private const val REQUEST_SETTING_PERMISSION = 231
        fun getApplicationName(context: Context): String {
            val applicationInfo = context.applicationInfo
            val stringId = applicationInfo.labelRes
            return if (stringId == 0) applicationInfo.nonLocalizedLabel.toString() else context.getString(
                stringId
            )
        }

        fun loadBitmapFromView(v: View?): Bitmap {
            val b = Bitmap.createBitmap(
                v!!.layoutParams.width,
                v.layoutParams.height,
                Bitmap.Config.ARGB_8888
            )
            val c = Canvas(b)
            v.layout(v.left, v.top, v.right, v.bottom)
            v.draw(c)
            return b
        }
    }
}