package com.videomontage.freeeditingapps.activity

import android.Manifest
import android.content.Context
import android.content.CursorLoader
import android.content.Intent
import android.content.pm.PackageManager
import android.database.Cursor
import android.media.MediaMetadataRetriever
import android.media.MediaMetadataRetriever.*
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.hbisoft.pickit.PickiT
import com.hbisoft.pickit.PickiTCallbacks
import java.io.File
import java.util.*

open class TopAbsActivity : AppCompatActivity(), PickiTCallbacks {

//    private val storage = SimpleStorage(this)
    private val REQUEST_CODE_STORAGE_ACCESS: Int = 111
    private var pickiT: PickiT? = null
    var videoImageRef = ""

    //Permissions
    private val PERMISSION_REQ_ID_RECORD_AUDIO = 22
    private val PERMISSION_REQ_ID_WRITE_EXTERNAL_STORAGE = PERMISSION_REQ_ID_RECORD_AUDIO + 1

//    private lateinit var billingUtil : BillingUtil


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)


        //Initialize PickiT
        pickiT = PickiT(this, this@TopAbsActivity, this)
        videoImageRef = "video"
//        openGallery("video")

        // Permission check removed - handled by MainActivity
        // checkSelfPermission()

//        billingUtil = BillingUtil(this@TopAbsActivity)
//        billingUtil.connectBillingClient(this@TopAbsActivity)

    }

    override fun onResume() {
//        billingUtil.queryPurchases()
        super.onResume()
    }

    fun openGallery(videoOrImage: String) {
        //  first check if permissions was granted
//        if (checkSelfPermission()) {
//            if (videoImageRef == "video") {
//                videoImageRef = ""
//                val intent: Intent
//                intent = if (Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED) {
//                    Intent(Intent.ACTION_PICK, MediaStore.Video.Media.EXTERNAL_CONTENT_URI)
//                } else {
//                    Intent(Intent.ACTION_PICK, MediaStore.Video.Media.INTERNAL_CONTENT_URI)
//                }
//                //  In this example we will set the type to video
//                intent.type = "video/*"
//                intent.action = Intent.ACTION_GET_CONTENT
//                intent.putExtra("return-data", true)
//                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
//                    intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
//                }
//                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
//                activityResultLauncher.launch(intent)
//            } else {
//                videoImageRef = ""
//                val intent: Intent
//                intent = if (Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED) {
//                    Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
//                } else {
//                    Intent(Intent.ACTION_PICK, MediaStore.Images.Media.INTERNAL_CONTENT_URI)
//                }
//                //  In this example we will set the type to video
//                intent.type = "image/*"
//                intent.action = Intent.ACTION_GET_CONTENT
//                intent.putExtra("return-data", true)
//                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
//                    intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
//                }
//                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
//                activityResultLauncher.launch(intent)
//            }
//        }
    }


    private fun checkSelfPermission(): Boolean {
        // For Android 13+, check media permissions
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            val permissions = arrayOf(
                // READ_MEDIA_IMAGES removed - using Intent-based image selection
                // READ_MEDIA_VIDEO removed - using Intent-based video selection
                Manifest.permission.READ_MEDIA_AUDIO
            )
            
            val notGranted = permissions.filter {
                ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED
            }
            
            if (notGranted.isNotEmpty()) {
                ActivityCompat.requestPermissions(this, notGranted.toTypedArray(), PERMISSION_REQ_ID_WRITE_EXTERNAL_STORAGE)
                return false
            }
            return true
        }
        
        // For Android 10-12, no WRITE_EXTERNAL_STORAGE needed
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.Q) return true

        // For Android 9 and below
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE), PERMISSION_REQ_ID_WRITE_EXTERNAL_STORAGE)
            return false
        }
        return true
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String?>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PERMISSION_REQ_ID_WRITE_EXTERNAL_STORAGE) {
            if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                //  Permissions was granted, open the gallery
                if (videoImageRef == "video") {
                    openGallery("video")
                } else {
                    openGallery("image")
                }
            } else {
                showLongToast("No permission for " + Manifest.permission.WRITE_EXTERNAL_STORAGE)
            }
        }
    }

    fun showLongToast(msg: String) {
        Toast.makeText(applicationContext, msg, Toast.LENGTH_LONG).show()
    }

    var activityResultLauncher = registerForActivityResult(
            StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            val data = result.data
            //  Get path from PickiT (The path will be returned in PickiTonCompleteListener)
            //
            //  If the selected file is from Dropbox/Google Drive or OnDrive:
            //  Then it will be "copied" to your app directory (see path example below) and when done the path will be returned in PickiTonCompleteListener
            //  /storage/emulated/0/Android/data/your.package.name/files/Temp/tempDriveFile.mp4
            //
            //  else the path will directly be returned in PickiTonCompleteListener
            val clipData = Objects.requireNonNull(data)!!.clipData
            if (clipData != null) {
                val numberOfFilesSelected = clipData.itemCount
                if (numberOfFilesSelected > 1) {
                    pickiT!!.getMultiplePaths(clipData)
                    val allPaths = StringBuilder("""
    Multiple Files Selected:
    
    """.trimIndent())
                    for (i in 0 until clipData.itemCount) {
                        allPaths.append("\n\n").append(clipData.getItemAt(i).uri)
                    }
//                    originalTv.setText(allPaths.toString())
                } else {
                    pickiT!!.getPath(clipData.getItemAt(0).uri, Build.VERSION.SDK_INT)
//                    originalTv.setText(clipData.getItemAt(0).uri.toString())
                }
            } else {
                pickiT!!.getPath(data!!.data, Build.VERSION.SDK_INT)
//                originalTv.setText(data.data.toString())
            }
        }
    }


    override fun PickiTonUriReturned() {
        TODO("Not yet implemented")
    }

    override fun PickiTonStartListener() {
        TODO("Not yet implemented")
    }

    override fun PickiTonProgressUpdate(progress: Int) {
        TODO("Not yet implemented")
    }

    override fun PickiTonCompleteListener(path: String?, wasDriveFile: Boolean, wasUnknownProvider: Boolean, wasSuccessful: Boolean, Reason: String?) {

        //  Check if it was a Drive/local/unknown provider file and display a Toast

        //  Check if it was a Drive/local/unknown provider file and display a Toast
        if (wasDriveFile) {
            showLongToast("Drive file was selected")
        } else if (wasUnknownProvider) {
            showLongToast("File was selected from unknown provider")
        } else {
            showLongToast("Local file was selected")
        }

        //  Chick if it was successful
        if (wasSuccessful) {
            //  Set returned path to TextView
            if (path!!.contains("/proc/")) {
                Log.d("dddd", "PickiTonCompleteListener: Sub-directory inside Downloads was selected.\n We will be making use of the /proc/ protocol.\n You can use this path as you would normally.\n\nPickiT path:\n$path")
            } else {
                Log.d("dddd", "PickiTonCompleteListener: $path")
//                var documentFile = DocumentFileCompat.fromFullPath(baseContext, path)
                var videoFile = File(path)
                Log.d("ddd", "PickiTonCompleteListener: ${videoFile.getMediaDuration(baseContext)}")
//                var mediaStoreCompat1 = MediaStoreCompat.fromFileName(baseContext, MediaType.VIDEO, documentFile?.name!!)
//                Log.d("dddd", "PickiTonCompleteListener: ${mediaStoreCompat1?.lastModified}")
//                Log.d("dddd", "PickiTonCompleteListener: ${mediaStoreCompat1?.length}")

//                                    var videoModel: VideoModel = VideoModel(
//                            it.id,
//                            it.name,
//                            "",
//                            "",
//                            it.length().toString(),
////                            "/storage/emulated/0/DCIM/VideoCollage/20201016_161625.mp4",
//                            it.uri.toString(),
//                            "",
//                            123555L,
//                            it.lastModified().toString())
//                    Statistic.SELECTED_VIDEO_MODEL = videoModel
//                    SelectedFragmentHelper.addFragment(0, supportFragmentManager, this@TopAbsActivity)
            }

        } else {
            showLongToast("Error, please see the log..")
        }
    }



    override fun PickiTonMultipleCompleteListener(paths: ArrayList<String>?, wasSuccessful: Boolean, Reason: String?) {
        val allPaths = java.lang.StringBuilder()
        for (i in paths!!.indices) {
            allPaths.append("\n").append(paths[i]).append("\n")
        }

        Log.d("dddd", "PickiTonMultipleCompleteListener: ${allPaths.toString()}")
    }


//    private fun setupSimpleStorage() {
//        storage.storageAccessCallback = object : StorageAccessCallback {
//            override fun onRootPathNotSelected(
//                    requestCode: Int,
//                    rootPath: String,
//                    uri: Uri,
//                    selectedStorageType: StorageType,
//                    expectedStorageType: StorageType
//            ) {
//                MaterialDialog(this@TopAbsActivity)
//                        .message(text = "Please select $rootPath")
//                        .negativeButton(android.R.string.cancel)
//                        .positiveButton {
//                            val initialRoot = if (expectedStorageType.isExpected(selectedStorageType)) selectedStorageType else expectedStorageType
//                            storage.requestStorageAccess(REQUEST_CODE_STORAGE_ACCESS, initialRoot, expectedStorageType)
//                        }.show()
//            }
//
//            override fun onExpectedStorageNotSelected(requestCode: Int, selectedFolder: DocumentFile, selectedStorageType: StorageType, expectedBasePath: String, expectedStorageType: StorageType) {
//                Toast.makeText(baseContext, "onExpectedStorageNotSelected", Toast.LENGTH_SHORT).show()
//
//            }
//
//            override fun onCanceledByUser(requestCode: Int) {
//                Toast.makeText(baseContext, "Canceled by user", Toast.LENGTH_SHORT).show()
//            }
//
//            override fun onStoragePermissionDenied(requestCode: Int) {
//                /*
//                Request runtime permissions for Manifest.permission.WRITE_EXTERNAL_STORAGE
//                and Manifest.permission.READ_EXTERNAL_STORAGE
//                */
//            }
//
//            override fun onRootPathPermissionGranted(requestCode: Int, root: DocumentFile) {
//                Toast.makeText(baseContext, "Storage access has been granted for ${root.getStorageId(baseContext)}", Toast.LENGTH_SHORT).show()
//            }
//        }
//    }

//    private fun setupFilePickerCallback() {
//        storage.filePickerCallback = object : FilePickerCallback {
//            override fun onCanceledByUser(requestCode: Int) {
//                Toast.makeText(baseContext, "File picker canceled by user", Toast.LENGTH_SHORT).show()
//            }
//
//            override fun onStoragePermissionDenied(requestCode: Int, files: List<DocumentFile>?) {
//
//            }
//
//            override fun onFileSelected(requestCode: Int, files: List<DocumentFile>) {
//                files.forEach {
//                    var mediaStoreCompat = MediaStoreCompat.fromMediaId(this@TopAbsActivity, MediaType.VIDEO, it.id)
//                    Log.d("ddd", "onFileSelected: ${Environment.getExternalStorageDirectory().getAbsolutePath()}")
//                    var videoModel: VideoModel = VideoModel(
//                            it.id,
//                            it.name,
//                            "",
//                            "",
//                            it.length().toString(),
////                            "/storage/emulated/0/DCIM/VideoCollage/20201016_161625.mp4",
//                            it.uri.toString(),
//                            "",
//                            123555L,
//                            it.lastModified().toString())
//                    Statistic.SELECTED_VIDEO_MODEL = videoModel
//                    SelectedFragmentHelper.addFragment(0, supportFragmentManager, this@TopAbsActivity)
//
//                }
//                Toast.makeText(baseContext, "File selected: ${files}", Toast.LENGTH_SHORT).show()
//
//            }
//
//        }
//    }


}

fun File.getMediaDuration(context: Context): Long {
    if (!exists()) return 0
    val retriever = MediaMetadataRetriever()
    retriever.setDataSource(context, Uri.parse(absolutePath))
    val duration = retriever.extractMetadata(METADATA_KEY_DURATION)
    val author = retriever.extractMetadata(METADATA_KEY_AUTHOR)
    val album = retriever.extractMetadata(METADATA_KEY_ALBUM)
    val albumArtist = retriever.extractMetadata(METADATA_KEY_ALBUMARTIST)
    val frameCount = retriever.extractMetadata(METADATA_KEY_VIDEO_FRAME_COUNT)
    val videoHeight = retriever.extractMetadata(METADATA_KEY_VIDEO_HEIGHT)
    val videoWidth = retriever.extractMetadata(METADATA_KEY_VIDEO_WIDTH)

    Log.d("ddd", "getMediaDuration: " +
            "\nAuthor $author" +
            "\nAlbum $album" +
            "\nAlbum Artist $albumArtist" +
            "\nFrame Count $frameCount" +
            "\nVideo Height $videoHeight" +
            "\nVideo Width $videoWidth")

    retriever.release()

    return duration?.toLongOrNull() ?: 0
}