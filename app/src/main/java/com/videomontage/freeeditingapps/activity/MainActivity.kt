package com.videomontage.freeeditingapps.activity

//import com.google.android.gms.ads.AdError
//import com.google.android.gms.ads.AdRequest
//import com.google.android.gms.ads.FullScreenContentCallback
//import com.google.android.gms.ads.MobileAds
//import com.google.android.gms.ads.interstitial.InterstitialAd
//import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback
import android.Manifest
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.PopupMenu
import android.widget.Toast
import androidx.core.content.ContextCompat
// import com.createphoto.MainActivity
// import com.glitch.activity.SplashScreenActivity
import com.google.android.material.snackbar.Snackbar
import com.karumi.dexter.Dexter
import com.karumi.dexter.MultiplePermissionsReport
import com.karumi.dexter.PermissionToken
import com.karumi.dexter.listener.PermissionRequest
import com.karumi.dexter.listener.multi.MultiplePermissionsListener
import com.videomontage.freeeditingapps.BuildConfig
import com.videomontage.freeeditingapps.R
import com.videomontage.freeeditingapps.analytics.AmplitudeHelper
import com.videomontage.freeeditingapps.fragment.DetailsSelectFileFragment
import com.videomontage.freeeditingapps.fragment.*
import com.videomontage.freeeditingapps.model.VideoModel
import com.videomontage.freeeditingapps.utils.UriToPathUtil
import timber.log.Timber
import com.videomontage.freeeditingapps.statistic.Statistic
import com.videomontage.freeeditingapps.utils.Const
import com.videomontage.freeeditingapps.utils.FabricEvents
import com.videomontage.freeeditingapps.utils.Flog
import com.videomontage.freeeditingapps.utils.SelectedFragmentHelper.FRAGMENT_INDEX
import com.videomontage.freeeditingapps.utils.StorageUtils
import com.videomontage.freeeditingapps.utils.UrlUtil
import com.videomontage.freeeditingapps.utils.assets.AssetsUtil.listAssetFiles
import com.videomontage.freeeditingapps.view.CreditsDialog
import java.io.File

//public class MainActivity extends AbsActivity implements BillingProcessor.IBillingHandler {
class MainActivity : AbsActivity() {
    private var doubleBackToExitPressedOnce = false

    //    BillingProcessor bp;
    //    private DialogExitApp dialogExitApp;
    //    private AdmobFullHelper admobFullHelper;
    private var ivBg: ImageView? = null
    private var moreIv: ImageView? = null
    private var pendingActionForVideoPicker = -1 // Track which action triggered the video picker
    private var viewAds: FrameLayout? = null
    private val receiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (intent == null || intent.action == null) {
                return
            }
            when (intent.action) {
                Statistic.OPEN_CUTTER_STUDIO -> {
                    addFragmentStudio(INDEX_CUTTER, false)
                }
                Statistic.OPEN_MERGER_STUDIO -> {
                    addFragmentStudio(INDEX_MERGER, false)
                }
                Statistic.OPEN_SPEED_STUDIO -> {
                    addFragmentStudio(INDEX_SPEED, false)
                }
                Statistic.OPEN_ADD_MUSIC_STUDIO -> {
                    addFragmentStudio(INDEX_ADD_MUSIC, false)
                }
                Statistic.OPEN_EFFECSTS_STUDIO -> {
                    addFragmentStudio(INDEX_EFFECTS, false)
                }
            }
        }
    }

    //    private var mInterstitialAd: InterstitialAd? = null
    private var afterAdHanlder: Handler? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

//        bp = new BillingProcessor(this, "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApTYMYL+ETFD8p+eJ0wBaflGKe6CqQPa/UQzcGoyIumRGRNVEV4SgEvZVqt2SM9FZApIQcDS//yyZm5ec5lxZbeAzc8jIiSHLRljSXE7K4z/D7IFkbBT+WuahT+vZ17UHle+jxLxJxcmNxTgBIXaigCtjloYub5Nbp6+ERKl+BRYeWOxAUN2BusmxI2qL64iIXudyNsluQ7p4jSJburAjsRXJqy0z8pkPAo8F29evmGjIMBeLYiEINMFBwRGlc2rbGLOBJbfhkMvl/c9MQPr8AUX9eIc2XGXLg2vieXS95kP8XU720JSxQSX5fLyS0wZoEC6kxzhV23ORBZ1IM1iwiQIDAQAB", this);
//        bp.initialize();
        deleteCache(baseContext)
        initActions()
        initView()
        //        loadAdsBanner();
//        loadAds()
        listAssetFiles("fonts", this)

//        new Handler().postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                startActivity(new Intent(MainActivity.this, SplashScreenActivity.class));
//            }
//        }, 1000);
        
        // Check permissions once on startup with caching
        if (!isPermissionCheckCached() || !hasRequiredPermissions()) {
            checkInitialPermissions()
        }

    }

    override fun setView(): Int {
        return R.layout.activity_main
    }

    private fun initActions() {
        val it = IntentFilter()
        it.addAction(Statistic.OPEN_CUTTER_STUDIO)
        it.addAction(Statistic.OPEN_MERGER_STUDIO)
        it.addAction(Statistic.OPEN_SPEED_STUDIO)
        it.addAction(Statistic.OPEN_ADD_MUSIC_STUDIO)
        it.addAction(Statistic.OPEN_EFFECSTS_STUDIO)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            registerReceiver(receiver, it, Context.RECEIVER_NOT_EXPORTED)
        } else {
            registerReceiver(receiver, it)
        }
    }

    override fun onDestroy() {
//        if (bp != null) {
//            bp.release();
//        }
        super.onDestroy()
        unregisterReceiver(receiver)
    }

    fun setBackGroundAds(id: Int) {
        viewAds!!.setBackgroundColor(id)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        if (resultCode != RESULT_OK || data == null) {
            Timber.tag("DEBUG_FLOW").d("MainActivity: Video selection cancelled or failed")
            pendingActionForVideoPicker = -1
            return
        }
        
        when (requestCode) {
            REQUEST_VIDEO_PICK -> {
                handleSingleVideoSelection(data)
            }
            REQUEST_MULTIPLE_VIDEO_PICK -> {
                handleMultipleVideoSelection(data)
            }
        }
    }
    
    private fun handleSingleVideoSelection(data: Intent) {
        val uri = data.data
        if (uri != null) {
            Timber.tag("DEBUG_FLOW").d("MainActivity: Single video selected: $uri")
            
            val videoModel = createVideoModelFromUri(uri)
            if (videoModel != null) {
                navigateToTargetFragment(videoModel)
            } else {
                Toast.makeText(this, "Unable to process selected video", Toast.LENGTH_SHORT).show()
            }
        }
        pendingActionForVideoPicker = -1
    }
    
    private fun handleMultipleVideoSelection(data: Intent) {
        val videoModels = mutableListOf<VideoModel>()
        
        // Handle multiple selection
        data.clipData?.let { clipData ->
            for (i in 0 until clipData.itemCount) {
                val uri = clipData.getItemAt(i).uri
                val videoModel = createVideoModelFromUri(uri)
                if (videoModel != null) {
                    videoModels.add(videoModel)
                }
            }
        } ?: data.data?.let { uri ->
            // Single selection through multiple picker
            val videoModel = createVideoModelFromUri(uri)
            if (videoModel != null) {
                videoModels.add(videoModel)
            }
        }
        
        Timber.tag("DEBUG_FLOW").d("MainActivity: Multiple videos selected: ${videoModels.size}")
        
        if (videoModels.isNotEmpty()) {
            navigateToMergerFragment(videoModels)
        } else {
            Toast.makeText(this, "No valid videos selected", Toast.LENGTH_SHORT).show()
        }
        pendingActionForVideoPicker = -1
    }
    
    private fun createVideoModelFromUri(uri: Uri): VideoModel? {
        return try {
            val contentResolver = contentResolver
            
            // Get FFmpeg-compatible file path
            val filePath = UriToPathUtil.getFFmpegCompatiblePath(this, uri)
            if (filePath == null) {
                Timber.tag("DEBUG_FLOW").e("MainActivity: Unable to get file path for URI: $uri")
                return null
            }
            
            // Get basic info from ContentResolver
            val cursor = contentResolver.query(uri, null, null, null, null)
            var displayName = "Selected Video"
            var size = 0L
            
            cursor?.use {
                if (it.moveToFirst()) {
                    val nameIndex = it.getColumnIndex("_display_name")
                    val sizeIndex = it.getColumnIndex("_size")
                    
                    if (nameIndex != -1) {
                        displayName = it.getString(nameIndex) ?: "Selected Video"
                    }
                    if (sizeIndex != -1) {
                        size = it.getLong(sizeIndex)
                    }
                }
            }
            
            // Get video duration using MediaMetadataRetriever
            var duration = "0"
            try {
                val retriever = MediaMetadataRetriever()
                retriever.setDataSource(this, uri)
                val durationMs = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
                if (durationMs != null) {
                    duration = durationMs // Duration in milliseconds as string
                    Timber.tag("DEBUG_FLOW").d("MainActivity: Video duration: ${duration}ms")
                }
                retriever.release()
            } catch (e: Exception) {
                Timber.tag("DEBUG_FLOW").e(e, "MainActivity: Error getting video duration, using 0")
            }
            
            VideoModel(
                uri.toString(), // id
                displayName, // nameAudio (title)
                "", // nameArtist
                "", // nameAlbum
                duration, // duration in milliseconds
                filePath, // path (FFmpeg-compatible file path)
                "", // resolution
                size, // size
                System.currentTimeMillis().toString() // dateModifier
            )
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e(e, "MainActivity: Error creating VideoModel from URI")
            null
        }
    }
    
    private fun navigateToTargetFragment(videoModel: VideoModel) {
        Timber.tag("DEBUG_FLOW").d("MainActivity: Navigating to fragment for action: $pendingActionForVideoPicker")
        
        val bundle = Bundle().apply {
            putInt(Statistic.ACTION, pendingActionForVideoPicker)
            putParcelable(Statistic.VIDEO_MODEL, videoModel)
            putString(Statistic.PATH_VIDEO, videoModel.path)
        }
        
        val fragment = when (pendingActionForVideoPicker) {
            INDEX_CUTTER -> CutterFragment.newInstance(bundle)
            INDEX_SPEED -> SpeedFragment.newInstance(bundle)
            INDEX_ADD_MUSIC -> AddMusicFragment.newInstance(bundle)
            INDEX_EFFECTS -> MoreOptionsFragment.newInstance(bundle)
            else -> null
        }
        
        fragment?.let {
            supportFragmentManager.beginTransaction()
                .setCustomAnimations(
                    R.anim.animation_left_to_right,
                    R.anim.animation_right_to_left,
                    R.anim.animation_left_to_right,
                    R.anim.animation_right_to_left
                )
                .replace(R.id.view_container, it)
                .addToBackStack(null)
                .commitAllowingStateLoss()
        }
    }
    
    private fun navigateToMergerFragment(videoModels: List<VideoModel>) {
        Timber.tag("DEBUG_FLOW").d("MainActivity: Navigating to DetailsSelectFileFragment with ${videoModels.size} videos")
        
        // Store the selected videos in a static variable that DetailsSelectFileFragment can access
        DetailsSelectFileFragment.preSelectedVideos = videoModels
        
        // Navigate to DetailsSelectFileFragment which handles merger workflow
        val fragment = DetailsSelectFileFragment.newInstance()
        supportFragmentManager.beginTransaction()
            .setCustomAnimations(
                R.anim.animation_left_to_right,
                R.anim.animation_right_to_left,
                R.anim.animation_left_to_right,
                R.anim.animation_right_to_left
            )
            .replace(R.id.view_container, fragment)
            .addToBackStack(null)
            .commitAllowingStateLoss()
    }

    override fun onBackPressed() {
        Flog.e("baccccfffffffffffffffcccccccc")
        try {
            val fragmentsInStack = supportFragmentManager.backStackEntryCount
            if (fragmentsInStack > 0) {
                supportFragmentManager.popBackStack()

                Handler().postDelayed({
                    if (fragmentsInStack < 2) {
                        initLayoutContainer()
                    }
                }, 1000)
            } else {
                if (doubleBackToExitPressedOnce) {
                    super.onBackPressed()
                    return
                }
                doubleBackToExitPressedOnce = true
                Toast.makeText(
                    this,
                    baseContext.resources.getString(R.string.common_press_back_again),
                    Toast.LENGTH_SHORT
                ).show()
                Handler().postDelayed({ doubleBackToExitPressedOnce = false }, 2000)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

//                if (AppRate.showRateDialogIfMeetsConditions(this)) {
//
//                } else {
//                    dialogExitApp.show();
//                }
    }

//    private fun loadAds() {
//        MobileAds.initialize(this) {
//            val adRequest = AdRequest.Builder().build()
//            InterstitialAd.load(
//                this,
//                if (TEST) "ca-app-pub-3940256099942544/1033173712"
//                else "ca-app-pub-2572150356682336/6250279701",
//                adRequest,
//                object : InterstitialAdLoadCallback() {
//                    override fun onAdLoaded(interstitialAd: InterstitialAd) {
//                        mInterstitialAd = interstitialAd
//                        mInterstitialAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
//                            override fun onAdDismissedFullScreenContent() {
//                                mInterstitialAd = null
//                                loadAds()
//                            }
//
//                            override fun onAdFailedToShowFullScreenContent(adError: AdError) {
//                                mInterstitialAd = null
//                                loadAds()
//                            }
//
//                            override fun onAdShowedFullScreenContent() {
//                                // Called when ad is shown.
//                            }
//                        }
//                    }
//                }
//            )
//        }
//    }

//    private fun loadAds() {
//        if (TEST) {
//            MobileAds.initialize(
//                this,
//                "ca-app-pub-3940256099942544~3347511713"
//            )
//            initInterstetial("ca-app-pub-3940256099942544/1033173712")
//            loadAdsNative("ca-app-pub-3940256099942544/6300978111")
//        } else {
//            MobileAds.initialize(
//                this,
//                "ca-app-pub-2572150356682336~9811262520"
//            )
//            initInterstetial("ca-app-pub-2572150356682336/6250279701")
//            loadAdsNative("ca-app-pub-2572150356682336/9707320742")
//        }
//    }

    private fun initView() {
        ivBg = findViewById(R.id.iv_bg)
        viewAds = findViewById(R.id.fl_ad_banner)
        findViewById<View>(R.id.iv_cutter).setOnClickListener { view: View? ->
            AmplitudeHelper.setMenuItemCutter()
            checkPermissionsAndOpenFragment(INDEX_CUTTER)
        }
        findViewById<View>(R.id.iv_speed).setOnClickListener { view: View? ->
            AmplitudeHelper.setMenuItemSpeed()
            checkPermissionsAndOpenFragment(INDEX_SPEED)
        }
        findViewById<View>(R.id.iv_merger).setOnClickListener { view: View? ->
            AmplitudeHelper.setMenuItemMerger()
            checkPermissionsAndOpenFragment(INDEX_MERGER)

        }
        findViewById<View>(R.id.iv_add_music).setOnClickListener { view: View? ->
            AmplitudeHelper.setMenuItemAddMusic()
            checkPermissionsAndOpenFragment(INDEX_ADD_MUSIC)

        }
        findViewById<View>(R.id.iv_studio).setOnClickListener { view: View? ->
            AmplitudeHelper.setMenuItemStudio()
            checkPermissionsAndOpenFragment(INDEX_STUDIO)
        }

        findViewById<View>(R.id.iv_more_app).setOnClickListener { view: View? ->
            AmplitudeHelper.setMenuItemEffects()
            checkPermissionsAndOpenFragment(INDEX_EFFECTS)
        }

        moreIv = findViewById(R.id.moreIv)


        val creditsString = "Credits" // resources.getString(com.createphoto.R.string.popup_menu_credits)
        val privacyPolicyString = "Privacy Policy" // resources.getString(com.createphoto.R.string.popup_menu_privacy_policy)
        moreIv?.setOnClickListener(View.OnClickListener { view ->
            val menu = PopupMenu(baseContext, view)
            menu.menu.add(creditsString)
            menu.menu.add(privacyPolicyString)
            if (BuildConfig.DEBUG) {
                // menu.menu.add(Const.POP_UP_EDIT_ACTIVITY)
                // menu.menu.add(Const.POP_UP_PHOTO_ACTIVITY)
                // menu.menu.add(Const.POP_UP_GLITCH_ACTIVITY)
            }
            menu.setOnMenuItemClickListener { item ->
                when (item.title.toString()) {
                    creditsString -> {
                        val creditsDialog = CreditsDialog(this@MainActivity) { }
                        creditsDialog.show()
                    }

                    privacyPolicyString -> {
                        UrlUtil.openLinkIfBrowserInstalled(
                            baseContext,
                            "https://gerrix90.github.io/SwD-Policy/privacy_policy_video_editor.html",
                            R.string.app_name // com.createphoto.R.string.no_browser
                        )
                    }

                    Const.POP_UP_EDIT_ACTIVITY -> //                                AmplitudeHelper.setMenuItemSpeed();
                        addFragmentListVideo(INDEX_EDIT_ACTIVITY, false)

                    Const.POP_UP_PHOTO_ACTIVITY -> {
                        // Launch simple photo editor
                        val intent = Intent(this@MainActivity, SimplePhotoEditActivity::class.java)
                        startActivity(intent)
                    }

                    Const.POP_UP_GLITCH_ACTIVITY -> {
                        // Glitch activity disabled  
                        Toast.makeText(this@MainActivity, "Glitch feature not available", Toast.LENGTH_SHORT).show()
                    }
                }
                true
            }
            menu.show()
        })
    }

    private fun addFragmentStudio(indexFragmentOpen: Int, isAlwayShowAds: Boolean) {
        FRAGMENT_INDEX = INDEX_STUDIO
        afterAdHanlder = Handler(Looper.getMainLooper())
        afterAdHanlder?.post {
            setVisibilityForRatingDialog(View.GONE)
            val bundle = Bundle()
            bundle.putString("title", "Studio")
            bundle.putInt(Statistic.CHECK_OPEN_STUDIO, Statistic.FROM_MAIN)
            bundle.putInt(Statistic.OPEN_FRAGMENT, indexFragmentOpen)
            supportFragmentManager.beginTransaction()
                .setCustomAnimations(
                    R.anim.animation_left_to_right,
                    R.anim.animation_right_to_left,
                    R.anim.animation_left_to_right,
                    R.anim.animation_right_to_left
                )
                .add(R.id.view_container, StudioFragment.newInstance(bundle))
                .addToBackStack(null)
                .commit()
        }
    }

    //    private void moreApp() {
    //        BUtils.showMoreAppDialog(getSupportFragmentManager());
    //    }
    private fun addFragmentListVideo(fragment: Int, isAlwaysShow: Boolean) {
        // Don't check permissions here - they should already be granted
        FRAGMENT_INDEX = fragment
        afterAdHanlder = Handler(Looper.getMainLooper())
        afterAdHanlder?.post {
            setVisibilityForRatingDialog(View.GONE)
//            moreIv!!.visibility = View.GONE
            val bundle = Bundle()
            bundle.putInt(Statistic.ACTION, fragment)
            supportFragmentManager.beginTransaction()
                .setCustomAnimations(
                    R.anim.animation_left_to_right,
                    R.anim.animation_right_to_left,
                    R.anim.animation_left_to_right,
                    R.anim.animation_right_to_left
                )
                .replace(R.id.view_container, ListVideoFragment.newInstance(bundle))
                .addToBackStack(null)
                .commitAllowingStateLoss()
        }

//        showInterstitial()

//        showFullAds(isAlwaysShow);
    }

//    private fun showInterstitial() {
//        if (mInterstitialAd != null) {
//            mInterstitialAd?.show(this)
//        } else {
//            checkRuntimePermission()
//        }
//    }

    private fun addFragmentMerger() {
        afterAdHanlder = Handler(Looper.getMainLooper())
        afterAdHanlder?.post {
            setVisibilityForRatingDialog(View.GONE)
            FabricEvents.logEvent(
                FabricEvents.MAIN_MANU_PRESSED,
                FabricEvents.MAIN_MENU_OPTION,
                "Merger"
            )
//            moreIv!!.visibility = View.GONE
            supportFragmentManager.beginTransaction()
                .setCustomAnimations(
                    R.anim.animation_left_to_right,
                    R.anim.animation_right_to_left,
                    R.anim.animation_left_to_right,
                    R.anim.animation_right_to_left
                )
                .add(R.id.view_container, DetailsSelectFileFragment.newInstance())
                .addToBackStack(null)
                .commitAllowingStateLoss()
        }

//        showInterstitial()
    } //    @Override

    //    public void onProductPurchased(@NonNull String productId, @Nullable PurchaseInfo details) {
    //
    //    }
    //
    //    @Override
    //    public void onPurchaseHistoryRestored() {
    //
    //    }
    //
    //    @Override
    //    public void onBillingError(int errorCode, @Nullable Throwable error) {
    //
    //    }
    //
    //    @Override
    //    public void onBillingInitialized() {
    //
    //        bp.subscribe(MainActivity.this, "premium");
    //
    //
    //        bp.getSubscriptionListingDetailsAsync("yearly", new BillingProcessor.ISkuDetailsResponseListener() {
    //            @Override
    //            public void onSkuDetailsResponse(@Nullable List<SkuDetails> products) {
    //                Log.d("ddd", "onSkuDetailsResponse: " + products.toString());
    //            }
    //
    //            @Override
    //            public void onSkuDetailsError(String error) {
    //
    //                Log.d("ddd", "onSkuDetailsError: " + error);
    //            }
    //        });
    //    }
    companion object {
        const val INDEX_CUTTER = 0
        const val INDEX_SPEED = 1
        const val INDEX_MERGER = 2
        const val INDEX_ADD_MUSIC = 3
        const val INDEX_EFFECTS = 4
        const val INDEX_EDIT_ACTIVITY = 5
        const val INDEX_STUDIO = 6
        
        const val REQUEST_VIDEO_PICK = 1001
        const val REQUEST_MULTIPLE_VIDEO_PICK = 1002

        @JvmField
        val TEST = BuildConfig.DEBUG
        var PERMISSTION_ALLOWED: Handler? = null
        fun deleteCache(context: Context?) {
            try {
                val dir = StorageUtils.getIndividualCacheDirectory(context)
                val deletedCache = deleteDir(dir)
                //            Log.d("Cache", "deleteCache: " + deletedCache);
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        fun deleteDir(dir: File?): Boolean {
            return if (dir != null && dir.isDirectory) {
                val children = dir.list()
                for (i in children.indices) {
                    val success =
                        deleteDir(
                            File(
                                dir,
                                children[i]
                            )
                        )
                    if (!success) {
                        return false
                    }
                }
                dir.delete()
            } else if (dir != null && dir.isFile) {
                dir.delete()
            } else {
                false
            }
        }
    }

    private fun openFragment(fragmentIndex: Int) {
        when (fragmentIndex) {
            INDEX_CUTTER, INDEX_SPEED, INDEX_ADD_MUSIC, INDEX_EFFECTS -> {
                // For single video selection modes, open picker directly
                pendingActionForVideoPicker = fragmentIndex
                openSingleVideoPicker()
            }
            INDEX_MERGER -> {
                // For merger, open multiple video picker
                pendingActionForVideoPicker = INDEX_MERGER
                openMultipleVideoPicker()
            }
            INDEX_STUDIO -> addFragmentStudio(0, true)
        }
    }
    
    private fun openSingleVideoPicker() {
        Timber.tag("DEBUG_FLOW").d("MainActivity: Opening single video picker for action: $pendingActionForVideoPicker")
        
        val intent = Intent(Intent.ACTION_GET_CONTENT).apply {
            type = "video/*"
            putExtra(Intent.EXTRA_LOCAL_ONLY, true)
            addCategory(Intent.CATEGORY_OPENABLE)
        }
        
        try {
            startActivityForResult(
                Intent.createChooser(intent, "Select Video"),
                REQUEST_VIDEO_PICK
            )
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e(e, "MainActivity: Error starting video picker")
            Toast.makeText(this, "No video app found", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun openMultipleVideoPicker() {
        Timber.tag("DEBUG_FLOW").d("MainActivity: Opening multiple video picker")
        
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            type = "video/*"
            putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
            addCategory(Intent.CATEGORY_OPENABLE)
        }
        
        try {
            startActivityForResult(intent, REQUEST_MULTIPLE_VIDEO_PICK)
        } catch (e: Exception) {
            Timber.tag("DEBUG_FLOW").e(e, "MainActivity: Error starting multiple video picker")
            Toast.makeText(this, "No video app found", Toast.LENGTH_SHORT).show()
        }
    }

    private fun checkPermissionsAndOpenFragment(fragmentIndex: Int) {
        // Check if we already have permissions
        if (hasRequiredPermissions()) {
            openFragment(fragmentIndex)
        } else {
            checkRuntimePermissions(object : PermissionCheckCallback {
                override fun onPermissionGranted() {
                    // Permissions granted, cache the status
                    cachePermissionStatus(true)
                    openFragment(fragmentIndex)
                }

                override fun onPermissionDenied() {
                    cachePermissionStatus(false)
                    showPermissionDeniedDialog()
                }
            })
        }
    }

    private fun checkRuntimePermissions(callback: PermissionCheckCallback) {
        val permissions: MutableList<String> = ArrayList()
        
        // Handle permissions based on Android version
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ requires granular media permissions
            // READ_MEDIA_IMAGES removed - using Intent-based image selection
            // READ_MEDIA_VIDEO removed - using Intent-based video selection
            permissions.add(Manifest.permission.READ_MEDIA_AUDIO)
        } else {
            // Android 12 and below
            permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
                permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            }
        }

        Dexter.withActivity(this)
            .withPermissions(permissions)
            .withListener(object : MultiplePermissionsListener {
                override fun onPermissionsChecked(report: MultiplePermissionsReport) {
                    when {
                        report.areAllPermissionsGranted() -> {
                            callback.onPermissionGranted()
                        }

                        report.isAnyPermissionPermanentlyDenied() -> {
                            showPermissionDeniedDialog()
                            callback.onPermissionDenied()
                        }

                        else -> {
                            Toast.makeText(
                                this@MainActivity,
                                getString(R.string.common_permission_required),
                                Toast.LENGTH_SHORT
                            ).show()
                            callback.onPermissionDenied()
                        }
                    }
                }

                override fun onPermissionRationaleShouldBeShown(
                    permissions: List<PermissionRequest>,
                    token: PermissionToken
                ) {
                    token.continuePermissionRequest()
                }
            }).check()
    }


    private fun showPermissionDeniedDialog() {
        Snackbar.make(
            findViewById(R.id.content),
            getResources().getString(R.string.common_permission_required),
            Snackbar.LENGTH_LONG
        )
            .setAction("OK") {
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                val uri = Uri.fromParts("package", packageName, null)
                intent.data = uri
                startActivity(intent)
            }
            .setActionTextColor(getResources().getColor(android.R.color.holo_red_light))
            .show()
    }

    private fun checkInitialPermissions() {
        if (!hasRequiredPermissions()) {
            checkRuntimePermissions(object : PermissionCheckCallback {
                override fun onPermissionGranted() {
                    cachePermissionStatus(true)
                }

                override fun onPermissionDenied() {
                    cachePermissionStatus(false)
                    showPermissionDeniedDialog()
                }
            })
        } else {
            // Permissions already granted, update cache
            cachePermissionStatus(true)
        }
    }
    
    private fun isPermissionCheckCached(): Boolean {
        val prefs = getSharedPreferences("permissions_cache", Context.MODE_PRIVATE)
        val lastCheckTime = prefs.getLong("last_permission_check_time", 0)
        val currentTime = System.currentTimeMillis()
        
        // Cache is valid for 24 hours
        return (currentTime - lastCheckTime) < (24 * 60 * 60 * 1000)
    }

    private fun hasRequiredPermissions(): Boolean {
        // Check actual permissions status
        val permissions = getRequiredPermissions()
        return permissions.all { permission ->
            ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
        }
    }

    private fun getRequiredPermissions(): List<String> {
        val permissions = mutableListOf<String>()
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permissions.add(Manifest.permission.READ_MEDIA_IMAGES)
            permissions.add(Manifest.permission.READ_MEDIA_VIDEO)
            permissions.add(Manifest.permission.READ_MEDIA_AUDIO)
        } else {
            permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
                permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            }
        }
        
        return permissions
    }

    private fun cachePermissionStatus(granted: Boolean) {
        val prefs = getSharedPreferences("permissions_cache", Context.MODE_PRIVATE)
        prefs.edit()
            .putBoolean("all_permissions_granted", granted)
            .putLong("last_permission_check_time", System.currentTimeMillis())
            .apply()
    }

    override fun onResume() {
        super.onResume()
        // Only check permissions if cache is expired or status changed
        if (!isPermissionCheckCached()) {
            val currentStatus = hasRequiredPermissions()
            val prefs = getSharedPreferences("permissions_cache", Context.MODE_PRIVATE)
            val cachedStatus = prefs.getBoolean("all_permissions_granted", false)
            
            // Only show dialog if permissions were revoked
            if (cachedStatus && !currentStatus) {
                checkInitialPermissions()
            } else if (currentStatus != cachedStatus) {
                cachePermissionStatus(currentStatus)
            }
        }
    }

}

interface PermissionCheckCallback {
    fun onPermissionGranted()
    fun onPermissionDenied()
}
