package com.videomontage.freeeditingapps.analytics;

import android.util.Log;

import com.videomontage.freeeditingapps.BuildConfig;

import java.util.Timer;
import java.util.TimerTask;

public class UserSessionHelper {
    private static final String TAG = UserSessionHelper.class.getSimpleName();

    private static final boolean DEBUG = BuildConfig.DEBUG;

    private static final int TIMEOUT_SECONDS = 5;

    public interface OnUserSessionEnd {
        public void onSessionEnd(long sessionDurationinMillis);
    }

    protected OnUserSessionEnd onSessionEnd;

    protected long sessionStart = 0;
    protected int resumedActivities = 0;

    protected Timer timer = null;

    public UserSessionHelper(OnUserSessionEnd onSessionEnd) {
        this.onSessionEnd = onSessionEnd;
    }

    class EndSessionTask extends TimerTask {
        public void run() {
            if (DEBUG) {
                Log.d(TAG, "Session ended!");
            }

            timer.cancel();
            timer = null;

            long duration = System.currentTimeMillis() - sessionStart - TIMEOUT_SECONDS*1000;
            sessionStart = 0;

            onSessionEnd.onSessionEnd(duration);

        }
    }
    public void clearTimeout() {
        if (DEBUG) {
            Log.d(TAG, "Cancelling the session ending timer!");
        }

        if (timer != null) {
            timer.cancel();
            timer.purge();
            timer = null;
        }
    }

    public void setTimeout(int seconds) {
        timer = new Timer();
        timer.schedule(new EndSessionTask(), seconds * 1000);
    }

    public void onActivityResumed() {
        if (DEBUG) {
            Log.d(TAG, "Activity comes to foreground! " + resumedActivities);
        }

        clearTimeout();

        if (sessionStart == 0) {
            sessionStart = System.currentTimeMillis();
        }
        resumedActivities++;

    }

    public void onActivityPaused() {
        if (DEBUG) {
            Log.d(TAG, "Activity goes to background! " + resumedActivities);
        }

        resumedActivities--;

        if (resumedActivities == 0) {
            setTimeout(TIMEOUT_SECONDS);
        }
    }
}
