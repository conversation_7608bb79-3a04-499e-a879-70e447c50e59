package com.videomontage.freeeditingapps.analytics;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.util.Log;
import android.widget.Toast;

import com.android.volley.DefaultRetryPolicy;
import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.Response;
import com.android.volley.RetryPolicy;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;
import com.videomontage.freeeditingapps.activity.MainActivity;
import com.videomontage.freeeditingapps.application.MyApplication;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

public class AnalyticsVolley {

    public static void sendAnalytics(Context context) {
        if (MainActivity.TEST)return;

        new Thread(new Runnable() {
            @Override
            public void run() {
                if (isInternetAvailable(context)){
                    String evetns = AnalyticsSharedPref.getPreference(context);
                    if (evetns.equalsIgnoreCase(""))return;

                    AnalyticsVolley.addItemToSheet(context, UniqueId.id(context), evetns);
                    AnalyticsSharedPref.setPreference(context, "");
                    MyApplication.ANALYTICS.clear();
                }
            }
        }).start();

    }


    private static void   addItemToSheet(Context context, String userID, String events ) {


        StringRequest stringRequest = new StringRequest(Request.Method.POST, "https://script.google.com/macros/s/AKfycbxN4UpHwBuXFw1HY8oHY1em65ssJJ6wnKoBbqisDlMvcKMTwFs/exec",
                new Response.Listener<String>() {
                    @Override
                    public void onResponse(String response) {


//                        Toast.makeText(context, "Successfully posted data!", Toast.LENGTH_SHORT).show();
                    }
                },
                new Response.ErrorListener() {
                    @Override
                    public void onErrorResponse(VolleyError error) {

                    }
                }
        ) {
            @Override
            protected Map<String, String> getParams() {
                Map<String, String> parmas = new HashMap<>();

                //here we pass params
                parmas.put("action","addItem");
                parmas.put("itemName",userID);
                parmas.put("brand",events);

                return parmas;
            }
        };

        int socketTimeOut = 50000;// u can change this .. here it is 50 seconds

        RetryPolicy retryPolicy = new DefaultRetryPolicy(socketTimeOut, 0, DefaultRetryPolicy.DEFAULT_BACKOFF_MULT);
        stringRequest.setRetryPolicy(retryPolicy);

        RequestQueue queue = Volley.newRequestQueue(context);

        queue.add(stringRequest);


    }

    public static boolean isInternetAvailable(Context context) {
            ConnectivityManager cm = (ConnectivityManager)context
                    .getSystemService(Context.CONNECTIVITY_SERVICE);

            NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
            if (activeNetwork != null && activeNetwork.isConnected()) {
                try {
                    URL url = new URL("http://www.google.com/");
                    HttpURLConnection urlc = (HttpURLConnection)url.openConnection();
                    urlc.setRequestProperty("User-Agent", "test");
                    urlc.setRequestProperty("Connection", "close");
                    urlc.setConnectTimeout(1000); // mTimeout is in seconds
                    urlc.connect();
                    if (urlc.getResponseCode() == 200) {
                        return true;
                    } else {
                        return false;
                    }
                } catch (IOException e) {
                    Log.i("warning", "Error checking internet connection", e);
                    return false;
                }
            }

            return false;

    }
}
