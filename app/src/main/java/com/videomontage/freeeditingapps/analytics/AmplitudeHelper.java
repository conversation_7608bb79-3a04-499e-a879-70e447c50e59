package com.videomontage.freeeditingapps.analytics;

import com.amplitude.api.Amplitude;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

public class AmplitudeHelper {

    public static final String APP_OPENED = "App opened";
    public static final String MENU_ITEM_CUTTER = "Cutter - Menu Item";
    public static final String MENU_ITEM_EDIT_ACTIVITY = "Edit Activity - Menu Item";
    public static final String CUTTER_OPENED = "Cutter Opened";
    public static final String MENU_ITEM_SPEED = "Speed - Menu Item";
    public static final String SPEED_OPENED = "Speed Opened";
    public static final String MENU_ITEM_MERGER = "Merger - Menu Item";
    public static final String MERGER_OPENED = "Merger Opened";
    public static final String MENU_ITEM_ADD_MUSIC = "Add Music - Menu Item";
    public static final String ADD_MUSIC_OPENED = "Add Music Opened";
    public static final String MENU_ITEM_STUDIO = "Studio - Menu Item";
    public static final String STUDIO_OPENED = "Studio Opened";
    public static final String MENU_ITEM_EFFECTS = "Effects - Menu Item";
    public static final String EFFECTS_OPENED = "Effects Opened";
    public static final String FRAGMENT_LIST_VIDEO_OPENED = "Fragment - List Video";
    public static final String FRAGMENT_LIST_VIDEO_SELECTED = "Selected video";
    public static final String SAVE = "Save";
    public static final String OPEN_VIDEO_WITH = "Open video with";
    public static final String RENAME_VIDEO_FILE = "Rename video file";
    public static final String DETAIL_VIDEO = "Detail Video";
    public static final String DELETE_VIDEO = "Delete Video";
    public static final String SHARE_VIDEO = "Share";


    public static void setAppOpened() {
        FirebaseCrashlytics.getInstance().setCustomKey("device_id", Amplitude.getInstance().getDeviceId());
        logAmplitudeEvent(APP_OPENED);
    }

    public static void setMenuItemCutter() {
        logAmplitudeEvent(MENU_ITEM_CUTTER);
    }

    public static void setMenuItemEditActivity() {
        logAmplitudeEvent(MENU_ITEM_EDIT_ACTIVITY);
    }

    public static void setCutterOpened(){
        logAmplitudeEvent(CUTTER_OPENED);
    }

    public static void setMenuItemSpeed() {
        logAmplitudeEvent(MENU_ITEM_SPEED);
    }

    public static void setSpeedOpened(){
        logAmplitudeEvent(SPEED_OPENED);
    }

    public static void setMenuItemMerger() {
        logAmplitudeEvent(MENU_ITEM_MERGER);
    }

    public static void setMergerOpened(){
        logAmplitudeEvent(MERGER_OPENED);
    }

    public static void setMenuItemAddMusic() {
        logAmplitudeEvent(MENU_ITEM_ADD_MUSIC);
    }

    public static void setAddMusicOpened(){
        logAmplitudeEvent(ADD_MUSIC_OPENED);
    }

    public static void setMenuItemStudio() {
        logAmplitudeEvent(MENU_ITEM_STUDIO);
    }

    public static void setStudioOpened() {
        logAmplitudeEvent(STUDIO_OPENED);
    }

    public static void setMenuItemEffects() {
        logAmplitudeEvent(MENU_ITEM_EFFECTS);
    }

    public static void setEffectsOpened(){
        logAmplitudeEvent(EFFECTS_OPENED);
    }

    public static void setFragmentListVideo(){
        logAmplitudeEvent(FRAGMENT_LIST_VIDEO_OPENED);
    }

    public static void setFragmentListVideoSelected(){
        logAmplitudeEvent(FRAGMENT_LIST_VIDEO_SELECTED);
    }

    public static void setSave(){
        logAmplitudeEvent(SAVE);
    }

    public static void setOpenVideoWith(){
        logAmplitudeEvent(OPEN_VIDEO_WITH);
    }

    public static void setRenameVideoFile(){
        logAmplitudeEvent(RENAME_VIDEO_FILE);
    }

    public static void setDetailVideo(){
        logAmplitudeEvent(DETAIL_VIDEO);
    }

    public static void setDeleteVideo(){
        logAmplitudeEvent(DELETE_VIDEO);
    }

    public static void setShareVideo(){
        logAmplitudeEvent(SHARE_VIDEO);

    }

    public static void setEffectName(String effectName){
        logAmplitudeEvent(effectName);
    }

    private static void logAmplitudeEvent(String event) {
        Amplitude.getInstance().logEvent(event);
        FirebaseCrashlytics.getInstance().log(event);

    }
}
