package com.videomontage.freeeditingapps.analytics;

import com.amplitude.api.Amplitude;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;

public class FragmentAnalytics {

    private String fragmentName;
    private long duration;
    private HashMap<String, String> clicks = new HashMap<>();

    public FragmentAnalytics(String fragmentName) {
        this.fragmentName = fragmentName;
    }

    public void addClick(String click) {
        SimpleDateFormat s = new SimpleDateFormat("hh:mm:ss");
        String format = s.format(new Date());
        this.clicks.put(format, click);
    }

    public boolean isClicksEmpty(){
        return clicks.isEmpty();
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }



}
