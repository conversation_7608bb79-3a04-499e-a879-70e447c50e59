package com.videomontage.freeeditingapps.analytics;

import android.content.Context;
import android.content.SharedPreferences;

import java.util.UUID;

public class UniqueId {

    private static String sID = null;
    private static final String SHARED_PREF_KEY = "ANALYTICS_USER_ID";
    private static final String ID_KEY = "id";

    public synchronized static String id(Context context) {
        if (sID == null) {
            SharedPreferences pref = context.getSharedPreferences(
                    SHARED_PREF_KEY, 0);

            sID = pref.getString(ID_KEY, "");

            if (sID.equalsIgnoreCase("")) {
                sID = generateAndStoreUserId(pref);
            }

        }
        return sID;
    }

    private synchronized static String generateAndStoreUserId(SharedPreferences pref) {
        String id = UUID.randomUUID().toString();
        SharedPreferences.Editor editor = pref.edit();
        editor.putString(ID_KEY, id);
        editor.apply();
        return id;
    }

}