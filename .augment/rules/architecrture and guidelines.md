---
type: "always_apply"
---

  🏗️ Architecture Requirements

  Clean Architecture + MVVM Pattern with strict layer separation:

  - Presentation Layer: Jetpack Compose UI with ViewModels (ui/ package)
  - Domain Layer: Business logic and use cases (domain/ package)
  - Data Layer: Repository implementations (data/ package)
  - Package Structure: Feature-based organization under soly.lyricsgenerator

  🔴 Critical Code Standards (ZERO TOLERANCE)

  1. No Force Unwrapping: Never use !! - always use ?. and ?:
  2. No When Expressions: Use sealed classes with polymorphic dispatch
  3. No Hardcoded Strings: UI text via stringResource(), constants for keys
  4. Analytics Constants: Use AnalyticsConstants.kt - never hardcode analytics strings
  5. Hilt DI: All ViewModels with @HiltViewModel, constructor injection only
  6. Timber Logging: MANDATORY DEBUG_FLOW tag format: Timber.tag("DEBUG_FLOW").d("ClassName: message")

  🔄 Development Workflow

  1. Domain First: Create use cases and repository interfaces
  2. Data Layer: Implement repositories with Room/API
  3. Presentation: ViewModels with sealed state classes, Compose UI
  4. Testing: Unit + UI + Integration tests (all must pass)
  5. Constants: Extract strings to resources

  🧪 Testing Requirements

  MANDATORY: All three test types must pass:
  - Unit Tests (ViewModels, use cases, business logic)
  - UI Tests (Compose components and screens)
  - Integration Tests (Complete user flows end-to-end)

  🔧 Key Technologies

  - Jetpack Compose (Material3)
  - Hilt (DI)
