# Video Editor Project Rules

## Critical Implementation Paths

### ExoPlayer Migration Pattern
When updating video playback code:
```java
// OLD - Don't use
SimpleExoPlayer player = ExoPlayerFactory.newSimpleInstance(context);
ExtractorMediaSource mediaSource = new ExtractorMediaSource(...);

// NEW - Use this pattern
ExoPlayer player = new ExoPlayer.Builder(context).build();
MediaItem mediaItem = MediaItem.fromUri(uri);
MediaSource mediaSource = new ProgressiveMediaSource.Factory(dataSourceFactory).createMediaSource(mediaItem);
```

### Module Dependencies
Before working on features, check settings.gradle:
- Active modules: app, bettervideoplayer, photoeditor
- Disabled modules are commented out with reasons
- Don't uncomment modules without fixing their dependencies first

### Repository Configuration
Never add J<PERSON><PERSON> or Bintray - they're shutdown. Use:
- google()
- mavenCentral()
- maven { url "https://jitpack.io" }

## User Workflow Patterns

### Build Commands
User typically runs:
1. `./gradlew clean` when switching branches
2. `./gradlew assembleDebug` for testing
3. Full clean build: `./gradlew clean assembleDebug`

### Testing Approach
- No automated tests currently
- Manual testing on physical devices preferred
- Focus on video export functionality first

## Project-Specific Patterns

### Custom Components
1. **BubbleSeekBar** - Internalized library
   - Attributes in res/values/attrs_bubbleseekbar.xml
   - Don't look for external dependency

2. **FFmpeg Commands** - All in ComModel class
   - Don't modify command strings without testing
   - Each effect has specific parameter requirements

### Error Handling
- Video processing errors often silent
- Always check FFmpeg return codes
- Log extensively during development

## Known Challenges

### Memory Management
- Large videos can cause OOM
- Always release ExoPlayer resources
- Clean up temporary files after processing

### Threading
- FFmpeg runs on background thread
- UI updates must post to main thread
- Progress callbacks can be frequent

### File Permissions
- Always check storage permissions
- Use SAF for Android 10+
- Media scanner refresh required after export

## Evolution of Decisions

### Dependency Management
1. Started with JCenter/Bintray dependencies
2. Discovered they were shutdown
3. Migrated to Maven Central
4. Internalized unavailable libraries

### Feature Prioritization
1. Initially tried to fix all modules
2. Realized some dependencies unavailable
3. Focused on core video editing features
4. Disabled non-essential modules

### API Modernization
1. Found extensive deprecated API usage
2. Updated critical APIs (ExoPlayer)
3. Left non-blocking deprecations for later
4. Prioritized buildability over warnings

## Tool Usage Patterns

### Gradle
- Wrapper included, use ./gradlew
- Requires Java 21 for Gradle 8.1.1
- Clean builds recommended after major changes

### Android Studio
- Import as Gradle project
- Sync required after settings.gradle changes
- Build variants: debug/release

### Git
- Currently on 'refactoring' branch
- Main branch is 'master'
- Local changes not committed during fixes

## Development Tips

### Quick Wins
- Comment out problematic code rather than delete
- Use Toast messages for disabled features
- Focus on one module at a time

### Debugging
- Check logcat for FFmpeg output
- ExoPlayer has verbose logging option
- Firebase Crashlytics captures crashes

### Performance
- Test on mid-range devices
- Monitor memory during video processing
- Profile export times vs video length

Remember: This is a working app with real users. Preserve existing functionality while modernizing.