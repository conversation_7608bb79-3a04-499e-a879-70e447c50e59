🏗️ Architecture Requirements

Clean Architecture + MVVM Pattern with strict layer separation:

- Presentation Layer: Jetpack Compose UI with ViewModels (ui/ package)
- Domain Layer: Business logic and use cases (domain/ package)
- Data Layer: Repository implementations (data/ package)
- Package Structure: Feature-based organization under com.videomontage.freeeditingapps

🔴 Critical Code Standards (ZERO TOLERANCE)

1. No Force Unwrapping: Never use !! - always use ?. and ?:
2. No When Expressions: Use sealed classes with polymorphic dispatch
3. No Hardcoded Strings: UI text via stringResource(), constants for keys
4. Analytics Constants: Use AnalyticsConstants.kt - never hardcode analytics strings
5. Hilt DI: All ViewModels with @HiltViewModel, constructor injection only
6. Timber Logging: MANDATORY DEBUG_FLOW tag format: Timber.tag("DEBUG_FLOW").d("ClassName: message")

🔄 Development Workflow

1. Domain First: Create use cases and repository interfaces
2. Data Layer: Implement repositories with Room/API
3. Presentation: ViewModels with sealed state classes, Compose UI
4. Testing: Unit + UI + Integration tests (all must pass)
5. Constants: Extract strings to resources

🧪 Testing Requirements

MANDATORY: All three test types must pass:
- Unit Tests (ViewModels, use cases, business logic)
- UI Tests (Compose components and screens)
- Integration Tests (Complete user flows end-to-end)

🔧 Key Technologies

- Jetpack Compose (Material3)
- Hilt (DI)
- Room (Database)
- Navigation Compose
- Firebase (Analytics, Crashlytics)
- ExoPlayer (Video Playback)
- FFmpeg (Video Processing)

📋 Task Completion Policy

CRITICAL: Never mark tasks complete without user approval. Always present solution and wait for explicit user confirmation before marking anything as done.

📦 True Feature-Based Clean Architecture Structure

Each feature is completely self-contained with its own Clean Architecture layers:

com.videomontage.freeeditingapps/
├── features/                    # Self-Contained Feature Modules
│   ├── cutter/                  # Video Cutter Feature
│   │   ├── data/
│   │   │   └── repository/
│   │   │       └── CutterRepositoryImpl.kt
│   │   ├── domain/
│   │   │   ├── model/
│   │   │   │   ├── CutterConfig.kt
│   │   │   │   └── TimeRange.kt
│   │   │   ├── repository/
│   │   │   │   └── CutterRepository.kt
│   │   │   └── usecase/
│   │   │       ├── CutVideoUseCase.kt
│   │   │       └── ValidateTimeRangeUseCase.kt
│   │   ├── presentation/
│   │   │   ├── CutterScreen.kt
│   │   │   ├── CutterViewModel.kt
│   │   │   ├── state/
│   │   │   │   └── CutterUiState.kt
│   │   │   └── components/
│   │   │       ├── TimelineSlider.kt
│   │   │       └── CutterControls.kt
│   │   └── di/
│   │       └── CutterModule.kt
│   │
│   ├── speed/                   # Video Speed Feature
│   │   ├── data/
│   │   │   └── repository/
│   │   │       └── SpeedRepositoryImpl.kt
│   │   ├── domain/
│   │   │   ├── model/
│   │   │   │   └── SpeedConfig.kt
│   │   │   ├── repository/
│   │   │   │   └── SpeedRepository.kt
│   │   │   └── usecase/
│   │   │       └── ChangeSpeedUseCase.kt
│   │   ├── presentation/
│   │   │   ├── SpeedScreen.kt
│   │   │   ├── SpeedViewModel.kt
│   │   │   ├── state/
│   │   │   │   └── SpeedUiState.kt
│   │   │   └── components/
│   │   │       └── SpeedSelector.kt
│   │   └── di/
│   │       └── SpeedModule.kt
│   │
│   ├── merger/                  # Video Merger Feature
│   │   ├── data/
│   │   │   └── repository/
│   │   │       └── MergerRepositoryImpl.kt
│   │   ├── domain/
│   │   │   ├── model/
│   │   │   │   └── MergeConfig.kt
│   │   │   ├── repository/
│   │   │   │   └── MergerRepository.kt
│   │   │   └── usecase/
│   │   │       └── MergeVideosUseCase.kt
│   │   ├── presentation/
│   │   │   ├── MergerScreen.kt
│   │   │   ├── MergerViewModel.kt
│   │   │   ├── state/
│   │   │   │   └── MergerUiState.kt
│   │   │   └── components/
│   │   │       └── VideoOrderList.kt
│   │   └── di/
│   │       └── MergerModule.kt
│   │
│   ├── music/                   # Add Music Feature
│   │   ├── data/
│   │   │   └── repository/
│   │   │       └── MusicRepositoryImpl.kt
│   │   ├── domain/
│   │   │   ├── model/
│   │   │   │   ├── AudioFile.kt
│   │   │   │   └── MusicConfig.kt
│   │   │   ├── repository/
│   │   │   │   └── MusicRepository.kt
│   │   │   └── usecase/
│   │   │       ├── AddMusicUseCase.kt
│   │   │       └── GetAudioFilesUseCase.kt
│   │   ├── presentation/
│   │   │   ├── MusicScreen.kt
│   │   │   ├── MusicViewModel.kt
│   │   │   ├── state/
│   │   │   │   └── MusicUiState.kt
│   │   │   └── components/
│   │   │       ├── MusicPicker.kt
│   │   │       └── AudioWaveform.kt
│   │   └── di/
│   │       └── MusicModule.kt
│   │
│   ├── effects/                 # Video Effects Feature
│   │   ├── data/
│   │   │   └── repository/
│   │   │       └── EffectsRepositoryImpl.kt
│   │   ├── domain/
│   │   │   ├── model/
│   │   │   │   ├── Effect.kt
│   │   │   │   └── FilterConfig.kt
│   │   │   ├── repository/
│   │   │   │   └── EffectsRepository.kt
│   │   │   └── usecase/
│   │   │       ├── ApplyEffectUseCase.kt
│   │   │       └── GetEffectsUseCase.kt
│   │   ├── presentation/
│   │   │   ├── EffectsScreen.kt
│   │   │   ├── EffectsViewModel.kt
│   │   │   ├── state/
│   │   │   │   └── EffectsUiState.kt
│   │   │   └── components/
│   │   │       ├── FilterGrid.kt
│   │   │       └── EffectPreview.kt
│   │   └── di/
│   │       └── EffectsModule.kt
│   │
│   └── photoedit/              # Photo Edit Feature
│       ├── data/
│       │   └── repository/
│       │       └── PhotoEditRepositoryImpl.kt
│       ├── domain/
│       │   ├── model/
│       │   │   └── PhotoEditConfig.kt
│       │   ├── repository/
│       │   │   └── PhotoEditRepository.kt
│       │   └── usecase/
│       │       └── EditPhotoUseCase.kt
│       ├── presentation/
│       │   ├── PhotoEditScreen.kt
│       │   ├── PhotoEditViewModel.kt
│       │   ├── state/
│       │   │   └── PhotoEditUiState.kt
│       │   └── components/
│       │       └── PhotoCanvas.kt
│       └── di/
│           └── PhotoEditModule.kt
│
├── ui/                          # Global UI Layer (Non-Feature Specific)
│   ├── main/                    # Main Navigation
│   │   ├── MainActivity.kt
│   │   ├── MainScreen.kt
│   │   ├── MainViewModel.kt
│   │   └── components/
│   │       └── FeatureGrid.kt
│   ├── components/              # Shared UI Components
│   │   ├── LoadingComponent.kt
│   │   ├── ErrorComponent.kt
│   │   └── VideoPreview.kt
│   ├── theme/                   # Material 3 Theme
│   │   ├── Theme.kt
│   │   ├── Color.kt
│   │   └── Typography.kt
│   └── navigation/              # App Navigation
│       └── AppNavigation.kt
│
├── domain/                      # Shared Domain (Cross-Feature)
│   ├── usecase/
│   │   ├── GetVideosUseCase.kt
│   │   └── SaveVideoUseCase.kt
│   ├── repository/
│   │   └── VideoRepository.kt
│   └── model/
│       ├── VideoModel.kt
│       ├── ProcessingResult.kt
│       └── VideoCommand.kt
│
├── data/                        # Shared Data Layer
│   ├── repository/
│   │   └── VideoRepositoryImpl.kt
│   ├── local/
│   │   ├── database/
│   │   │   ├── VideoDatabase.kt
│   │   │   └── VideoDao.kt
│   │   └── entity/
│   │       └── VideoEntity.kt
│   ├── remote/
│   │   └── api/
│   │       └── VideoApiService.kt
│   └── datasource/
│       ├── VideoLocalDataSource.kt
│       └── MediaStoreDataSource.kt
│
├── di/                          # Global Dependency Injection
│   ├── DatabaseModule.kt
│   ├── RepositoryModule.kt
│   └── UseCaseModule.kt
│
├── analytics/                   # Analytics Infrastructure
│   ├── AnalyticsConstants.kt
│   └── AnalyticsManager.kt
│
└── utils/                       # Utility Classes
    ├── FileUtils.kt
    └── VideoUtils.kt

🔑 Key Differences from Incorrect Structure

❌ What Was Wrong Before
- Features split across layers instead of being self-contained
- No feature-specific dependency injection modules
- Missing state management classes in presentation layer
- No clear separation between shared and feature-specific code

✅ What's Correct Now
1. Self-Contained Features: Each feature has its complete Clean Architecture stack
2. Feature-Specific DI: Each feature manages its own dependencies via di/ module
3. State Management: UI state classes for complex feature states in presentation/state/
4. Clear Separation: Shared code vs feature code is clearly distinguished
5. Complete Isolation: Features can be developed/tested independently

🏛️ Architecture Benefits
- Feature Independence: Each feature is completely self-contained
- Clean Architecture: Proper layer separation within each feature
- Scalability: Easy to add new features following the same pattern
- Testability: Each feature has its own test structure
- Team Collaboration: Different teams can work on different features independently

🎯 Migration Phases

Phase 1: Setup and Dependencies (Week 1-2)

1.1 Git Strategy
```bash
# Create main migration branch from master
git checkout master
git pull origin master
git checkout -b feature/compose-migration
git push -u origin feature/compose-migration

# Create Phase 1 branch
git checkout -b feature/compose-setup
git push -u origin feature/compose-setup
```

1.2 Dependencies Setup
- Add Compose BOM and core dependencies
- Configure build features and compiler options
- Add Hilt dependency injection
- Create Compose theme and design system
- Set up Compose navigation

1.3 Architecture Foundation
- Create Feature-Based Package Structure: Reorganize into feature-specific folders (cutter/, speed/, merger/, music/, effects/, photoedit/)
- Configure @HiltAndroidApp in MyApplication
- Create AnalyticsConstants.kt for all analytics strings
- Eliminate all !! operators - replace with ?. and ?:
- Replace all when expressions with sealed classes using polymorphic dispatch

Phase 2: Core Components Migration (Week 3)

2.1 Domain Layer First (Following Domain First Approach)
```kotlin
// domain/usecase/GetVideosUseCase.kt
class GetVideosUseCase @Inject constructor(
    private val videoRepository: VideoRepository
) {
    suspend operator fun invoke(): Flow<List<VideoModel>> = 
        videoRepository.getVideos()
}

// domain/repository/VideoRepository.kt
interface VideoRepository {
    suspend fun getVideos(): Flow<List<VideoModel>>
    suspend fun processVideo(command: VideoCommand): Result<String>
}
```

2.2 Sealed Classes for Commands
```kotlin
sealed class VideoCommand {
    abstract suspend fun execute(): Result<String>
    
    data class CutterCommand(
        val startTime: Long,
        val endTime: Long,
        val inputPath: String
    ) : VideoCommand() {
        override suspend fun execute(): Result<String> {
            return FFmpegProcessor.cut(startTime, endTime, inputPath)
        }
    }
}
```

Phase 3: Fragment Migration (Week 4-5)

3.1 ViewModels with Sealed State Classes
```kotlin
@HiltViewModel
class VideoListViewModel @Inject constructor(
    private val getVideosUseCase: GetVideosUseCase
) : ViewModel() {
    
    sealed class UiState {
        @Composable
        abstract fun render()
        
        object Loading : UiState() {
            @Composable
            override fun render() {
                LoadingComponent()
            }
        }
        
        data class Success(val videos: List<VideoModel>) : UiState() {
            @Composable
            override fun render() {
                VideoList(videos)
            }
        }
        
        data class Error(val message: String) : UiState() {
            @Composable
            override fun render() {
                ErrorComponent(message)
            }
        }
    }
}
```

#### 3.2 Compose Screens
```kotlin
@Composable
fun VideoListScreen(
    viewModel: VideoListViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    // ✅ CORRECT: Polymorphic dispatch instead of when
    uiState.render()
}
```

### Phase 4: Complex UI Migration (Week 6-8)

#### 4.1 Navigation with Sealed Classes
```kotlin
sealed class Screen {
    abstract val route: String
    
    @Composable
    abstract fun content()
    
    object Main : Screen() {
        override val route = "main"
        
        @Composable
        override fun content() {
            MainScreen()
        }
    }
}
```

#### 4.2 Dialog State Management
```kotlin
sealed class DialogState {
    @Composable
    abstract fun render()
    
    object None : DialogState() {
        @Composable
        override fun render() {
            // No dialog to render
        }
    }
    
    data class Error(val message: String) : DialogState() {
        @Composable
        override fun render() {
            ErrorDialog(message)
        }
    }
}
```

### Phase 5: Integration and Testing (Week 9-10)

#### 5.1 Mandatory Test Suite (ALL MUST PASS)
```kotlin
// Unit Tests - ViewModels, use cases, business logic
@Test
fun `getVideos returns success state`() {
    // Test use case logic
}

// UI Tests - Compose components and screens  
@Test
fun `video list displays correctly`() {
    // Test Compose UI
}

// Integration Tests - Complete user flows end-to-end
@Test
fun `video editing flow works end to end`() {
    // Test complete user journey
}
```

## 🔄 Development Workflow

1. **Domain First**: Create use cases and repository interfaces
2. **Data Layer**: Implement repositories with Room/API
3. **Presentation**: ViewModels with sealed state classes, Compose UI
4. **Testing**: Unit + UI + Integration tests (all must pass)
5. **Constants**: Extract strings to resources

## 📦 Target Package Structure - FEATURE-BASED ORGANIZATION

### Complete Feature-Based Structure
```
com.videomontage.freeeditingapps/
├── ui/                          # Presentation Layer (Feature-Based)
│   ├── cutter/                  # Video Cutter Feature
│   │   ├── CutterScreen.kt
│   │   ├── CutterViewModel.kt
│   │   └── components/
│   │       ├── TimelineSlider.kt
│   │       └── CutterControls.kt
│   ├── speed/                   # Video Speed Feature
│   │   ├── SpeedScreen.kt
│   │   ├── SpeedViewModel.kt
│   │   └── components/
│   │       └── SpeedSelector.kt
│   ├── merger/                  # Video Merger Feature
│   │   ├── MergerScreen.kt
│   │   ├── MergerViewModel.kt
│   │   └── components/
│   │       └── VideoOrderList.kt
│   ├── music/                   # Add Music Feature
│   │   ├── MusicScreen.kt
│   │   ├── MusicViewModel.kt
│   │   └── components/
│   │       ├── MusicPicker.kt
│   │       └── AudioWaveform.kt
│   ├── effects/                 # Video Effects Feature
│   │   ├── EffectsScreen.kt
│   │   ├── EffectsViewModel.kt
│   │   └── components/
│   │       ├── FilterGrid.kt
│   │       └── EffectPreview.kt
│   ├── photoedit/              # Photo Edit Feature
│   │   ├── PhotoEditScreen.kt
│   │   ├── PhotoEditViewModel.kt
│   │   └── components/
│   │       └── PhotoCanvas.kt
│   ├── main/                    # Main Navigation Feature
│   │   ├── MainScreen.kt
│   │   ├── MainViewModel.kt
│   │   └── components/
│   │       └── FeatureGrid.kt
│   └── common/                  # Shared UI Components
│       ├── components/
│       │   ├── LoadingComponent.kt
│       │   ├── ErrorComponent.kt
│       │   └── VideoPreview.kt
│       ├── theme/
│       │   ├── Theme.kt
│       │   ├── Color.kt
│       │   └── Typography.kt
│       └── navigation/
│           └── AppNavigation.kt
├── domain/                      # Business Logic Layer (Feature-Based)
│   ├── cutter/
│   │   ├── usecase/
│   │   │   ├── CutVideoUseCase.kt
│   │   │   └── ValidateTimeRangeUseCase.kt
│   │   ├── repository/
│   │   │   └── CutterRepository.kt
│   │   └── model/
│   │       └── CutterConfig.kt
│   ├── speed/
│   │   ├── usecase/
│   │   │   └── ChangeSpeedUseCase.kt
│   │   ├── repository/
│   │   │   └── SpeedRepository.kt
│   │   └── model/
│   │       └── SpeedConfig.kt
│   ├── merger/
│   │   ├── usecase/
│   │   │   └── MergeVideosUseCase.kt
│   │   ├── repository/
│   │   │   └── MergerRepository.kt
│   │   └── model/
│   │       └── MergeConfig.kt
│   ├── music/
│   │   ├── usecase/
│   │   │   ├── AddMusicUseCase.kt
│   │   │   └── GetAudioFilesUseCase.kt
│   │   ├── repository/
│   │   │   └── MusicRepository.kt
│   │   └── model/
│   │       ├── AudioFile.kt
│   │       └── MusicConfig.kt
│   ├── effects/
│   │   ├── usecase/
│   │   │   ├── ApplyEffectUseCase.kt
│   │   │   └── GetEffectsUseCase.kt
│   │   ├── repository/
│   │   │   └── EffectsRepository.kt
│   │   └── model/
│   │       ├── Effect.kt
│   │       └── FilterConfig.kt
│   ├── photoedit/
│   │   ├── usecase/
│   │   │   └── EditPhotoUseCase.kt
│   │   ├── repository/
│   │   │   └── PhotoEditRepository.kt
│   │   └── model/
│   │       └── PhotoEditConfig.kt
│   └── common/                  # Shared Domain Logic
│       ├── usecase/
│       │   ├── GetVideosUseCase.kt
│       │   └── SaveVideoUseCase.kt
│       ├── repository/
│       │   └── VideoRepository.kt
│       └── model/
│           ├── VideoModel.kt
│           ├── ProcessingResult.kt
│           └── VideoCommand.kt
└── data/                        # Data Layer (Feature-Based)
    ├── cutter/
    │   └── repository/
    │       └── CutterRepositoryImpl.kt
    ├── speed/
    │   └── repository/
    │       └── SpeedRepositoryImpl.kt
    ├── merger/
    │   └── repository/
    │       └── MergerRepositoryImpl.kt
    ├── music/
    │   └── repository/
    │       └── MusicRepositoryImpl.kt
    ├── effects/
    │   └── repository/
    │       └── EffectsRepositoryImpl.kt
    ├── photoedit/
    │   └── repository/
    │       └── PhotoEditRepositoryImpl.kt
    ├── common/                  # Shared Data Logic
    │   ├── repository/
    │   │   └── VideoRepositoryImpl.kt
    │   ├── local/
    │   │   ├── database/
    │   │   │   ├── VideoDatabase.kt
    │   │   │   └── VideoDao.kt
    │   │   └── entity/
    │   │       └── VideoEntity.kt
    │   ├── remote/
    │   │   └── api/
    │   │       └── VideoApiService.kt
    │   └── datasource/
    │       ├── VideoLocalDataSource.kt
    │       └── MediaStoreDataSource.kt
    └── di/                      # Dependency Injection
        ├── DatabaseModule.kt
        ├── RepositoryModule.kt
        └── UseCaseModule.kt
```

### Feature-Based Benefits
- **Scalability**: Each feature is self-contained and can be developed independently
- **Maintainability**: Related code is grouped together, making it easier to find and modify
- **Team Collaboration**: Different developers can work on different features without conflicts
- **Testing**: Feature-specific tests are organized alongside the feature code
- **Code Reuse**: Common components are clearly separated and reusable across features

## 🚨 Zero Tolerance Enforcement

### Pre-commit Checks
```bash
# Check for force unwrapping
grep -r "!!" app/src/main/java/ && echo "❌ FOUND !!" || echo "✅ NO !!"

# Check for when expressions (should be minimal)
grep -r "when\s*(" app/src/main/java/ | wc -l

# Check for hardcoded analytics strings
grep -r "AmplitudeHelper\." app/src/main/java/ | grep -v "AnalyticsConstants"

# Check Timber logging format
grep -r "Timber\." app/src/main/java/ | grep -v "DEBUG_FLOW"
```

### Code Review Checklist
- [ ] No `!!` operators used
- [ ] When expressions replaced with sealed classes
- [ ] All strings use `stringResource()` or constants
- [ ] Analytics use `AnalyticsConstants.kt`
- [ ] All ViewModels use `@HiltViewModel`
- [ ] Proper `DEBUG_FLOW` Timber logging
- [ ] Clean Architecture layer separation maintained
- [ ] All tests pass (Unit + UI + Integration)

## 🧪 Testing Requirements

**MANDATORY**: All three test types must pass:
- **Unit Tests**: ViewModels, use cases, business logic
- **UI Tests**: Compose components and screens
- **Integration Tests**: Complete user flows end-to-end

## 🔧 Key Technologies

- Jetpack Compose (Material3)
- Hilt (DI)
- Clean Architecture
- MVVM Pattern
- Sealed Classes (No When Expressions)
- Timber Logging

## 🔴 Sealed Classes Examples (No When Expressions)

### Navigation Actions
```kotlin
// ❌ WRONG: when expressions
when (fragmentIndex) {
    INDEX_CUTTER -> openCutter()
    INDEX_SPEED -> openSpeed()
}

// ✅ CORRECT: sealed classes with polymorphic dispatch
sealed class NavigationAction {
    abstract fun execute(context: Context)

    object Cutter : NavigationAction() {
        override fun execute(context: Context) {
            // Open cutter functionality
        }
    }

    object Speed : NavigationAction() {
        override fun execute(context: Context) {
            // Open speed functionality
        }
    }
}

// Usage
navigationAction.execute(context)
```

### Processing Results
```kotlin
// ❌ WRONG: when for result handling
when (result) {
    is Result.Success -> showSuccess(result.data)
    is Result.Error -> showError(result.exception)
}

// ✅ CORRECT: sealed classes with polymorphic dispatch
sealed class ProcessingResult<T> {
    abstract fun handle(
        onSuccess: (T) -> Unit,
        onError: (Exception) -> Unit
    )

    data class Success<T>(val data: T) : ProcessingResult<T>() {
        override fun handle(
            onSuccess: (T) -> Unit,
            onError: (Exception) -> Unit
        ) {
            onSuccess(data)
        }
    }

    data class Error<T>(val exception: Exception) : ProcessingResult<T>() {
        override fun handle(
            onSuccess: (T) -> Unit,
            onError: (Exception) -> Unit
        ) {
            onError(exception)
        }
    }
}

// Usage
result.handle(
    onSuccess = { data -> showSuccess(data) },
    onError = { exception -> showError(exception) }
)
```

### Fragment Creation
```kotlin
// ❌ WRONG: when for fragment creation
val fragment = when (pendingActionForVideoPicker) {
    INDEX_CUTTER -> CutterFragment.newInstance(bundle)
    INDEX_SPEED -> SpeedFragment.newInstance(bundle)
    else -> null
}

// ✅ CORRECT: sealed classes with polymorphic dispatch
sealed class FragmentDestination {
    abstract fun createFragment(bundle: Bundle): Fragment

    object Cutter : FragmentDestination() {
        override fun createFragment(bundle: Bundle): Fragment {
            return CutterFragment.newInstance(bundle)
        }
    }

    object Speed : FragmentDestination() {
        override fun createFragment(bundle: Bundle): Fragment {
            return SpeedFragment.newInstance(bundle)
        }
    }
}

// Usage
val fragment = destination.createFragment(bundle)
```

## 📝 Current State Analysis

### Issues Found in Existing Code
1. ❌ **No Hilt DI**: Currently using manual dependency management in MyApplication
2. ❌ **No Clean Architecture**: Mixed concerns in fragments and activities
3. ❌ **Package Structure**: Not feature-based, mixed layers
4. ❌ **Force Unwrapping**: Found `!!` operators in existing code
5. ❌ **Hardcoded Strings**: Analytics strings not in constants
6. ❌ **When Expressions**: Multiple when expressions need replacement

### Current Package Structure
```
com.videomontage.freeeditingapps/
├── activity/                    # Activities (to be migrated)
├── fragment/                    # Fragments (to be migrated)
├── adapter/                     # RecyclerView adapters (to be replaced)
├── model/                       # Data models (to be moved to domain)
├── utils/                       # Utilities (to be reorganized)
├── analytics/                   # Analytics (needs constants extraction)
└── application/                 # Application class (needs Hilt setup)
```

## 🎯 Migration Success Criteria

### Phase Completion Criteria
- [ ] **Phase 1**: All dependencies added, Hilt configured, package structure created
- [ ] **Phase 2**: Domain layer complete with use cases and repository interfaces
- [ ] **Phase 3**: All fragments migrated to Compose screens with sealed states
- [ ] **Phase 4**: Complex UI components migrated with proper state management
- [ ] **Phase 5**: All tests passing (Unit + UI + Integration)

### Code Quality Gates
- [ ] Zero `!!` operators in new code
- [ ] Zero when expressions in new code
- [ ] All ViewModels use `@HiltViewModel`
- [ ] All strings use `stringResource()` or constants
- [ ] All analytics use `AnalyticsConstants.kt`
- [ ] All logging uses `DEBUG_FLOW` format
- [ ] Clean Architecture layers properly separated

---

**Remember**: This is a ZERO TOLERANCE migration. Every single when expression must be replaced with sealed classes using polymorphic dispatch. No exceptions.
